package rcmd

type WorkloadChart struct {
	Name                        string              `json:"name"`                      // 名称
	Fullname                    string              `json:"fullname"`                  // 全称
	NameSpace                   string              `json:"nameSpace"`                 // 命名空间
	Env                         []Env               `json:"env"`                       // 环境变量
	Deploy                      Deploy              `json:"deploy"`                    // 部署选项
	Labels                      map[string]string   `json:"labels"`                    // 标签
	Command                     []string            `json:"command"`                   // 命令参数
	Service                     Service             `json:"service"`                   // 服务协议和端口
	Volumes                     Volumes             `json:"volumes"`                   // volumes
	ExtraPvc                    []ExtraPvc          `json:"extraPvc"`                  // 额外的PVC
	Kubevela                    Kubevela            `json:"kubevela"`                  // kubevela标签
	Replicas                    string              `json:"replicas"`                  // 副本数
	Container                   Container           `json:"container"`                 // 容器配置
	Resources                   Resources           `json:"resources"`                 // 资源配置
	Prometheus                  Prometheus          `json:"prometheus"`                // 启用监控指标采集
	FlowControl                 FlowControl         `json:"flowControl"`               // 启用发布冲突检查
	StatefulSet                 bool                `json:"statefulSet"`               // 是否为sts
	StsRollingUpdatePartition   int32               `json:"stsRollingUpdatePartition"` // sts滚动更新分区
	AppConfigMap                map[string]string   `json:"appConfigMap"`              // 配置文件
	VolumeMounts                VolumeMounts        `json:"volumeMounts"`              // 文件挂载
	LivenessProbe               bool                `json:"livenessProbe"`             // 是否启动存活探测
	LivenessPeriodSeconds       string              `json:"livenessPeriodSeconds"`     // 存活探测时间间隔
	LivenessTimeout             string              `json:"livenessTimeout"`
	LivenessInitialDelaySeconds string              `json:"livenessInitialDelaySeconds"` // 存活指针初始时间间隔
	LivenessFailureThreshold    string              `json:"livenessFailureThreshold"`    // 存活探测失败阈值
	LivenessSuccessThreshold    string              `json:"livenessSuccessThreshold"`    // 存活探测成功阈值
	LivenessProbeMethod         ProbeMethod         `json:"livenessProbeMethod"`         // 存活探测方法
	ReadnessProbe               bool                `json:"readnessProbe"`               // 是否启动就绪探测
	ReadnessInitialDelaySeconds string              `json:"readnessInitialDelaySeconds"` // 就绪探测初始时间间隔
	ReadnessPeriodSeconds       string              `json:"readnessPeriodSeconds"`       // 就绪探测时间间隔
	ReadnessFailureThreshold    string              `json:"readnessFailureThreshold"`    // 就绪探测失败阈值
	ReadnessProbeMethod         ProbeMethod         `json:"readnessProbeMethod"`         // 就绪探测方法
	Tolerations                 []Tolerations       `json:"tolerations"`
	ExtraContainer              []ExtraContainer    `json:"extraContainer"`              // 额外的容器
	MountManagerOBS             MountManagerOBS     `json:"mountManagerOBS"`             // 启用OBS挂载管理
	IstioNotInjection           bool                `json:"istioNotInjection"`           // 关闭注入
	CustomIstioSidecar          CustomIstioSidecar  `json:"customIstioSidecar"`          // 启用特殊注入行为
	IstioVirtualService         IstioVirtualService `json:"istioVirtualService"`         // istioVirtualService
	RollingUpdateMaxSurge       string              `json:"rollingUpdateMaxSurge"`       // 滚动更新最大并发
	RollingUpdateMaxUnavailable string              `json:"rollingUpdateMaxUnavailable"` // 滚动更新最大不可用
}
type FieldRef struct {
	FieldPath  string `json:"fieldPath"`
	APIVersion string `json:"apiVersion"`
}
type ValueFrom struct {
	FieldRef FieldRef `json:"fieldRef"`
}
type Env struct {
	Name      string    `json:"name"`
	Value     string    `json:"value"`
	ValueFrom ValueFrom `json:"valueFrom"`
}
type Image struct {
	Tag string `json:"tag"`
}
type Deploy struct {
	Image    Image  `json:"image"`
	Priority string `json:"priority"`
}
type PortList struct {
	Port     int64  `json:"port"`
	Protocol string `json:"protocol"`
}
type ExternalLB struct {
	Enabled bool   `json:"enabled"`
	ID      string `json:"id"`
	VpcId   string `json:"vpcId"`
	Ip      string `json:"ip"`
}
type Service struct {
	PortList   []PortList `json:"portList"`
	ExternalLB ExternalLB `json:"externalLB"`
}
type Volumes struct {
	Enabled bool            `json:"enabled"`
	Content []VolumeContent `json:"content"`
}
type EmptyDir struct {
}
type ConfigMap struct {
	Name        string `json:"name"`
	DefaultMode int32  `json:"defaultMode"`
}
type HostPath struct {
	Path string `json:"path"`
	Type string `json:"type"`
}
type Secret struct {
	SecretName  string `json:"secretName"`
	DefaultMode int    `json:"defaultMode"`
}
type PersistentVolumeClaim struct {
	ClaimName string `json:"claimName"`
}
type VolumeContent struct {
	Name                  string                `json:"name"`
	EmptyDir              EmptyDir              `json:"emptyDir"`
	ConfigMap             ConfigMap             `json:"configMap"`
	HostPath              HostPath              `json:"hostPath"`
	Secret                Secret                `json:"secret"`
	PersistentVolumeClaim PersistentVolumeClaim `json:"persistentVolumeClaim"`
}
type ExtraPvc struct {
	Name      string `json:"name"`
	MountPath string `json:"mountPath"`
}
type Kubevela struct {
	CompType string `json:"compType"`
}
type Exec struct {
	Command []string `json:"command"`
}
type HTTPGet struct {
	Path        string        `json:"path"`
	Port        int32         `json:"port"`
	Scheme      string        `json:"scheme"`
	HTTPHeaders []HTTPHeaders `json:"httpHeaders"`
	Host        string        `json:"host"`
}
type HTTPHeaders struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}
type TCPSocket struct {
	Port int32 `json:"port"`
}
type PreStop struct {
	Exec      Exec      `json:"exec"`
	HttpGet   HTTPGet   `json:"httpGet"`
	TCPSocket TCPSocket `json:"tcpSocket"`
}
type Lifecycle struct {
	Enabled bool    `json:"enabled"`
	PreStop PreStop `json:"preStop"`
}
type Container struct {
	Lifecycle Lifecycle `json:"lifecycle"`
}
type Limits struct {
	CPU    string `json:"cpu"`
	Memory string `json:"memory"`
}
type Requests struct {
	CPU    string `json:"cpu"`
	Memory string `json:"memory"`
}
type Resources struct {
	Limits   Limits   `json:"limits"`
	Requests Requests `json:"requests"`
}
type Prometheus struct {
	Path      string `json:"path"`
	Port      int32  `json:"port"`
	Enabled   bool   `json:"enabled"`
	Container string `json:"container"`
}
type FlowControl struct {
	Enable   bool   `json:"enable"`
	RepoName string `json:"repoName"`
}
type VolumeMounts struct {
	Enabled bool                 `json:"enabled"`
	Content []VolumeMountContent `json:"content"`
}
type VolumeMountContent struct {
	Name      string `json:"name"`
	SubPath   string `json:"subPath"`
	MountPath string `json:"mountPath"`
}
type ProbeMethod struct {
	Exec      Exec      `json:"exec"`
	TCPSocket TCPSocket `json:"tcpSocket"`
	HTTPGet   HTTPGet   `json:"httpGet"`
}
type Tolerations struct {
	Effect            string `json:"effect"`
	Key               string `json:"key"`
	Operator          string `json:"operator"`
	TolerationSeconds int    `json:"tolerationSeconds"`
}
type Ports struct {
	ContainerPort int `json:"containerPort"`
}
type MultiContainerVolumeMounts struct {
	Name      string `json:"name"`
	MountPath string `json:"mountPath"`
}
type LivenessProbe struct {
	TCPSocket           TCPSocket `json:"tcpSocket"`
	TimeoutSeconds      int32     `json:"timeoutSeconds"`
	InitialDelaySeconds int32     `json:"initialDelaySeconds"`
}
type ExtraContainer struct {
	Env             []Env                        `json:"env"`
	Name            string                       `json:"name"`
	Image           string                       `json:"image"`
	Ports           []Ports                      `json:"ports"`
	Command         []string                     `json:"command"`
	Resources       Resources                    `json:"resources"`
	VolumeMounts    []MultiContainerVolumeMounts `json:"volumeMounts"`
	LivenessProbe   LivenessProbe                `json:"livenessProbe"`
	ImagePullPolicy string                       `json:"imagePullPolicy"`
}
type MountManagerOBS struct {
	Enable bool   `json:"enable"`
	Path   string `json:"path"`
	Pvc    string `json:"pvc"`
}
type CustomIstioSidecar struct {
	Enabled          bool   `json:"enabled"`
	ProxyCPU         string `json:"proxyCPU"`
	ProxyCPULimit    string `json:"proxyCPULimit"`
	ProxyMemory      string `json:"proxyMemory"`
	ProxyMemoryLimit string `json:"proxyMemoryLimit"`
	InterceptionMode string `json:"interceptionMode"`
}
type IstioVirtualService struct {
	Enabled bool `json:"enabled"`
}

type HpaChart struct {
	Autoscaling   Autoscaling `json:"autoscaling"`
	IsStatefulSet bool        `json:"isStatefulSet"`
}
type Autoscaling struct {
	MaxReplicas                       int32 `json:"maxReplicas"`
	MinReplicas                       int32 `json:"minReplicas"`
	TargetCPUUtilizationPercentage    int32 `json:"targetCPUUtilizationPercentage"`
	TargetMemoryUtilizationPercentage int32 `json:"targetMemoryUtilizationPercentage"`
}
