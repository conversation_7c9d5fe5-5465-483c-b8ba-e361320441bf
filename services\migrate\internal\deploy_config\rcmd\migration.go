package rcmd

import (
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/protocol/deploy"
	"52tt.com/cicd/services/migrate/internal/deploy_config"
	"52tt.com/cicd/services/migrate/internal/deploy_config/common"
	db "52tt.com/cicd/services/migrate/pkg/database"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
)

// ExportZTTWAllConfig 导出中台T网所有配置
func ExportZTTWAllConfig(envs []string, services []string, targetProject string, isUpdate, isCover bool) error {
	for _, env := range envs {
		err := ExportZTTWConfig(env, services, targetProject, isUpdate, isCover)
		if err != nil {
			return err
		}
	}
	return nil
}

func ExportZTTWConfig(env string, services []string, targetProjectName string, isUpdate, isCover bool) error {
	k8sMap := map[string][]int{
		db.Dev:  {42},
		db.Test: {130, 144, 145, 146},
		db.Prod: {116, 117, 182},
	}
	deployConfigs, err := deploy_config.GetProjectDeployConfig(env, k8sMap)
	if err != nil {
		return err
	}
	var configs []deploy_config.UnitDeployConfig
	for _, dc := range deployConfigs {
		if dc.Unit.App.Team.Name != "中台-T网" || dc.Unit.Status != "online" {
			continue
		}
		// exportServices
		if !isContainUnit(dc.Unit.Name, services) && len(services) > 0 {
			continue
		}
		configs = append(configs, deploy_config.UnitDeployConfig{
			Resource:       dc.Resource,
			Values:         dc.Values,
			Default:        dc.Default,
			K8sName:        dc.K8sEnv.AssetsK8sCluster.Name,
			NameSpace:      dc.K8sEnv.NameSpace,
			K8sDescription: dc.K8sEnv.AssetsK8sCluster.Description,
			K8sPrivateId:   dc.K8sEnv.AssetsK8sCluster.PrivateId,
			UnitName:       dc.Unit.Name,
			ObjectId:       dc.Unit.ObjectId,
			TeamName:       dc.Unit.App.Team.Name,
			ChartName:      dc.Chart.Name,
			ChartUrl:       dc.Chart.Url,
			ChartVersion:   dc.Chart.Version,
			ChartType:      dc.Chart.CharType,
			UnitId:         dc.UnitId,
			K8sEnvId:       dc.K8sEnvId,
		})
	}
	if len(configs) == 0 {
		return nil
	}
	//err = deploy_config.CreateConfig(configs)
	err = PullConfig(configs, targetProjectName, isUpdate, isCover)
	if err != nil {
		return err
	}

	return nil
}

// ExportZTYPSFAllConfig 导出中台音频算法组所有配置
func ExportZTYPSFAllConfig(envs []string, services []string, targetProject string, isUpdate, isCover bool) error {
	for _, env := range envs {
		err := ExportYPSFConfig(env, services, targetProject, isUpdate, isCover)
		if err != nil {
			return err
		}
	}
	return nil
}

func ExportYPSFConfig(env string, services []string, targetProjectName string, isUpdate, isCover bool) error {
	k8sMap := map[string][]int{
		db.Dev:  {50},
		db.Test: {114, 119, 134, 141, 142, 143},
		db.Prod: {41, 42, 74, 75, 181, 184, 195},
	}
	deployConfigs, err := deploy_config.GetProjectDeployConfig(env, k8sMap)
	if err != nil {
		return err
	}
	var configs []deploy_config.UnitDeployConfig
	for _, dc := range deployConfigs {
		if dc.Unit.App.Team.Name != "中台线-音频算法组" || dc.Unit.Status != "online" {
			continue
		}
		// exportServices
		if !isContainUnit(dc.Unit.Name, services) && len(services) > 0 {
			continue
		}
		configs = append(configs, deploy_config.UnitDeployConfig{
			Resource:       dc.Resource,
			Values:         dc.Values,
			Default:        dc.Default,
			K8sName:        dc.K8sEnv.AssetsK8sCluster.Name,
			NameSpace:      dc.K8sEnv.NameSpace,
			K8sDescription: dc.K8sEnv.AssetsK8sCluster.Description,
			K8sPrivateId:   dc.K8sEnv.AssetsK8sCluster.PrivateId,
			UnitName:       dc.Unit.Name,
			ObjectId:       dc.Unit.ObjectId,
			TeamName:       dc.Unit.App.Team.Name,
			ChartName:      dc.Chart.Name,
			ChartUrl:       dc.Chart.Url,
			ChartVersion:   dc.Chart.Version,
			ChartType:      dc.Chart.CharType,
			UnitId:         dc.UnitId,
			K8sEnvId:       dc.K8sEnvId,
		})
	}
	if len(configs) == 0 {
		return nil
	}
	//err = deploy_config.CreateConfig(configs)
	err = PullConfig(configs, targetProjectName, isUpdate, isCover)
	if err != nil {
		return err
	}

	return nil
}

// ExportZTTJAllConfig 导出中台推荐组所有配置
func ExportZTTJAllConfig(envs []string, services []string, targetProject string, isUpdate, isCover bool) error {
	for _, env := range envs {
		err := ExportZTTJConfig(env, services, targetProject, isUpdate, isCover)
		if err != nil {
			return err
		}
	}
	return nil
}

func ExportZTTJConfig(env string, services []string, targetProjectName string, isUpdate, isCover bool) error {
	k8sMap := map[string][]int{
		db.Dev:  {},
		db.Test: {129, 132, 158, 159, 160, 161, 162},
		db.Prod: {122, 123, 124, 152, 186, 193, 197},
	}
	deployConfigs, err := deploy_config.GetProjectDeployConfig(env, k8sMap)
	if err != nil {
		return err
	}
	var configs []deploy_config.UnitDeployConfig
	for _, dc := range deployConfigs {
		if dc.Unit.App.Team.Name != "中台-推荐" || dc.Unit.Status != "online" {
			continue
		}
		// exportServices
		if !isContainUnit(dc.Unit.Name, services) && len(services) > 0 {
			continue
		}
		configs = append(configs, deploy_config.UnitDeployConfig{
			Resource:       dc.Resource,
			Values:         dc.Values,
			Default:        dc.Default,
			K8sName:        dc.K8sEnv.AssetsK8sCluster.Name,
			NameSpace:      dc.K8sEnv.NameSpace,
			K8sDescription: dc.K8sEnv.AssetsK8sCluster.Description,
			K8sPrivateId:   dc.K8sEnv.AssetsK8sCluster.PrivateId,
			UnitName:       dc.Unit.Name,
			ObjectId:       dc.Unit.ObjectId,
			TeamName:       dc.Unit.App.Team.Name,
			ChartName:      dc.Chart.Name,
			ChartUrl:       dc.Chart.Url,
			ChartVersion:   dc.Chart.Version,
			ChartType:      dc.Chart.CharType,
			UnitId:         dc.UnitId,
			K8sEnvId:       dc.K8sEnvId,
		})
	}
	if len(configs) == 0 {
		return nil
	}
	//err = deploy_config.CreateConfig(configs)
	err = PullConfig(configs, targetProjectName, isUpdate, isCover)
	if err != nil {
		return err
	}

	return nil
}

// ExportZTSFAllConfig 导出中台算法组所有配置
func ExportZTSFAllConfig(envs []string, services []string, targetProject string, isUpdate, isCover bool) error {
	for _, env := range envs {
		err := ExportZTSFConfig(env, services, targetProject, isUpdate, isCover)
		if err != nil {
			return err
		}
	}
	return nil
}

func ExportZTSFConfig(env string, services []string, targetProjectName string, isUpdate, isCover bool) error {
	k8sMap := map[string][]int{
		db.Dev:  {},
		db.Test: {154, 155, 156, 157},
		db.Prod: {16, 17, 28, 29, 30, 31, 38, 120, 194, 196},
	}
	deployConfigs, err := deploy_config.GetProjectDeployConfig(env, k8sMap)
	if err != nil {
		return err
	}
	var configs []deploy_config.UnitDeployConfig
	for _, dc := range deployConfigs {
		if dc.Unit.App.Team.Name != "中台-算法" || dc.Unit.Status != "online" {
			continue
		}
		// exportServices
		if !isContainUnit(dc.Unit.Name, services) && len(services) > 0 {
			continue
		}
		configs = append(configs, deploy_config.UnitDeployConfig{
			Resource:       dc.Resource,
			Values:         dc.Values,
			Default:        dc.Default,
			K8sName:        dc.K8sEnv.AssetsK8sCluster.Name,
			NameSpace:      dc.K8sEnv.NameSpace,
			K8sDescription: dc.K8sEnv.AssetsK8sCluster.Description,
			K8sPrivateId:   dc.K8sEnv.AssetsK8sCluster.PrivateId,
			UnitName:       dc.Unit.Name,
			ObjectId:       dc.Unit.ObjectId,
			TeamName:       dc.Unit.App.Team.Name,
			ChartName:      dc.Chart.Name,
			ChartUrl:       dc.Chart.Url,
			ChartVersion:   dc.Chart.Version,
			ChartType:      dc.Chart.CharType,
			UnitId:         dc.UnitId,
			K8sEnvId:       dc.K8sEnvId,
		})
	}
	if len(configs) == 0 {
		return nil
	}
	//err = deploy_config.CreateConfig(configs)
	err = PullConfig(configs, targetProjectName, isUpdate, isCover)
	if err != nil {
		return err
	}

	return nil
}

func PullConfig(configs []deploy_config.UnitDeployConfig, targetProjectName string, isUpdate, isCover bool) error {
	dbEnv := db.Prod // 同步环境
	//dbEnv := db.Test

	hpaMap := make(map[string]HpaChart)
	for _, conf := range configs {
		var tempHpa HpaChart
		if conf.Resource == "HPA" {
			json.Unmarshal(conf.Values, &tempHpa)
			log.Debugf("unit name: %v", conf.UnitName)
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			hpaMap[unitKey] = tempHpa
		}
	}
	log.Debugf("hpaMap: %v", hpaMap)

	count := 0
	var successApps []string
	for _, conf := range configs {
		if conf.ChartName == "rcmd-server" {
			var err error
			// 处理数据导入转换逻辑
			log.Debugf("unitId: %v, unitName: %v", conf.UnitId, conf.UnitName)
			var valueConfig WorkloadChart
			_ = json.Unmarshal(conf.Default, &valueConfig)
			_ = json.Unmarshal(conf.Values, &valueConfig)

			appBaseConfig, err := HandAppBaseConfig(valueConfig)
			if err != nil {
				return err
			}
			appAdvancedConfig, err := HandAppAdvancedConfig(valueConfig, conf.UnitName)
			if err != nil {
				return err
			}
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			traitConfig, err := HandTraitConfig(valueConfig, unitKey, hpaMap)
			if err != nil {
				return err
			}

			// 数据库操作：查询app、查询/创建deploy_metadata、创建deploy_config、更新app
			// 1.查询app
			newTeamName := conf.TeamName
			if targetProjectName != "" {
				newTeamName = targetProjectName
			}
			appId, err := common.FindAppIdBy(dbEnv, newTeamName, conf.UnitName)
			if err != nil {
				return err
			}
			if appId == 0 {
				log.Debugf("app name: %v is not exist", conf.UnitName)
				continue
			}

			// 2.查询/创建deploy_metadata
			metadata := deploy_config.DeployMetadata{
				Env:       getEnvEnum(conf.K8sName),
				EnvTarget: 1, //默认基准环境
				Cluster:   getClusterName(conf.K8sName),
				Namespace: conf.NameSpace,
				AppID:     appId,
				AppName:   conf.UnitName,
				ConfigID:  0,
			}
			err = deploy_config.GetDeployConfigLastVersion(dbEnv, &metadata)
			if err != nil {
				return err
			}
			if !isCover && metadata.Config != nil && metadata.Config.CreatedBy != 71 {
				log.Infof("has existed config, unitId: %v, unitName: %v, createdBy: %v", conf.UnitId, conf.UnitName, metadata.Config.CreatedByChineseName)
			}
			// 只验证数据转换是否可行，不更新
			if !isUpdate {
				log.Infof("ignore update, unitId: %v, unitName: %v", conf.UnitId, conf.UnitName)
				continue
			}
			version := 1
			if metadata.ID != 0 {
				if metadata.Config != nil {
					version = metadata.Config.Version + 1
					//continue
				}
				// 只更新指定用户的数据
				if !isCover && metadata.Config != nil && metadata.Config.CreatedBy != 71 {
					log.Infof("has existed config ignore update, unitId: %v, unitName: %v, createdBy: %v", conf.UnitId, conf.UnitName, metadata.Config.CreatedByChineseName)
					continue
				}
			} else {
				err = deploy_config.CreateDeployMetadata(dbEnv, &metadata)
				if err != nil {
					return err
				}
			}

			// 3.创建deploy_config
			deployConfig := deploy_config.DeployConfig{
				AppBasicConfig:       appBaseConfig,
				AppAdvancedConfig:    appAdvancedConfig,
				TraitConfig:          traitConfig,
				Version:              version,
				CreatedBy:            71, //创建用户id
				CreatedByChineseName: "陈伟良",
				CreatedByEmployeeNo:  "T2517",
				TemplateID:           0,
				MetadataID:           metadata.ID,
			}
			deployConfig.ConfigType = 1
			if valueConfig.StatefulSet {
				deployConfig.ConfigType = 3
			}
			err = deploy_config.CreateDeployConfig(dbEnv, &deployConfig)
			if err != nil {
				return err
			}
			metadata.ConfigID = deployConfig.ID
			err = deploy_config.UpdateDeployMetadata(dbEnv, &metadata)
			if err != nil {
				return err
			}

			// 4.更新matchLabels,先不刷
			//matchLabels := make(map[string]string)
			//serviceLabels := make(map[string]string)
			//// deploy标签
			//matchLabels["app"] = conf.UnitName
			//matchLabels["app.kubernetes.io/name"] = conf.UnitName
			//matchLabels["app.kubernetes.io/instance"] = conf.UnitName
			//if valueConfig.Kubevela.CompType != "" {
			//	// kubevela 迁移过来的服务多两个matchLabels
			//	matchLabels["app.oam.dev/component"] = conf.UnitName
			//	matchLabels["workload.oam.dev/type"] = valueConfig.Kubevela.CompType
			//}
			//// 服务标签
			//serviceLabels["app.kubernetes.io/name"] = conf.UnitName
			//serviceLabels["app.kubernetes.io/instance"] = conf.UnitName
			//params := app.UpdateLabelsParameter{
			//	AppId:         int(appId),
			//	MatchLabels:   matchLabels,
			//	ServiceLabels: serviceLabels,
			//}
			//err = app.UpgradeAppLabels(dbEnv, &params)
			if err != nil {
				return err
			}

			successApps = append(successApps, conf.UnitName)
			count++
		}
	}
	log.Infof("rcmd-server-finish:%d", count)
	log.Infof("rcmd-server-finish-list: %+v", successApps)
	return nil
}

func HandAppBaseConfig(config WorkloadChart) ([]byte, error) {
	res := deploy.AppBasicConfig{
		NetworkType:  "",
		NetworkPorts: []*deploy.AppBasicConfig_Port{},
		Annotations:  []*deploy.Pair{},
		Envs:         []*deploy.Pair{},
		Commands:     []string{},
		Configs:      []*deploy.AppBasicConfig_Config{},
	}
	// 网络配置-服务协议和端口
	if len(config.Service.PortList) > 0 {
		if config.Service.ExternalLB.Enabled {
			res.NetworkType = "LoadBalancer"
		} else {
			res.NetworkType = "ClusterIP"
		}
		count := 1
		for _, port := range config.Service.PortList {
			serviceName := fmt.Sprintf("%v-%v", port.Protocol, port.Port)
			if len(serviceName) >= 15 {
				serviceName = fmt.Sprintf("service-%d", count)
			}
			res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
				Name:         serviceName,
				InternalPort: port.Port,
				ExternalPort: port.Port,
			})
		}
		// prometheus监控
		if config.Prometheus.Enabled {
			log.Infof("prometheus enabled")
			res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
				Name:         "http-metrics",
				InternalPort: int64(config.Prometheus.Port),
				ExternalPort: int64(config.Prometheus.Port),
			})
		}
		if config.Service.ExternalLB.Enabled {
			res.Annotations = append(res.Annotations, &deploy.Pair{Key: "kubernetes.io/elb.class", Value: "union"})
			res.Annotations = append(res.Annotations, &deploy.Pair{Key: "kubernetes.io/elb.lb-algorithm", Value: "LEAST_CONNECTIONS"})
			res.Annotations = append(res.Annotations, &deploy.Pair{Key: "kubernetes.io/elb.id", Value: config.Service.ExternalLB.ID})
			res.Annotations = append(res.Annotations, &deploy.Pair{Key: "kubernetes.io/elb.vpc.id", Value: config.Service.ExternalLB.VpcId})
		}
	}
	// 环境变量
	if !config.StatefulSet {
		res.Envs = append(res.Envs, &deploy.Pair{Key: "TZ", Value: "Asia/Shanghai"})
	}
	for _, env := range config.Env {
		if env.Name != "" && env.Value != "" {
			res.Envs = append(res.Envs, &deploy.Pair{Key: env.Name, Value: env.Value})
		}
	}
	// 命令
	for _, command := range config.Command {
		res.Commands = append(res.Commands, command)
	}
	// 配置文件
	for fileName, fileContent := range config.AppConfigMap {
		res.Configs = append(res.Configs, &deploy.AppBasicConfig_Config{DirPath: "/app/config", FileName: fileName, Content: fileContent})
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func builderHandler() *deploy.HealthCheck_HealthCheckHandler {
	return &deploy.HealthCheck_HealthCheckHandler{
		Type: "",
		Exec: &deploy.ExecAction{
			Command: []string{},
		},
		TcpSocket: &deploy.TCPSocketAction{},
		HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
		Grpc:      &deploy.GRPCAction{},
		Probe: &deploy.HealthCheck_Probe{
			InitialDelaySeconds: 0,
			PeriodSeconds:       10,
			TimeoutSeconds:      1,
			SuccessThreshold:    1,
			FailureThreshold:    3,
		},
	}
}

func HandAppAdvancedConfig(config WorkloadChart, unitName string) ([]byte, error) {
	res := deploy.AppAdvancedConfig{
		Labels:      []*deploy.Pair{},
		Annotations: []*deploy.Pair{},
		HostAliases: []*deploy.HostAlias{},
		HealthCheck: &deploy.HealthCheck{
			ReadinessProbe: builderHandler(),
			LivenessProbe:  builderHandler(),
			StartupProbe:   builderHandler(),
			Types:          []string{},
		},
		ServiceAccountName: "default",
		Lifecycle: &deploy.Lifecycle{
			PostStart: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			PreStop: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			Types: []string{},
		},
		MountConfig: &deploy.MountConfig{
			Volumes:      []*deploy.Volume{},
			VolumeMounts: []*deploy.VolumeMount{},
		},
	}

	expectedLabel := map[string]bool{
		"uuid":       true,
		"cluster_id": true,
		"env":        true,
		"lang":       true,
		"type":       true,
	}
	// label 标签
	for key, value := range config.Labels {
		if _, ok := expectedLabel[key]; ok {
			continue
		}
		res.Labels = append(res.Labels, &deploy.Pair{Key: key, Value: value})
	}
	// 注解
	if config.FlowControl.Enable {
		res.Annotations = append(res.Annotations, &deploy.Pair{Key: "mpe/flow-control-repo", Value: config.FlowControl.RepoName})
	}
	// 主机别名
	// 就绪探针
	if config.ReadnessProbe {
		res.HealthCheck.Types = append(res.HealthCheck.Types, "readinessProbe")
		if len(config.ReadnessProbeMethod.Exec.Command) > 0 {
			res.HealthCheck.ReadinessProbe.Type = "exec"
			res.HealthCheck.ReadinessProbe.Exec = &deploy.ExecAction{}
			for _, cmd := range config.ReadnessProbeMethod.Exec.Command {
				res.HealthCheck.ReadinessProbe.Exec.Command = append(res.HealthCheck.ReadinessProbe.Exec.Command, cmd)
			}
		} else if config.ReadnessProbeMethod.HTTPGet.Port > 0 {
			res.HealthCheck.ReadinessProbe.Type = "httpGet"
			res.HealthCheck.ReadinessProbe.HttpGet = &deploy.HTTPGetAction{
				Path: config.ReadnessProbeMethod.HTTPGet.Path,
				Port: config.ReadnessProbeMethod.HTTPGet.Port,
			}
			for _, header := range config.ReadnessProbeMethod.HTTPGet.HTTPHeaders {
				res.HealthCheck.ReadinessProbe.HttpGet.Headers = append(res.HealthCheck.ReadinessProbe.HttpGet.Headers,
					&deploy.Pair{Key: header.Name, Value: header.Value})
			}
		} else {
			commands := []string{"grpc_health_probe", "-addr=127.0.0.1:8000", "-rpc-timeout=2s"}
			res.HealthCheck.ReadinessProbe.Exec.Command = append(res.HealthCheck.ReadinessProbe.Exec.Command, commands...)
		}

		readnessPeriodSeconds, _ := strconv.Atoi(config.ReadnessPeriodSeconds)
		if readnessPeriodSeconds != 0 {
			res.HealthCheck.ReadinessProbe.Probe.PeriodSeconds = int32(readnessPeriodSeconds)
		} else {
			res.HealthCheck.ReadinessProbe.Probe.PeriodSeconds = 10
		}
		readnessInitialDelaySeconds, _ := strconv.Atoi(config.ReadnessInitialDelaySeconds)
		if readnessInitialDelaySeconds != 0 {
			res.HealthCheck.ReadinessProbe.Probe.InitialDelaySeconds = int32(readnessInitialDelaySeconds)
		}
		res.HealthCheck.ReadinessProbe.Probe.TimeoutSeconds = 2
		res.HealthCheck.ReadinessProbe.Probe.SuccessThreshold = 1
		readnessFailureThreshold, _ := strconv.Atoi(config.ReadnessFailureThreshold)
		if readnessFailureThreshold != 0 {
			res.HealthCheck.ReadinessProbe.Probe.FailureThreshold = int32(readnessFailureThreshold)
		} else {
			res.HealthCheck.ReadinessProbe.Probe.FailureThreshold = 3
		}
	}
	// 存活探针
	if config.LivenessProbe {
		res.HealthCheck.Types = append(res.HealthCheck.Types, "livenessProbe")
		if len(config.LivenessProbeMethod.Exec.Command) > 0 {
			res.HealthCheck.LivenessProbe.Type = "exec"
			res.HealthCheck.LivenessProbe.Exec = &deploy.ExecAction{}
			for _, cmd := range config.LivenessProbeMethod.Exec.Command {
				res.HealthCheck.LivenessProbe.Exec.Command = append(res.HealthCheck.LivenessProbe.Exec.Command, cmd)
			}
		} else if config.LivenessProbeMethod.HTTPGet.Port > 0 {
			res.HealthCheck.LivenessProbe.Type = "httpGet"
			res.HealthCheck.LivenessProbe.HttpGet = &deploy.HTTPGetAction{
				Path: config.LivenessProbeMethod.HTTPGet.Path,
				Port: config.LivenessProbeMethod.HTTPGet.Port,
			}
			for _, header := range config.LivenessProbeMethod.HTTPGet.HTTPHeaders {
				res.HealthCheck.LivenessProbe.HttpGet.Headers = append(res.HealthCheck.LivenessProbe.HttpGet.Headers,
					&deploy.Pair{Key: header.Name, Value: header.Value})
			}
		} else {
			commands := []string{"grpc_health_probe", "-addr=127.0.0.1:8000", "-rpc-timeout=2s"}
			res.HealthCheck.LivenessProbe.Exec.Command = append(res.HealthCheck.LivenessProbe.Exec.Command, commands...)
		}

		livenessPeriodSeconds, _ := strconv.Atoi(config.LivenessPeriodSeconds)
		if livenessPeriodSeconds != 0 {
			res.HealthCheck.LivenessProbe.Probe.PeriodSeconds = int32(livenessPeriodSeconds)
		} else {
			res.HealthCheck.LivenessProbe.Probe.PeriodSeconds = 10
		}

		livenessInitialDelaySeconds, _ := strconv.Atoi(config.LivenessInitialDelaySeconds)
		if livenessInitialDelaySeconds != 0 {
			res.HealthCheck.LivenessProbe.Probe.InitialDelaySeconds = int32(livenessInitialDelaySeconds)
		}

		timeOut, _ := strconv.Atoi(config.LivenessTimeout[0 : len(config.LivenessTimeout)-1])
		if timeOut != 0 {
			res.HealthCheck.LivenessProbe.Probe.TimeoutSeconds = int32(timeOut)
		} else {
			res.HealthCheck.LivenessProbe.Probe.TimeoutSeconds = 1
		}

		livenessSuccessThreshold, _ := strconv.Atoi(config.LivenessSuccessThreshold)
		if livenessSuccessThreshold != 0 {
			res.HealthCheck.LivenessProbe.Probe.SuccessThreshold = int32(livenessSuccessThreshold)
		} else {
			res.HealthCheck.LivenessProbe.Probe.SuccessThreshold = 1
		}

		livenessFailureThreshold, _ := strconv.Atoi(config.LivenessFailureThreshold)
		if livenessFailureThreshold != 0 {
			res.HealthCheck.LivenessProbe.Probe.FailureThreshold = int32(livenessFailureThreshold)
		} else {
			res.HealthCheck.LivenessProbe.Probe.FailureThreshold = 3
		}
	}
	// 挂载配置
	if !config.StatefulSet {
		// deployment 默认挂载
		// volume
		res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
			Type:       "configMap",
			VolumeName: "service-targets-config",
			RefName:    "service-targets-config",
		})
		res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
			Type:       "hostPath",
			VolumeName: "osspipe-no-del",
			RefName:    "/dev/null",
		})
		res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
			Type:       "hostPath",
			VolumeName: "oss-bak-pipe-no-del",
			RefName:    "/dev/null",
		})
		res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
			Type:       "hostPath",
			VolumeName: "host-time",
			ReadOnly:   true,
			RefName:    "/etc/localtime",
		})
		// volumeMount
		res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
			VolumeName: "service-targets-config",
			MountPoint: "/root/etc/client/",
		})
		res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
			VolumeName: "osspipe-no-del",
			MountPoint: "/home/<USER>/log/oss/osspipe_no_del",
		})
		res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
			VolumeName: "oss-bak-pipe-no-del",
			MountPoint: "/home/<USER>/log/oss/oss_bak_pipe_no_del",
		})
		res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
			VolumeName: "host-time",
			MountPoint: "/etc/localtime",
		})
		// 挂载model-manager-obs
		if config.MountManagerOBS.Enable && config.MountManagerOBS.Pvc != "" && config.MountManagerOBS.Path != "" {
			// volume
			res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
				Type:       "PVC",
				VolumeName: "obs-model-manager",
				RefName:    config.MountManagerOBS.Pvc,
			})
			// volumeMount
			res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
				VolumeName: "obs-model-manager",
				MountPoint: config.MountManagerOBS.Path,
			})
		}
	}
	// 额外pvc
	if len(config.ExtraPvc) > 0 {
		for _, pvc := range config.ExtraPvc {
			res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
				Type:       "PVC",
				VolumeName: pvc.Name,
				RefName:    pvc.Name,
			})
			res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
				VolumeName: pvc.Name,
				MountPoint: pvc.MountPath,
			})
		}
	}
	if config.Volumes.Enabled {
		for _, volume := range config.Volumes.Content {
			if volume.Name == "" {
				continue
			}
			if volume.ConfigMap.Name != "" {
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "configMap",
					VolumeName: volume.Name,
					RefName:    volume.ConfigMap.Name,
				})
			} else if volume.HostPath.Path != "" {
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "hostPath",
					VolumeName: volume.Name,
					ReadOnly:   true,
					RefName:    volume.HostPath.Path,
				})
			} else if volume.Secret.SecretName != "" {
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "secret",
					VolumeName: volume.Name,
					RefName:    volume.Secret.SecretName,
				})
			} else if volume.PersistentVolumeClaim.ClaimName != "" {
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "PVC",
					VolumeName: volume.Name,
					RefName:    volume.PersistentVolumeClaim.ClaimName,
				})
			} else if volume.Name != "" {
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "emptyDir",
					Medium:     "default",
					VolumeName: volume.Name,
				})
			}
		}
	}
	// multiContainer绑定Config
	if len(config.ExtraContainer) > 0 {
		log.Infof("[rcmd] unitName: %v, extraContainer existed", unitName)
		var isAdd bool
		for _, container := range config.ExtraContainer {
			for _, volume := range container.VolumeMounts {
				if volume.Name == "config" {
					isAdd = true
					res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
						Type:       "configMap",
						VolumeName: volume.Name,
						RefName:    unitName,
					}) // 挂载的是旧cicd的configMap
					break
				}
			}
			if isAdd {
				break
			}
		}
	}

	if config.VolumeMounts.Enabled {
		for _, volumeMount := range config.VolumeMounts.Content {
			if volumeMount.Name == "" || volumeMount.MountPath == "" {
				continue
			}
			res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
				VolumeName: volumeMount.Name,
				MountPoint: volumeMount.MountPath,
				SubPath:    volumeMount.SubPath,
			})
		}
	}

	// lifecycle
	if config.Container.Lifecycle.Enabled {
		isPreStop := true
		if len(config.Container.Lifecycle.PreStop.Exec.Command) > 0 {
			res.Lifecycle.PreStop.Type = "exec"
			res.Lifecycle.PreStop.Exec = &deploy.ExecAction{}
			for _, cmd := range config.Container.Lifecycle.PreStop.Exec.Command {
				res.Lifecycle.PreStop.Exec.Command = append(res.Lifecycle.PreStop.Exec.Command, cmd)
			}
		} else if config.Container.Lifecycle.PreStop.TCPSocket.Port > 0 {
			res.Lifecycle.PreStop.Type = "tcpSocket"
			res.Lifecycle.PreStop.TcpSocket = &deploy.TCPSocketAction{
				Port: config.Container.Lifecycle.PreStop.TCPSocket.Port,
			}
		} else if config.Container.Lifecycle.PreStop.HttpGet.Port > 0 {
			res.Lifecycle.PreStop.Type = "httpGet"
			res.Lifecycle.PreStop.HttpGet = &deploy.HTTPGetAction{
				Path:   config.Container.Lifecycle.PreStop.HttpGet.Path,
				Port:   config.Container.Lifecycle.PreStop.HttpGet.Port,
				Scheme: config.Container.Lifecycle.PreStop.HttpGet.Scheme,
				Host:   config.Container.Lifecycle.PreStop.HttpGet.Host,
			}
			for _, header := range config.Container.Lifecycle.PreStop.HttpGet.HTTPHeaders {
				res.Lifecycle.PreStop.HttpGet.Headers = append(res.Lifecycle.PreStop.HttpGet.Headers, &deploy.Pair{
					Key:   header.Name,
					Value: header.Value,
				})
			}
		} else {
			isPreStop = false
		}
		// 使用preStop
		if isPreStop {
			res.Lifecycle.Types = append(res.Lifecycle.Types, "preStop")
			log.Infof("unitName: %v, use preStop, lifecycle: %+v", unitName, res.Lifecycle)
		}
	}
	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

type resources struct {
	Requests struct {
		CPU    float32
		Memory float32
	}
	Limits struct {
		CPU    float32
		Memory float32
	}
}

func handleResource(resource Resources) resources {
	var res resources
	cpuReq := resource.Requests.CPU
	if strings.Contains(cpuReq, "m") || strings.Contains(cpuReq, "M") {
		//单位为m
		reqCpu, _ := strconv.ParseFloat(cpuReq[0:len(cpuReq)-1], 32)
		res.Requests.CPU = float32(reqCpu / 1000)
	} else {
		//单位为核
		reqCpu, _ := strconv.ParseFloat(cpuReq, 32)
		res.Requests.CPU = float32(reqCpu)
	}

	memReq, _ := strconv.ParseFloat(resource.Requests.Memory[0:len(resource.Requests.Memory)-2], 32)
	if strings.Contains(resource.Requests.Memory, "Gi") {
		res.Requests.Memory = float32(memReq * 1024)
	} else {
		res.Requests.Memory = float32(memReq)
	}

	cpuLim := resource.Limits.CPU
	if strings.Contains(cpuLim, "m") || strings.Contains(cpuLim, "M") {
		//单位为m
		limCpu, _ := strconv.ParseFloat(cpuLim[0:len(cpuLim)-1], 32)
		res.Limits.CPU = float32(limCpu / 1000)
	} else {
		//单位为核
		limCpu, _ := strconv.ParseFloat(cpuLim, 32)
		res.Limits.CPU = float32(limCpu)
	}

	limMem, _ := strconv.ParseFloat(resource.Limits.Memory[0:len(resource.Limits.Memory)-2], 32)
	if strings.Contains(resource.Limits.Memory, "Gi") {
		res.Limits.Memory = float32(limMem * 1024)
	} else {
		res.Limits.Memory = float32(limMem)
	}

	return res
}

func HandTraitConfig(config WorkloadChart, unitKey string, hpaMap map[string]HpaChart) ([]byte, error) {
	var res deploy.TraitConfig
	// 资源限制
	res.ResourceConstraints = &deploy.ResourceConstraints{Resources: &deploy.Resources{}, Sidecar: &deploy.ResourceConstraints_Sidecar{}}
	baseResource := handleResource(config.Resources)
	res.ResourceConstraints.Resources.RequestCpu = baseResource.Requests.CPU
	res.ResourceConstraints.Resources.RequestMemory = baseResource.Requests.Memory
	res.ResourceConstraints.Resources.LimitCpu = baseResource.Limits.CPU
	res.ResourceConstraints.Resources.LimitMemory = baseResource.Limits.Memory
	// sidecar注入
	if config.CustomIstioSidecar.Enabled && !config.IstioNotInjection {
		res.ResourceConstraints.Sidecar.Enabled = true
		cpuReq1 := config.CustomIstioSidecar.ProxyCPU
		if strings.Contains(cpuReq1, "m") || strings.Contains(cpuReq1, "M") {
			reqCpu, _ := strconv.ParseFloat(cpuReq1[0:len(cpuReq1)-1], 32)
			res.ResourceConstraints.Sidecar.RequestCpu = float32(reqCpu / 1000)
		} else {
			reqCpu, _ := strconv.ParseFloat(cpuReq1, 32)
			res.ResourceConstraints.Sidecar.RequestCpu = float32(reqCpu)
		}

		reqMem1 := config.CustomIstioSidecar.ProxyMemory
		memReq1, _ := strconv.ParseFloat(reqMem1[0:len(reqMem1)-2], 32)
		if strings.Contains(reqMem1, "Gi") {
			res.ResourceConstraints.Sidecar.RequestMemory = float32(memReq1 * 1024)
		} else {
			res.ResourceConstraints.Sidecar.RequestMemory = float32(memReq1)
		}

		cpuLim1 := config.CustomIstioSidecar.ProxyCPULimit
		if strings.Contains(cpuLim1, "m") || strings.Contains(cpuLim1, "M") {
			limCpu, _ := strconv.ParseFloat(cpuLim1[0:len(cpuLim1)-1], 32)
			res.ResourceConstraints.Sidecar.LimitCpu = float32(limCpu / 1000)
		} else {
			limCpu, _ := strconv.ParseFloat(cpuLim1, 32)
			res.ResourceConstraints.Sidecar.LimitCpu = float32(limCpu)
		}

		litMem1 := config.CustomIstioSidecar.ProxyMemoryLimit
		memLit1, _ := strconv.ParseFloat(litMem1[0:len(litMem1)-2], 32)
		if strings.Contains(litMem1, "Gi") {
			res.ResourceConstraints.Sidecar.LimitMemory = float32(memLit1 * 1024)
		} else {
			res.ResourceConstraints.Sidecar.LimitMemory = float32(memLit1)
		}
	}
	// 伸缩配置
	res.ScalingConfig = &deploy.ScalingConfig{
		Hpa:      &deploy.ScalingConfig_HPA{Types: []string{}},
		MultiHpa: &deploy.ScalingConfig_MultiHPA{Types: []string{}, Cron: []*deploy.ScalingConfig_MultiHPA_Cron{}},
	}
	replicas, _ := strconv.Atoi(config.Replicas)
	if replicas > 0 {
		res.ScalingConfig.Replicas = int32(replicas)
	} else {
		res.ScalingConfig.Replicas = 1
	}
	res.ScalingConfig.Type = "replicas"
	if value, isExist := hpaMap[unitKey]; isExist && (value.Autoscaling.TargetCPUUtilizationPercentage > 0 || value.Autoscaling.TargetMemoryUtilizationPercentage > 0) {
		res.ScalingConfig.MultiHpa = &deploy.ScalingConfig_MultiHPA{
			Min:   value.Autoscaling.MinReplicas,
			Max:   value.Autoscaling.MaxReplicas,
			Types: make([]string, 0),
		}
		if value.Autoscaling.TargetCPUUtilizationPercentage > 0 {
			res.ScalingConfig.MultiHpa.CpuUtilization = value.Autoscaling.TargetCPUUtilizationPercentage
			res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "cpu")
		}
		if value.Autoscaling.TargetCPUUtilizationPercentage > 0 {
			res.ScalingConfig.MultiHpa.MemoryUtilization = value.Autoscaling.TargetMemoryUtilizationPercentage
			res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "memory")
		}
		res.ScalingConfig.Type = "multiHPA"
	}
	// 监控指标
	if config.Prometheus.Enabled {
		res.MonitorMetrics = &deploy.MonitorMetrics{
			Enabled:       true,
			Port:          config.Prometheus.Port,
			MetricsPath:   config.Prometheus.Path,
			ContainerName: config.Prometheus.Container,
		}
	}
	// 升级策略
	res.UpgradeStrategy = &deploy.UpgradeStrategy{
		Enabled:        false,
		MaxSurge:       25,
		MaxUnavailable: 25,
	}
	maxUnavailable := config.RollingUpdateMaxUnavailable
	maxSurge := config.RollingUpdateMaxSurge
	if config.StatefulSet {
		res.UpgradeStrategy.Enabled = true
		res.UpgradeStrategy.Partition = config.StsRollingUpdatePartition
	} else {
		if maxUnavailable != "" || maxSurge != "" {
			res.UpgradeStrategy.Enabled = true
			if maxUnavailable != "" {
				maxUn, _ := strconv.ParseInt(maxUnavailable[0:len(maxUnavailable)-1], 10, 32)
				res.UpgradeStrategy.MaxUnavailable = int32(maxUn)
			}
			if maxSurge != "" {
				maxSu, _ := strconv.ParseInt(maxSurge[0:len(maxSurge)-1], 10, 32)
				res.UpgradeStrategy.MaxSurge = int32(maxSu)
			}
		}
	}
	// 高级配置-advancedConfig
	res.AdvancedConfig = &deploy.TraitAdvancedConfig{
		SchedulingStrategy: []*deploy.SchedulingStrategy{},
		RateLimiting:       &deploy.RateLimiting{},
		CircuitBreaker:     &deploy.CircuitBreaker{},
		InitContainers:     []*deploy.InitContainer{},
		MultiContainers:    []*deploy.MultiContainer{},
		CustomScheduler:    &deploy.CustomScheduler{},
		ServiceMonitor:     &deploy.ServiceMonitor{},
	}
	for _, c := range config.ExtraContainer {
		container := &deploy.MultiContainer{
			ImageName:     c.Image,
			ContainerName: c.Name,
			Commands:      []string{},
			Resources: &deploy.Resources{
				RequestCpu:    0.2,
				RequestMemory: 200,
				LimitCpu:      1,
				LimitMemory:   2048,
			},
			Envs:         []*deploy.Pair{},
			VolumeMounts: []*deploy.VolumeMount{},
			HealthCheck: &deploy.HealthCheck{
				ReadinessProbe: builderHandler(),
				LivenessProbe:  builderHandler(),
				StartupProbe:   builderHandler(),
				Types:          []string{},
			},
			GpuResources:      &deploy.GPUResource{},
			ResourceFieldEnvs: []*deploy.ResourceFieldEnv{},
		}
		for _, v := range c.Env {
			if v.Name != "" && v.Value != "" {
				container.Envs = append(container.Envs, &deploy.Pair{Key: v.Name, Value: v.Value})
				continue
			}
			if v.Name != "" && v.ValueFrom.FieldRef.FieldPath != "" {
				container.ResourceFieldEnvs = append(container.ResourceFieldEnvs, &deploy.ResourceFieldEnv{
					Key:      v.Name,
					Resource: v.ValueFrom.FieldRef.FieldPath,
					Divisor:  "",
				})
			}
		}
		for _, cmd := range c.Command {
			container.Commands = append(container.Commands, cmd)
		}
		for _, v := range c.VolumeMounts {
			container.VolumeMounts = append(container.VolumeMounts, &deploy.VolumeMount{
				VolumeName: v.Name,
				MountPoint: v.MountPath,
			})
		}
		if c.Resources.Requests.CPU == "" {
			c.Resources.Requests.CPU = "200m"
		}
		if c.Resources.Requests.Memory == "" {
			c.Resources.Requests.Memory = "200Mi"
		}
		if c.Resources.Limits.CPU == "" {
			c.Resources.Limits.CPU = "1"
		}
		if c.Resources.Limits.Memory == "" {
			c.Resources.Limits.Memory = "2048Mi"
		}
		resource := handleResource(c.Resources)
		container.Resources.RequestCpu = resource.Requests.CPU
		container.Resources.RequestMemory = resource.Requests.Memory
		container.Resources.LimitCpu = resource.Limits.CPU
		container.Resources.LimitMemory = resource.Limits.Memory
		if c.LivenessProbe.TCPSocket.Port > 0 {
			container.HealthCheck.Types = append(container.HealthCheck.Types, "livenessProbe")
			container.HealthCheck.LivenessProbe.Type = "tcpSocket"
			container.HealthCheck.LivenessProbe.TcpSocket = &deploy.TCPSocketAction{
				Port: c.LivenessProbe.TCPSocket.Port,
			}
			container.HealthCheck.LivenessProbe.Probe = &deploy.HealthCheck_Probe{
				InitialDelaySeconds: c.LivenessProbe.InitialDelaySeconds,
				PeriodSeconds:       10,
				TimeoutSeconds:      1,
				SuccessThreshold:    1,
				FailureThreshold:    3,
			}
			if c.LivenessProbe.TimeoutSeconds > 0 {
				container.HealthCheck.LivenessProbe.Probe.TimeoutSeconds = c.LivenessProbe.TimeoutSeconds
			}
		}
		res.AdvancedConfig.MultiContainers = append(res.AdvancedConfig.MultiContainers, container)
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func getClusterName(name string) string {
	switch name {
	case "火山云集群k8s-hs-bj-1-test":
		return "k8s-hs-bj-1-test"
	case "k8s-hw-bj-zt-prod-new":
		return "k8s-hw-bj-zt-prod"
	case "k8s-hw-bj-zt-stage":
		return "k8s-hw-bj-zt-stage-turbo"
	case "kubeconfig-k8s-hw-bj-1-prod":
		return "k8s-hw-bj-1-prod"
	default:
		return name
	}
}

func getEnvEnum(k8sName string) int8 {
	switch k8sName {
	case "k8s-hw-zt-dev":
		return int8(1)
	case "k8s-hw-bj-zt-test-turbo",
		"k8s-hw-bj-1-test",
		"k8s-tc-bj-1-test",
		"火山云集群k8s-hs-bj-1-test":
		return int8(2)
	case "k8s-hw-bj-zt-stage":
		return int8(3)
	default:
		return 4
	}
}

func isContainUnit(unit string, keys []string) bool {
	var myMap map[string]bool
	myMap = make(map[string]bool)
	for _, key := range keys {
		myMap[key] = true
	}
	if _, ok := myMap[unit]; ok {
		return true
	}
	return false
}
