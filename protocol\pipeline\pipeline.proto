syntax = "proto3";
package pipeline;
option go_package = "protocol/pipeline";

import "protocol/third-party/google/protobuf/struct.proto";
import "google/protobuf/empty.proto";

message Pipeline {
  int64 id = 1;
  int64 appId = 2;
}

message Task{
  int64 id = 1;
  bytes config = 2;
}

message PipelineArray{
  repeated PipelineResult pipelines = 1;
}

message PipelineResult{
  int64 id = 1;
  int64 templateId = 2;
  string name = 3;
  string type = 4;
}

message PipelineCountReq {
  repeated int64 appIds = 1;
}

message PipelineAppReq{
  int64 app_id = 1 [json_name = "appId"];
  string app_name = 2 [json_name = "appName"];
  string build_path = 3 [json_name = "buildPath"];;
  string repo_addr = 4 [json_name = "repoAddr"];
  string language = 5 [json_name = "language"];
  string language_version = 6 [json_name = "languageVersion"];
}

message PipelineCountResp {
  map<int64, int64> count_map = 1 [json_name = "countMap"];
}

message PipelineAppResp{
  int32 code = 1;
  string msg = 2;
}

message GetPipelineConfigReq {
  //@gotags: uri:"id"
  int64 id = 1;
}

message GetPipelineConfigResp {
  message Stage {
    int64 id = 1;
    string name = 2;
    repeated Task tasks = 3;
    string type = 4;
  }

  message Task {
    int64 id = 1;
    string name = 2;
    google.protobuf.Struct config = 3;
    string type = 4;
  }

  int64 id = 1;
  repeated Stage stages = 2;
}

message UpdatePipelineTaskConfigReq{
  //@gotags: json:"pipelineRunId"
  int64 pipelineRunId = 1;
  //@gotags: json:"taskId"
  int64 task_id = 2;
  //@gotags: json:"taskRunId"
  int64 task_run_id = 3;
  //@gotags: json:"updatedPipelineConfig"
  bytes updated_pipeline_config = 4;
  //@gotags: json:"updatedTaskRunConfig"
  bytes updated_task_run_config = 5;
}

message UpdatePipelineTaskMultiCloudConfigReq{
  //@gotags: json:"pipelineRunId"
  int64 pipelineRunId = 1;
  //@gotags: json:"stageRunId"
  int64 stage_run_id = 3;
  //@gotags: json:"updatedTaskRunMultiCloudConfig"
  bytes updated_task_run_multi_cloud_config = 5;
}

message PipelineAppsReq{
  repeated int64 app_ids = 1 [json_name = "appIds"];
  string repo_addr = 4 [json_name = "repoAddr"];
}

message RunPipelingReq {
  int64 app_id=1;
  string pipeline_name = 2;
  string branch = 3;
  int64  user_id=4;
  string chineseName = 5;
  string employeeNo = 6;
  string description = 7;
}


message PrepareingReq {}

message PrepareingResp {}

message DelPipelineReq {
  optional int64 app_id = 1;
  optional int64 prj_id = 2;
}

service PipelineService {
  rpc NewPipeline(Pipeline) returns (PipelineResult);
  rpc GetPipelineConfig(GetPipelineConfigReq) returns (GetPipelineConfigResp);
  rpc GetPipelineByAppId(Pipeline) returns (PipelineArray);
  rpc GetTaskById(Task) returns(Task);
  rpc UpdatePipelineAppMsg(PipelineAppReq) returns(PipelineAppResp);
  rpc UpdatePipelineTaskConfig(UpdatePipelineTaskConfigReq) returns(google.protobuf.Empty);
  rpc UpdatePipelineTaskMultiCloudConfig(UpdatePipelineTaskMultiCloudConfigReq) returns(google.protobuf.Empty);
  rpc GetPipelineCountByAppIds(PipelineCountReq) returns(PipelineCountResp);
  rpc BatchUpdatePipelineAppMsg(PipelineAppsReq) returns(PipelineAppResp);
  rpc HandlePrepareingPipeline(PrepareingReq) returns(PrepareingResp);
  rpc RunPipeline(RunPipelingReq) returns(google.protobuf.Empty);
  rpc DelPipeline(DelPipelineReq) returns(google.protobuf.Empty);
}
