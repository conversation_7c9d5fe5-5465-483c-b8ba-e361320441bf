// Code generated by MockGen. DO NOT EDIT.
// Source: ./protocol/pipeline/pipeline_grpc.pb.go

// Package pipeline is a generated GoMock package.
package pipeline

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockPipelineServiceClient is a mock of PipelineServiceClient interface.
type MockPipelineServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockPipelineServiceClientMockRecorder
}

// MockPipelineServiceClientMockRecorder is the mock recorder for MockPipelineServiceClient.
type MockPipelineServiceClientMockRecorder struct {
	mock *MockPipelineServiceClient
}

// NewMockPipelineServiceClient creates a new mock instance.
func NewMockPipelineServiceClient(ctrl *gomock.Controller) *MockPipelineServiceClient {
	mock := &MockPipelineServiceClient{ctrl: ctrl}
	mock.recorder = &MockPipelineServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPipelineServiceClient) EXPECT() *MockPipelineServiceClientMockRecorder {
	return m.recorder
}

// BatchUpdatePipelineAppMsg mocks base method.
func (m *MockPipelineServiceClient) BatchUpdatePipelineAppMsg(ctx context.Context, in *PipelineAppsReq, opts ...grpc.CallOption) (*PipelineAppResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchUpdatePipelineAppMsg", varargs...)
	ret0, _ := ret[0].(*PipelineAppResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdatePipelineAppMsg indicates an expected call of BatchUpdatePipelineAppMsg.
func (mr *MockPipelineServiceClientMockRecorder) BatchUpdatePipelineAppMsg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdatePipelineAppMsg", reflect.TypeOf((*MockPipelineServiceClient)(nil).BatchUpdatePipelineAppMsg), varargs...)
}

// DelPipeline mocks base method.
func (m *MockPipelineServiceClient) DelPipeline(ctx context.Context, in *DelPipelineReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPipeline", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPipeline indicates an expected call of DelPipeline.
func (mr *MockPipelineServiceClientMockRecorder) DelPipeline(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPipeline", reflect.TypeOf((*MockPipelineServiceClient)(nil).DelPipeline), varargs...)
}

// GetPipelineByAppId mocks base method.
func (m *MockPipelineServiceClient) GetPipelineByAppId(ctx context.Context, in *Pipeline, opts ...grpc.CallOption) (*PipelineArray, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPipelineByAppId", varargs...)
	ret0, _ := ret[0].(*PipelineArray)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineByAppId indicates an expected call of GetPipelineByAppId.
func (mr *MockPipelineServiceClientMockRecorder) GetPipelineByAppId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineByAppId", reflect.TypeOf((*MockPipelineServiceClient)(nil).GetPipelineByAppId), varargs...)
}

// GetPipelineConfig mocks base method.
func (m *MockPipelineServiceClient) GetPipelineConfig(ctx context.Context, in *GetPipelineConfigReq, opts ...grpc.CallOption) (*GetPipelineConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPipelineConfig", varargs...)
	ret0, _ := ret[0].(*GetPipelineConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineConfig indicates an expected call of GetPipelineConfig.
func (mr *MockPipelineServiceClientMockRecorder) GetPipelineConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineConfig", reflect.TypeOf((*MockPipelineServiceClient)(nil).GetPipelineConfig), varargs...)
}

// GetPipelineCountByAppIds mocks base method.
func (m *MockPipelineServiceClient) GetPipelineCountByAppIds(ctx context.Context, in *PipelineCountReq, opts ...grpc.CallOption) (*PipelineCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPipelineCountByAppIds", varargs...)
	ret0, _ := ret[0].(*PipelineCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineCountByAppIds indicates an expected call of GetPipelineCountByAppIds.
func (mr *MockPipelineServiceClientMockRecorder) GetPipelineCountByAppIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineCountByAppIds", reflect.TypeOf((*MockPipelineServiceClient)(nil).GetPipelineCountByAppIds), varargs...)
}

// GetTaskById mocks base method.
func (m *MockPipelineServiceClient) GetTaskById(ctx context.Context, in *Task, opts ...grpc.CallOption) (*Task, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTaskById", varargs...)
	ret0, _ := ret[0].(*Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskById indicates an expected call of GetTaskById.
func (mr *MockPipelineServiceClientMockRecorder) GetTaskById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskById", reflect.TypeOf((*MockPipelineServiceClient)(nil).GetTaskById), varargs...)
}

// HandlePrepareingPipeline mocks base method.
func (m *MockPipelineServiceClient) HandlePrepareingPipeline(ctx context.Context, in *PrepareingReq, opts ...grpc.CallOption) (*PrepareingResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandlePrepareingPipeline", varargs...)
	ret0, _ := ret[0].(*PrepareingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandlePrepareingPipeline indicates an expected call of HandlePrepareingPipeline.
func (mr *MockPipelineServiceClientMockRecorder) HandlePrepareingPipeline(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlePrepareingPipeline", reflect.TypeOf((*MockPipelineServiceClient)(nil).HandlePrepareingPipeline), varargs...)
}

// NewPipeline mocks base method.
func (m *MockPipelineServiceClient) NewPipeline(ctx context.Context, in *Pipeline, opts ...grpc.CallOption) (*PipelineResult, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NewPipeline", varargs...)
	ret0, _ := ret[0].(*PipelineResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewPipeline indicates an expected call of NewPipeline.
func (mr *MockPipelineServiceClientMockRecorder) NewPipeline(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewPipeline", reflect.TypeOf((*MockPipelineServiceClient)(nil).NewPipeline), varargs...)
}

// RunPipeline mocks base method.
func (m *MockPipelineServiceClient) RunPipeline(ctx context.Context, in *RunPipelingReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RunPipeline", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RunPipeline indicates an expected call of RunPipeline.
func (mr *MockPipelineServiceClientMockRecorder) RunPipeline(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunPipeline", reflect.TypeOf((*MockPipelineServiceClient)(nil).RunPipeline), varargs...)
}

// UpdatePipelineAppMsg mocks base method.
func (m *MockPipelineServiceClient) UpdatePipelineAppMsg(ctx context.Context, in *PipelineAppReq, opts ...grpc.CallOption) (*PipelineAppResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePipelineAppMsg", varargs...)
	ret0, _ := ret[0].(*PipelineAppResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePipelineAppMsg indicates an expected call of UpdatePipelineAppMsg.
func (mr *MockPipelineServiceClientMockRecorder) UpdatePipelineAppMsg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineAppMsg", reflect.TypeOf((*MockPipelineServiceClient)(nil).UpdatePipelineAppMsg), varargs...)
}

// UpdatePipelineTaskConfig mocks base method.
func (m *MockPipelineServiceClient) UpdatePipelineTaskConfig(ctx context.Context, in *UpdatePipelineTaskConfigReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePipelineTaskConfig", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePipelineTaskConfig indicates an expected call of UpdatePipelineTaskConfig.
func (mr *MockPipelineServiceClientMockRecorder) UpdatePipelineTaskConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineTaskConfig", reflect.TypeOf((*MockPipelineServiceClient)(nil).UpdatePipelineTaskConfig), varargs...)
}

// UpdatePipelineTaskMultiCloudConfig mocks base method.
func (m *MockPipelineServiceClient) UpdatePipelineTaskMultiCloudConfig(ctx context.Context, in *UpdatePipelineTaskMultiCloudConfigReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePipelineTaskMultiCloudConfig", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePipelineTaskMultiCloudConfig indicates an expected call of UpdatePipelineTaskMultiCloudConfig.
func (mr *MockPipelineServiceClientMockRecorder) UpdatePipelineTaskMultiCloudConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineTaskMultiCloudConfig", reflect.TypeOf((*MockPipelineServiceClient)(nil).UpdatePipelineTaskMultiCloudConfig), varargs...)
}

// MockPipelineServiceServer is a mock of PipelineServiceServer interface.
type MockPipelineServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockPipelineServiceServerMockRecorder
}

// MockPipelineServiceServerMockRecorder is the mock recorder for MockPipelineServiceServer.
type MockPipelineServiceServerMockRecorder struct {
	mock *MockPipelineServiceServer
}

// NewMockPipelineServiceServer creates a new mock instance.
func NewMockPipelineServiceServer(ctrl *gomock.Controller) *MockPipelineServiceServer {
	mock := &MockPipelineServiceServer{ctrl: ctrl}
	mock.recorder = &MockPipelineServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPipelineServiceServer) EXPECT() *MockPipelineServiceServerMockRecorder {
	return m.recorder
}

// BatchUpdatePipelineAppMsg mocks base method.
func (m *MockPipelineServiceServer) BatchUpdatePipelineAppMsg(arg0 context.Context, arg1 *PipelineAppsReq) (*PipelineAppResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdatePipelineAppMsg", arg0, arg1)
	ret0, _ := ret[0].(*PipelineAppResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdatePipelineAppMsg indicates an expected call of BatchUpdatePipelineAppMsg.
func (mr *MockPipelineServiceServerMockRecorder) BatchUpdatePipelineAppMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdatePipelineAppMsg", reflect.TypeOf((*MockPipelineServiceServer)(nil).BatchUpdatePipelineAppMsg), arg0, arg1)
}

// DelPipeline mocks base method.
func (m *MockPipelineServiceServer) DelPipeline(arg0 context.Context, arg1 *DelPipelineReq) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPipeline", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPipeline indicates an expected call of DelPipeline.
func (mr *MockPipelineServiceServerMockRecorder) DelPipeline(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPipeline", reflect.TypeOf((*MockPipelineServiceServer)(nil).DelPipeline), arg0, arg1)
}

// GetPipelineByAppId mocks base method.
func (m *MockPipelineServiceServer) GetPipelineByAppId(arg0 context.Context, arg1 *Pipeline) (*PipelineArray, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineByAppId", arg0, arg1)
	ret0, _ := ret[0].(*PipelineArray)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineByAppId indicates an expected call of GetPipelineByAppId.
func (mr *MockPipelineServiceServerMockRecorder) GetPipelineByAppId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineByAppId", reflect.TypeOf((*MockPipelineServiceServer)(nil).GetPipelineByAppId), arg0, arg1)
}

// GetPipelineConfig mocks base method.
func (m *MockPipelineServiceServer) GetPipelineConfig(arg0 context.Context, arg1 *GetPipelineConfigReq) (*GetPipelineConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineConfig", arg0, arg1)
	ret0, _ := ret[0].(*GetPipelineConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineConfig indicates an expected call of GetPipelineConfig.
func (mr *MockPipelineServiceServerMockRecorder) GetPipelineConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineConfig", reflect.TypeOf((*MockPipelineServiceServer)(nil).GetPipelineConfig), arg0, arg1)
}

// GetPipelineCountByAppIds mocks base method.
func (m *MockPipelineServiceServer) GetPipelineCountByAppIds(arg0 context.Context, arg1 *PipelineCountReq) (*PipelineCountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineCountByAppIds", arg0, arg1)
	ret0, _ := ret[0].(*PipelineCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineCountByAppIds indicates an expected call of GetPipelineCountByAppIds.
func (mr *MockPipelineServiceServerMockRecorder) GetPipelineCountByAppIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineCountByAppIds", reflect.TypeOf((*MockPipelineServiceServer)(nil).GetPipelineCountByAppIds), arg0, arg1)
}

// GetTaskById mocks base method.
func (m *MockPipelineServiceServer) GetTaskById(arg0 context.Context, arg1 *Task) (*Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskById", arg0, arg1)
	ret0, _ := ret[0].(*Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskById indicates an expected call of GetTaskById.
func (mr *MockPipelineServiceServerMockRecorder) GetTaskById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskById", reflect.TypeOf((*MockPipelineServiceServer)(nil).GetTaskById), arg0, arg1)
}

// HandlePrepareingPipeline mocks base method.
func (m *MockPipelineServiceServer) HandlePrepareingPipeline(arg0 context.Context, arg1 *PrepareingReq) (*PrepareingResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlePrepareingPipeline", arg0, arg1)
	ret0, _ := ret[0].(*PrepareingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandlePrepareingPipeline indicates an expected call of HandlePrepareingPipeline.
func (mr *MockPipelineServiceServerMockRecorder) HandlePrepareingPipeline(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlePrepareingPipeline", reflect.TypeOf((*MockPipelineServiceServer)(nil).HandlePrepareingPipeline), arg0, arg1)
}

// NewPipeline mocks base method.
func (m *MockPipelineServiceServer) NewPipeline(arg0 context.Context, arg1 *Pipeline) (*PipelineResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewPipeline", arg0, arg1)
	ret0, _ := ret[0].(*PipelineResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewPipeline indicates an expected call of NewPipeline.
func (mr *MockPipelineServiceServerMockRecorder) NewPipeline(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewPipeline", reflect.TypeOf((*MockPipelineServiceServer)(nil).NewPipeline), arg0, arg1)
}

// RunPipeline mocks base method.
func (m *MockPipelineServiceServer) RunPipeline(arg0 context.Context, arg1 *RunPipelingReq) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunPipeline", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RunPipeline indicates an expected call of RunPipeline.
func (mr *MockPipelineServiceServerMockRecorder) RunPipeline(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunPipeline", reflect.TypeOf((*MockPipelineServiceServer)(nil).RunPipeline), arg0, arg1)
}

// UpdatePipelineAppMsg mocks base method.
func (m *MockPipelineServiceServer) UpdatePipelineAppMsg(arg0 context.Context, arg1 *PipelineAppReq) (*PipelineAppResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineAppMsg", arg0, arg1)
	ret0, _ := ret[0].(*PipelineAppResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePipelineAppMsg indicates an expected call of UpdatePipelineAppMsg.
func (mr *MockPipelineServiceServerMockRecorder) UpdatePipelineAppMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineAppMsg", reflect.TypeOf((*MockPipelineServiceServer)(nil).UpdatePipelineAppMsg), arg0, arg1)
}

// UpdatePipelineTaskConfig mocks base method.
func (m *MockPipelineServiceServer) UpdatePipelineTaskConfig(arg0 context.Context, arg1 *UpdatePipelineTaskConfigReq) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineTaskConfig", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePipelineTaskConfig indicates an expected call of UpdatePipelineTaskConfig.
func (mr *MockPipelineServiceServerMockRecorder) UpdatePipelineTaskConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineTaskConfig", reflect.TypeOf((*MockPipelineServiceServer)(nil).UpdatePipelineTaskConfig), arg0, arg1)
}

// UpdatePipelineTaskMultiCloudConfig mocks base method.
func (m *MockPipelineServiceServer) UpdatePipelineTaskMultiCloudConfig(arg0 context.Context, arg1 *UpdatePipelineTaskMultiCloudConfigReq) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineTaskMultiCloudConfig", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePipelineTaskMultiCloudConfig indicates an expected call of UpdatePipelineTaskMultiCloudConfig.
func (mr *MockPipelineServiceServerMockRecorder) UpdatePipelineTaskMultiCloudConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineTaskMultiCloudConfig", reflect.TypeOf((*MockPipelineServiceServer)(nil).UpdatePipelineTaskMultiCloudConfig), arg0, arg1)
}

// mustEmbedUnimplementedPipelineServiceServer mocks base method.
func (m *MockPipelineServiceServer) mustEmbedUnimplementedPipelineServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedPipelineServiceServer")
}

// mustEmbedUnimplementedPipelineServiceServer indicates an expected call of mustEmbedUnimplementedPipelineServiceServer.
func (mr *MockPipelineServiceServerMockRecorder) mustEmbedUnimplementedPipelineServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedPipelineServiceServer", reflect.TypeOf((*MockPipelineServiceServer)(nil).mustEmbedUnimplementedPipelineServiceServer))
}

// MockUnsafePipelineServiceServer is a mock of UnsafePipelineServiceServer interface.
type MockUnsafePipelineServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafePipelineServiceServerMockRecorder
}

// MockUnsafePipelineServiceServerMockRecorder is the mock recorder for MockUnsafePipelineServiceServer.
type MockUnsafePipelineServiceServerMockRecorder struct {
	mock *MockUnsafePipelineServiceServer
}

// NewMockUnsafePipelineServiceServer creates a new mock instance.
func NewMockUnsafePipelineServiceServer(ctrl *gomock.Controller) *MockUnsafePipelineServiceServer {
	mock := &MockUnsafePipelineServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafePipelineServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafePipelineServiceServer) EXPECT() *MockUnsafePipelineServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedPipelineServiceServer mocks base method.
func (m *MockUnsafePipelineServiceServer) mustEmbedUnimplementedPipelineServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedPipelineServiceServer")
}

// mustEmbedUnimplementedPipelineServiceServer indicates an expected call of mustEmbedUnimplementedPipelineServiceServer.
func (mr *MockUnsafePipelineServiceServerMockRecorder) mustEmbedUnimplementedPipelineServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedPipelineServiceServer", reflect.TypeOf((*MockUnsafePipelineServiceServer)(nil).mustEmbedUnimplementedPipelineServiceServer))
}
