package find

import (
	"context"
	"encoding/json"
	"time"

	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
)

type FindK8SResourceArgs struct {
	Search    string `json:"search" jsonschema:"required,description=要查询资源的名称(模糊匹配,只能查询出资源名称包含该关键字的资源,不支持正则匹配),此处的名称是指资源的名称,而不是资源的类型。"`
	Cluster   string `json:"cluster" jsonschema:"description=集群名称,非必填"`
	Namespace string `json:"namespace" jsonschema:"description=命名空间(NS)名称,非必填"`
}

type MultiResourceObj struct {
	Name         string         `json:"name" jsonschema:"required,description=资源名称"`
	Resource     string         `json:"resource" jsonschema:"required,description=资源类型"`
	ClusterName  string         `json:"clusterName" jsonschema:"required,description=集群名称"`
	Namespace    string         `json:"namespace" jsonschema:"required,description=命名空间(NS)名称"`
	Uuid         string         `json:"uuid" jsonschema:"description=资源唯一标识"`
	Descriptions map[string]any `json:"descriptions" jsonschema:"description=资源当前的状态信息 key-value结构体 key为状态名称、value为状态值"`
}

type K8sResourcesResp struct {
	Total int64              `json:"total" jsonschema:"required,description=查询到的资源总数"`
	List  []MultiResourceObj `json:"list" jsonschema:"required,description=资源列表"`
}

func (agg *findAgg) FindK8SResource(req FindK8SResourceArgs) (rst K8sResourcesResp, err error) {
	// 查询 k8s 资源  返回数据量不宜过大， 可能触发 SSE stream error: bufio.Scanner: token too long

	reqParm := &constack.MultiResourceReq{
		Search: req.Search,
		Page:   1,
		Size:   33,
	}
	if req.Cluster != "" {
		reqParm.Cluster = []string{req.Cluster}
	}
	if req.Namespace != "" {
		reqParm.Namespace = []string{req.Namespace}
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*3)
	defer cancel()
	objs, err := agg.cloudagg.MultiResource(ctx, reqParm)
	if err != nil {
		return
	}

	rst.Total = objs.Total
	for _, obj := range objs.GetObjs() {
		resource := MultiResourceObj{
			Name:        obj.Name,
			Resource:    obj.Resource,
			ClusterName: obj.ClusterName,
			Namespace:   obj.Namespace,
			Uuid:        obj.Uuid,
		}
		json.Unmarshal(obj.Data, &resource.Descriptions)

		rst.List = append(rst.List, resource)
	}

	return
}
