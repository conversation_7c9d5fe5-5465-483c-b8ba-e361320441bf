package tekton

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	v1 "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1"

	"52tt.com/cicd/services/pipeline/pkg/tekton/utils"

	"52tt.com/cicd/pkg/constants"
	pkgcontext "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/tools"
	"52tt.com/cicd/services/pipeline/internal/conf"
	"52tt.com/cicd/services/pipeline/internal/dao"
	"52tt.com/cicd/services/pipeline/internal/model"
	bizerr "52tt.com/cicd/services/pipeline/pkg/error"
	"github.com/pkg/errors"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func (c *Client) DeletePipelineRun(ctx context.Context, namespace, name string) error {
	err := c.tektonClient.TektonV1beta1().PipelineRuns(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			log.ErrorWithCtx(ctx, "delete Tekton Pipeline run Namespace[%s] Name[%s] not found", namespace, name)
			return bizerr.ErrTektonPipelineRunNotFound
		}
		log.ErrorWithCtx(ctx, "delete Tekton Pipeline run Namespace[%s] Name[%s] fail %v", namespace, name, err)
		return err
	}
	return nil
}

func (c *Client) GetPipelineRun(ctx context.Context, namespace string, pname string) (*v1beta1.PipelineRun, error) {
	run, err := c.tektonClient.TektonV1beta1().PipelineRuns(namespace).Get(ctx, pname, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			log.ErrorWithCtx(ctx, "get Tekton Pipeline run Namespace[%s] Name[%s] not found", namespace, pname)
			return nil, bizerr.ErrTektonPipelineRunNotFound
		}
		log.ErrorWithCtx(ctx, "get Tekton Pipeline run Namespace[%s] Name[%s] fail %v", namespace, pname, err)
		return nil, err
	}
	return run, nil
}

func (c *Client) GetPipelineRunByLabel(ctx context.Context, namespace string, label string) (*v1beta1.PipelineRun, error) {
	runs, err := c.tektonClient.TektonV1beta1().PipelineRuns(namespace).List(ctx, metav1.ListOptions{LabelSelector: label})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			log.ErrorWithCtx(ctx, "get Tekton Pipeline run Namespace[%s] label[%s] not found", namespace, label)
			return nil, bizerr.ErrTektonPipelineRunNotFound
		}
		log.ErrorWithCtx(ctx, "get Tekton Pipeline run Namespace[%s] label[%s] fail %v", namespace, label, err)
		return nil, err
	}
	if len(runs.Items) == 0 {
		return nil, nil
	}
	return &runs.Items[0], nil
}

func (c *Client) GetCustomRun(ctx context.Context, namespace string, name string) (*v1beta1.CustomRun, error) {
	run, err := c.tektonClient.TektonV1beta1().CustomRuns(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			log.ErrorWithCtx(ctx, "get Tekton CustomRuns Namespace[%s] Name[%s] not found", namespace, name)
			return nil, bizerr.ErrTektonPipelineRunNotFound
		}
		log.ErrorWithCtx(ctx, "get Tekton CustomRuns Namespace[%s] Name[%s] fail %v", namespace, name, err)
		return nil, err
	}
	return run, nil
}

func (c *Client) UpdateCustomRunStatus(ctx context.Context, namespace string, customRun *v1beta1.CustomRun) error {
	_, err := c.tektonClient.TektonV1beta1().CustomRuns(namespace).Update(ctx, customRun, metav1.UpdateOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			log.ErrorWithCtx(ctx, "update Tekton customRun Namespace[%s] Name[%s] not found", namespace, customRun.Name)
			return bizerr.ErrTektonPipelineRunNotFound
		}
		log.ErrorWithCtx(ctx, "update Tekton customRun Namespace[%s] Name[%s] fail %v", namespace, customRun.Name, err)
		return err
	}
	return nil
}

func (c *Client) UpdatePipelineRunStatus(ctx context.Context, namespace string, pipelineRun *v1beta1.PipelineRun) error {
	_, err := c.tektonClient.TektonV1beta1().PipelineRuns(namespace).Update(ctx, pipelineRun, metav1.UpdateOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			log.ErrorWithCtx(ctx, "update Tekton Pipeline run Namespace[%s] Name[%s] not found", namespace, pipelineRun.Name)
			return bizerr.ErrTektonPipelineRunNotFound
		}
		log.ErrorWithCtx(ctx, "update Tekton Pipeline run Namespace[%s] Name[%s] fail %v", namespace, pipelineRun.Name, err)
		return err
	}
	return nil
}

func (c *Client) GetTaskRun(ctx context.Context, namespace, name string) (*v1.TaskRun, error) {
	task, err := c.tektonClient.TektonV1().TaskRuns(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			log.ErrorWithCtx(ctx, "get Tekton Task Namespace[%s] Name[%s] not found", namespace, name)
			return nil, err
		}
		log.ErrorWithCtx(ctx, "get Tekton Task Namespace[%s] Name[%s] fail %v", namespace, name, err)
		return nil, err
	}
	return task, nil

}

func (c *Client) UpdateTaskRunStatus(ctx context.Context, namespace string, tr *v1.TaskRun) error {
	_, err := c.tektonClient.TektonV1().TaskRuns(namespace).Update(ctx, tr, metav1.UpdateOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			log.ErrorWithCtx(ctx, "update Tekton TaskRun Namespace[%s] Name[%s] not found", namespace, tr.Name)
			return bizerr.ErrTektonPipelineRunNotFound
		}
		log.ErrorWithCtx(ctx, "update Tekton TaskRun Namespace[%s] Name[%s] fail %v", namespace, tr.Name, err)
		return err
	}
	return nil
}

func genTektonPipelineRunName(env string, kind constants.PipelineRunKind, runID int64) string {
	return fmt.Sprintf("%s-%s-%d", env, kind, runID)
}

// CreatePipelineRun just create CI Pipelinerun
func (c *Client) CreatePipelineRun(ctx context.Context, pipelineRun *dao.PipelineRun, pipeline *v1beta1.Pipeline, extras ...ExtraParam) (*v1beta1.PipelineRun, error) {
	extraParams := &ExtraParams{}
	for _, op := range extras {
		op(extraParams)
	}

	for _, stage := range pipelineRun.Stages {
		if c.hasOverseaCD(stage) {
			extraParams.HasOverseaCD = true
			break
		}
	}

	pr := &v1beta1.PipelineRun{
		ObjectMeta: metav1.ObjectMeta{
			Name:      genTektonPipelineRunName(c.env, constants.CIPipelineRun, pipelineRun.ID),
			Namespace: pipelineRun.Pipeline.Template.TektonNamespace,
			Annotations: map[string]string{
				string(constants.PipelineRunIdKey): strconv.FormatInt(pipelineRun.ID, 10),
			},
		},
		Spec: v1beta1.PipelineRunSpec{
			Timeouts: &v1beta1.TimeoutFields{
				Pipeline: &metav1.Duration{Duration: c.timeouts["pipelineTimeouts"]},
				Tasks:    &metav1.Duration{Duration: c.timeouts["tasksTimeouts"]},
				Finally:  &metav1.Duration{Duration: c.timeouts["finallyTimeouts"]},
			},
			PipelineRef: &v1beta1.PipelineRef{
				Name: pipeline.Name,
			},
			Workspaces: []v1beta1.WorkspaceBinding{
				{Name: "cache", PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{ClaimName: "pvc-nfs"}, SubPath: "cache/image"},
				{Name: "buildCache", PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{ClaimName: "pvc-nfs"}, SubPath: fmt.Sprintf("cache/build/%s/%s", pipelineRun.Pipeline.AppName, pipelineRun.Pipeline.GetLanguageVersionFromPrCache())},
				{Name: "pkgCache", PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{ClaimName: "pvc-nfs"}, SubPath: fmt.Sprintf("cache/pkg/%s/%s", pipelineRun.Pipeline.AppName, pipelineRun.Pipeline.GetLanguageVersionFromPrCache())},
				{Name: "sonarCache", PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{ClaimName: "pvc-nfs"}, SubPath: fmt.Sprintf("cache/sonar/%s", pipelineRun.Pipeline.AppName)},
				{Name: "share-data", VolumeClaimTemplate: &corev1.PersistentVolumeClaim{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							string(constants.EnvKey):         c.env,
							string(constants.RepoAddressKey): pipelineRun.RepoAddress,
							string(constants.RequestDateKey): extraParams.RequestDate,
							string(constants.PipelineIdKey):  strconv.FormatInt(pipelineRun.PipelineId, 10),
							string(constants.BuildNumberKey): strconv.FormatInt(pipelineRun.BuildNumber, 10),
							string(constants.CsiVersionKey):  "v2",
							string(constants.BranchKey):      pipelineRun.Branch,
						},
					},
					Spec: corev1.PersistentVolumeClaimSpec{
						AccessModes:      []corev1.PersistentVolumeAccessMode{"ReadWriteMany"},
						StorageClassName: utils.StringPointer("nfs-client"),
						Resources: corev1.VolumeResourceRequirements{
							Requests: corev1.ResourceList{
								corev1.ResourceStorage: resource.MustParse("1Gi"),
							},
						},
					},
				}},
				{Name: "credential", Secret: &corev1.SecretVolumeSource{SecretName: "cr"}},
				{Name: "sonar-credentials", Secret: &corev1.SecretVolumeSource{SecretName: "sonar-credentials"}},
				{Name: "gitlab-http-auth-secret", Secret: &corev1.SecretVolumeSource{SecretName: "gitlab-http-auth-secret"}},
			},
			Params:       []v1beta1.Param{},
			TaskRunSpecs: []v1beta1.PipelineTaskRunSpec{},
		},
	}
	if pkgcontext.RequestID(ctx) != "" {
		pr.Annotations[string(constants.RequestIDKey)] = pkgcontext.RequestID(ctx)
	}

	// handle params
	var params []v1beta1.Param
	index := 0

	ciConfigStages := conf.GetCiStagesConfig()
	for _, s := range pipelineRun.Stages {
		for _, t := range s.Tasks {
			p, err := c.generatePipelineRunParams(ctx, t, *pipelineRun, extraParams)
			if err != nil {
				log.Errorf("create PipelineRun generate Params failed %s", err.Error())
				return nil, err
			}
			params = append(params, p...)

			isCiStage := tools.Any(ciConfigStages, func(r string) bool {
				return strings.EqualFold(r, s.Type)
			})
			if !isCiStage {
				continue
			}
			// add annotation for all CI tasks
			tektonTask := pipeline.Spec.Tasks[index]
			ptrSpec := v1beta1.PipelineTaskRunSpec{
				PipelineTaskName: tektonTask.Name,
				Metadata: &v1beta1.PipelineTaskMetadata{
					Annotations: map[string]string{
						string(constants.PipelineRunIdKey):      fmt.Sprintf("%d", pipelineRun.ID),
						string(constants.PipelineRunStageIdKey): fmt.Sprintf("%d", s.ID),
						string(constants.PipelineRunTaskIdKey):  fmt.Sprintf("%d", t.ID),
					},
				},
			}

			// 任务POD 选择特殊 集群节点
			taskPodTmpl, annos, isSpecialPod := c.genSpecialTaskPod(t, *pipelineRun)
			if isSpecialPod {
				ptrSpec.TaskPodTemplate = taskPodTmpl
				for k, v := range annos {
					ptrSpec.Metadata.Annotations[k] = v
				}
			}
			pr.Spec.TaskRunSpecs = append(pr.Spec.TaskRunSpecs, ptrSpec)
			index++
		}
	}
	pr.Spec.Params = params
	// trigger tekton pipeline run
	_, err := c.tektonClient.TektonV1beta1().PipelineRuns(pipelineRun.Pipeline.Template.TektonNamespace).Create(ctx, pr, metav1.CreateOptions{})
	if err != nil {
		log.Errorf("create Tekton PR for pipeline run[%d](%s) failed %s", pipelineRun.ID, pipelineRun.Pipeline.Template.TektonNamespace, err.Error())
		return nil, err
	}
	return pr, nil
}

func (c *Client) generatePipelineRunParams(ctx context.Context, t dao.PipelineRunTask, pipelineRun dao.PipelineRun, extraParams *ExtraParams) ([]v1beta1.Param, error) {
	var params []v1beta1.Param
	nodeBaseImage := "cr.ttyuyin.com/public/node:"
	pythonBaseImage := "cr.ttyuyin.com/public/python:"
	golangBaseImage := "cr.ttyuyin.com/public/golang:"
	switch t.GetType() {
	case constants.TASK_PULL_CODE:
		var pullCode model.PullCode
		if err := json.Unmarshal(t.Config, &pullCode); err != nil {
			return nil, fmt.Errorf("%w: %v", bizerr.ErrConfigUnmarshal, err)
		}
		params = []v1beta1.Param{
			{Name: "repo-url", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: pipelineRun.RepoAddress}},
			{Name: "revision", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: pipelineRun.Branch}},
			{Name: "submodules", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: strconv.FormatBool(pullCode.PullSubModule)}},
		}
		for _, f := range pullCode.Extends {
			switch f {
			case "remote":
				params = append(params, v1beta1.Param{Name: "remote", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: strconv.FormatBool(true)}})
			}
		}

		if constants.IsPreMergePipeline(pipelineRun.Pipeline.Type) {
			params = append(params, v1beta1.Param{Name: "depth", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "255"}}) //
			params = append(params, v1beta1.Param{Name: "sourceBranch", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: pipelineRun.SourceBranch}})
			params = append(params, v1beta1.Param{Name: "needMerge", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: strconv.FormatBool(true)}})
		}

	case constants.TASK_AUTOMATION_COMPILE:
		var build model.AutomationCompile
		if err := json.Unmarshal(t.Config, &build); err != nil {
			return nil, fmt.Errorf("%w: %v", bizerr.ErrConfigUnmarshal, err)
		}
		buildCommand := utils.ApplyScriptParams(build.BuildCommand, pipelineRun)
		switch pipelineRun.Pipeline.Template.Language {
		case "go":
			params = []v1beta1.Param{
				{Name: "buildCommand", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: buildCommand}},
			}
			if build.ImageAddress != "" {
				params = append(params, v1beta1.Param{Name: "golang-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: build.ImageAddress}})
			} else {
				params = append(params, v1beta1.Param{Name: "golang-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: golangBaseImage + pipelineRun.Pipeline.GetLanguageVersion()}})
			}
		case "java":
			params = []v1beta1.Param{
				{Name: "maven-build-command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: buildCommand}},
			}
			if build.ImageAddress != "" {
				params = append(params, v1beta1.Param{Name: "java-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: build.ImageAddress}})
			} else {
				defaultJDKImage := fmt.Sprintf("cr.ttyuyin.com/devops/tekton/maven:yw-nexus-3.6-jdk-%s", pipelineRun.Pipeline.GetLanguageVersion())
				params = append(params, v1beta1.Param{Name: "java-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: defaultJDKImage}})
			}
		case "node":
			params = []v1beta1.Param{
				{Name: "node-build-command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: buildCommand}},
			}
			if build.ImageAddress != "" {
				params = append(params, v1beta1.Param{Name: "node-build-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: build.ImageAddress}})
			} else {
				params = append(params, v1beta1.Param{Name: "node-build-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: nodeBaseImage + pipelineRun.Pipeline.GetLanguageVersion()}})
			}
		case "cpp":
			params = []v1beta1.Param{
				{Name: "cppBuildCommand", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: buildCommand}},
			}
			if build.ImageAddress != "" {
				params = append(params, v1beta1.Param{Name: "cppBuildImage", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: build.ImageAddress}})
			}
		case "python":
			params = []v1beta1.Param{
				{Name: "python-build-command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: buildCommand}},
			}
			if build.ImageAddress != "" {
				params = append(params, v1beta1.Param{Name: "python-build-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: build.ImageAddress}})
			} else {
				params = append(params, v1beta1.Param{Name: "python-build-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: pythonBaseImage + pipelineRun.Pipeline.GetLanguageVersion()}})
			}
		default:
			log.Infof("genearte tekton PipelineRun task %s language %s not support yet", t.Type, pipelineRun.Pipeline.Template.Language)
		}
	case constants.TASK_UNIT_TEST:
		var unitTest model.UnitTest
		if err := json.Unmarshal(t.Config, &unitTest); err != nil {
			return nil, fmt.Errorf("%w: %v", bizerr.ErrConfigUnmarshal, err)
		}
		testCommand := utils.ApplyScriptParams(unitTest.UnitTestCommand, pipelineRun)
		switch pipelineRun.Pipeline.Template.Language {
		case "go":
			params = []v1beta1.Param{
				{Name: "testCommand", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: testCommand}},
			}
			if unitTest.ImageAddress != "" {
				params = append(params, v1beta1.Param{Name: "goTestImage", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: unitTest.ImageAddress}})
			} else {
				params = append(params, v1beta1.Param{Name: "goTestImage", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: golangBaseImage + pipelineRun.Pipeline.GetLanguageVersion()}})
			}
		case "java":
			params = []v1beta1.Param{
				{Name: "maven-test-command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: testCommand}},
			}
			if unitTest.ImageAddress != "" {
				params = append(params, v1beta1.Param{Name: "maven-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: unitTest.ImageAddress}})
			} else {
				defaultJDKImage := fmt.Sprintf("cr.ttyuyin.com/devops/tekton/maven:yw-nexus-3.6-jdk-%s", pipelineRun.Pipeline.GetLanguageVersion())
				params = append(params, v1beta1.Param{Name: "maven-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: defaultJDKImage}})
			}
		case "node":
			params = []v1beta1.Param{
				{Name: "node-test-command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: testCommand}},
			}
			if unitTest.ImageAddress != "" {
				params = append(params, v1beta1.Param{Name: "node-test-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: unitTest.ImageAddress}})
			} else {
				params = append(params, v1beta1.Param{Name: "node-test-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: nodeBaseImage + pipelineRun.Pipeline.GetLanguageVersion()}})
			}
		case "cpp":
			params = []v1beta1.Param{
				{Name: "cppTestCommand", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: testCommand}},
			}
			if unitTest.ImageAddress != "" {
				params = append(params, v1beta1.Param{Name: "cppTestImage", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: unitTest.ImageAddress}})
			}
		case "python":
			params = []v1beta1.Param{
				{Name: "python-test-command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: testCommand}},
			}
			if unitTest.ImageAddress != "" {
				params = append(params, v1beta1.Param{Name: "python-test-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: unitTest.ImageAddress}})
			} else {
				params = append(params, v1beta1.Param{Name: "python-test-image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: pythonBaseImage + pipelineRun.Pipeline.GetLanguageVersion()}})
			}
		default:
			log.Infof("genearte tekton PipelineRun task %s language %s not support yet", t.Type, pipelineRun.Pipeline.Template.Language)
		}
	case constants.TASK_CUSTOM_SHELL:
		var customShell model.CustomShell
		if err := json.Unmarshal(t.Config, &customShell); err != nil {
			return nil, fmt.Errorf("%w: %v", bizerr.ErrConfigUnmarshal, err)
		}
		shellCommand := utils.ApplyScriptParams(customShell.ExecShell, pipelineRun)
		params = []v1beta1.Param{
			{Name: fmt.Sprintf("shell-contents-%d", t.TaskId), Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: shellCommand}},
		}
		if customShell.ImageAddress != "" {
			params = append(params, v1beta1.Param{Name: fmt.Sprintf("shell-image-%d", t.TaskId), Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: customShell.ImageAddress}})
		}
	case constants.TASK_CHECKSTYLE:
		var checkStyle model.CheckStyle
		if err := json.Unmarshal(t.Config, &checkStyle); err != nil {
			return nil, fmt.Errorf("%w: %v", bizerr.ErrConfigUnmarshal, err)
		}
		checkStyleCommand := utils.ApplyScriptParams(checkStyle.CheckStyleCommand, pipelineRun)
		// 流水线语言 go(1.19) → go1.19
		checkStyleLanguageTag := regexp.MustCompile(`[()]`).ReplaceAllString(pipelineRun.Pipeline.Language, "")
		params = []v1beta1.Param{
			{Name: "checkStyleLanguageTag", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: checkStyleLanguageTag}},
			{Name: "checkStyleCommand", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: checkStyleCommand}},
			{Name: "checkStyleRule", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: checkStyle.CheckStyleRule}},
		}
	case constants.TASK_SONAR_SCAN:
		var sonarScan model.SonarScan
		if err := json.Unmarshal(t.Config, &sonarScan); err != nil {
			return nil, fmt.Errorf("%w: %v", bizerr.ErrConfigUnmarshal, err)
		}
		sonarScanParam := utils.ApplyScriptParams(sonarScan.ScanParam, pipelineRun)
		projectKey := fmt.Sprintf("%s-%s", extraParams.ProjectIdentity, pipelineRun.Pipeline.AppName)
		params = []v1beta1.Param{
			{Name: "SONAR_PROJECT_KEY", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: projectKey}},
			{Name: "SONAR_EXCLUSIONS", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: sonarScan.ExcludeRule}},
			{Name: "SONAR_TEST_INCLUSIONS", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: sonarScan.TestFileIncludeRule}},
			{Name: "SONAR_FLAGS", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: sonarScanParam}},
			{Name: "SONAR_QUALITY_GATEWAY", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: strconv.FormatBool(sonarScan.TerminatePipeline)}},
			{Name: "LANGUAGE", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: pipelineRun.Pipeline.GetProgramLanguage()}},
		}
		for _, f := range sonarScan.ScanThreshold {
			switch f.Name {
			case constants.BUG:
				params = append(params, v1beta1.Param{Name: "SONAR_QUALITY_BUG", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: f.ValueFormated()}})
			case constants.VULNERABILITY:
				params = append(params, v1beta1.Param{Name: "SONAR_QUALITY_VUL", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: f.ValueFormated()}})
			case constants.BAD_SMELL:
				params = append(params, v1beta1.Param{Name: "SONAR_QUALITY_SMELL", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: f.ValueFormated()}})
			case constants.TEST_COVERAGE:
				params = append(params, v1beta1.Param{Name: "SONAR_QUALITY_COVERAGE", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: f.ValueFormated()}})
			}
		}
		log.InfoWithCtx(ctx, "sonar scan task[%s], params: %+v", projectKey, params)
	case constants.TASK_GENERATE_PUSH_IMAGE:
		var pushImage model.GeneratePushImage
		if err := json.Unmarshal(t.Config, &pushImage); err != nil {
			return nil, fmt.Errorf("%w: %v", bizerr.ErrConfigUnmarshal, err)
		}
		// build args
		var buildArgs []string
		for _, aMap := range pushImage.BuildParams {
			buildParamsValue := aMap["value"]
			if aMap["value"] != "" {
				buildParamsValue = utils.ApplyScriptParams(aMap["value"], pipelineRun)
			}
			buildArgs = append(buildArgs, fmt.Sprintf("--build-arg=%s=%s", aMap["key"], buildParamsValue))
		}

		registryUrl := conf.AppConfig.Tekton.RegistryUrl
		if pushImage.PushOverseasRegistryFlag || extraParams.HasOverseaCD {
			registryUrl = conf.AppConfig.Tekton.OverseasRegistryUrl
		}
		// 项目特殊镜像地址 优先级高于默认地址
		if val, has := c.prj2SpecialImgs[pipelineRun.Pipeline.ProjectID]; has {
			registryUrl = val.DefaultUrl
		}

		params = []v1beta1.Param{
			{Name: "app-name", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: pipelineRun.Pipeline.AppName}},
			{Name: "registry-url", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: registryUrl}},
			{Name: "timeStamp", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: time.Now().Format("20060102150405")}},
			{Name: "PROJECT_ID", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: strconv.FormatInt(pipelineRun.Pipeline.ProjectID, 10)}},
		}
		if len(buildArgs) > 0 {
			params = append(params, v1beta1.Param{Name: "EXTRA_ARGS", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeArray, ArrayVal: buildArgs}})
		} else {
			params = append(params, v1beta1.Param{Name: "EXTRA_ARGS", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeArray, ArrayVal: []string{}}})
		}
		if pushImage.BuildWay == constants.GET_DOCKER_FILE_FROM_REPO {
			pushImageFilePath := utils.ApplyScriptParams(pushImage.FilePath, pipelineRun)
			params = append(params, v1beta1.Param{Name: "DOCKERFILE", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: pushImageFilePath}})
		} else {
			pushImageDockerFile := utils.ApplyScriptParams(pushImage.DockerFile, pipelineRun)
			params = append(params, v1beta1.Param{Name: "DOCKERFILE_CONTENT", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: pushImageDockerFile}})
		}
		if !pushImage.CacheBaseImage {
			params = append(params, v1beta1.Param{Name: "BUILD_CACHE", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "false"}})
		}
		if pushImage.EnableOCI != nil {
			params = append(params, v1beta1.Param{Name: "EnableOCI", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: strconv.FormatBool(*pushImage.EnableOCI)}})
		}
	case constants.TASK_SCA_SCAN:
		var scaScan model.SCASCAN
		if err := json.Unmarshal(t.Config, &scaScan); err != nil {
			return nil, errors.Wrap(bizerr.ErrConfigUnmarshal, err.Error())
		}
		params = []v1beta1.Param{
			{Name: "BUILD_PATH", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: pipelineRun.Pipeline.BuildPath}},
			{Name: "APP_NAME", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: extraParams.AppName}},
			{Name: "CMDB_ID", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: extraParams.CmdbID}},
			{Name: "BUILD_NUMBER", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: strconv.FormatInt(pipelineRun.BuildNumber, 10)}},
			{Name: "PROJECT_NAME", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: extraParams.ProjectIdentity}},
		}
		if scaScan.ImageAddress != "" {
			params = append(params, v1beta1.Param{Name: "IMAGE_ADDRESS", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: scaScan.ImageAddress}})
		}
	default:
		log.Infof("genearte tekton PipelineRun task %s not support yet", t.Type)
	}
	return params, nil
}
