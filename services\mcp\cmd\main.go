package main

import (
	"fmt"
	"strings"

	cloudagg "52tt.com/cicd/pkg/cloud/aggregate"
	"52tt.com/cicd/pkg/engine"
	"52tt.com/cicd/pkg/engine/middleware"
	localServer "52tt.com/cicd/pkg/server"
	"52tt.com/cicd/protocol/app"
	"52tt.com/cicd/protocol/iam"
	"52tt.com/cicd/protocol/pipeline"
	"52tt.com/cicd/services/mcp/internal/conf"
	"52tt.com/cicd/services/mcp/internal/service/del_ser"
	"52tt.com/cicd/services/mcp/internal/service/find"
	"52tt.com/cicd/services/mcp/internal/service/restart"
	"52tt.com/cicd/services/mcp/internal/service/run_ser"
	"52tt.com/cicd/services/mcp/internal/service/upd_ser"
	"52tt.com/cicd/services/mcp/rest"
	"github.com/gin-gonic/gin"
	"github.com/mark3labs/mcp-go/server"
	"github.com/spf13/pflag"
)

const (
	APIMcp = "/mcp"
)

func run(configPath string) error {
	c, err := conf.LoadConfig(configPath)

	if err != nil {
		return err
	}
	local := engine.NewEngine(c, &engine.EngineOptions{})
	local.Use(middleware.CORS())
	if err := local.Init(); err != nil {
		return err
	}

	mcpSer := runMCP(local, c)

	pipelineCC, _ := localServer.NewRpcClient(c.GetRegistry().PipelineRpcUrl)
	psClient := pipeline.NewPipelineServiceClient(pipelineCC)

	iamCC, _ := localServer.NewRpcClient(c.GetRegistry().IamRpcUrl)
	iamClient := iam.NewUserServiceClient(iamCC)

	appGrpcClient, _ := localServer.NewRpcClient(c.GetRegistry().AppRpcUrl)
	appClient := app.NewAppServiceClient(appGrpcClient)

	cloudAggCli, err := cloudagg.NewAggClient(&cloudagg.AggCfg{
		HTTPTarget:       conf.MCPSerConfig.Cloud.Host,
		GRPCTarget:       conf.MCPSerConfig.Cloud.GrpcTarget,
		DeployGRPCTarget: conf.MCPSerConfig.Cloud.DeployGrpcTarget,
		Token:            conf.MCPSerConfig.Cloud.Token,
	})
	if err != nil {
		return err
	}

	run_ser.InitAgg(psClient, iamClient, appClient)
	restart.InitAgg(cloudAggCli)
	find.InitAgg(cloudAggCli)
	del_ser.InitAgg(cloudAggCli)
	upd_ser.InitAgg(cloudAggCli, iamClient)

	rest.Register(mcpSer)

	helloWorldController := rest.NewHelloWorldController()
	local.Register(
		helloWorldController.Route,
	)

	svr := localServer.NewServer(local, c)

	if err := svr.Run(); err != nil {
		return fmt.Errorf("start service error: %v", err)
	}
	return nil
}

func runMCP(engine *engine.LocalEngine, c *conf.Config) (mcpServer *server.MCPServer) {
	mcpServer = server.NewMCPServer("Infra Platform Open MCP", "0.1.0")
	sseServer := server.NewSSEServer(mcpServer, c.MCP.BaseURL+APIMcp)

	mcp := engine.Engine.Group(APIMcp)
	mcp.Use(func(ginCtx *gin.Context) {
		ginCtx.Request.URL.Path = strings.Replace(ginCtx.Request.URL.Path, APIMcp, "", 1)
		sseServer.ServeHTTP(ginCtx.Writer, ginCtx.Request)
	})
	mcp.GET("*any", func(ginCtx *gin.Context) {})
	mcp.POST("*any", func(ginCtx *gin.Context) {})
	return
}

func main() {
	var configPath string

	pflag.StringVar(&configPath, "s", "services/mcp/etc/", "path of application setting file")
	pflag.Parse()
	if err := run(configPath); err != nil {
		panic(err)
	}
}
