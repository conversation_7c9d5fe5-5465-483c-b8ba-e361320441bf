package qyxn

import (
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/protocol/deploy"
	"52tt.com/cicd/services/migrate/internal/app"
	"52tt.com/cicd/services/migrate/internal/deploy_config"
	"52tt.com/cicd/services/migrate/internal/deploy_config/common"
	db "52tt.com/cicd/services/migrate/pkg/database"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
)

// general-server chart包配置导出

func ExportAllConfig(envs []string, services []string, isUpdate bool, targetProjectName string) error {
	for _, env := range envs {
		err := ExportConfig(env, services, isUpdate, targetProjectName)
		if err != nil {
			return err
		}
	}
	return nil
}

func ExportConfig(env string, services []string, isUpdate bool, targetProjectName string) error {
	k8sMap := map[string][]int{
		db.Dev:  {24, 26, 27},
		db.Test: {169, 170, 171},
		//db.Prod: {54, 56, 58},
		db.Prod: {},
	}
	var configs []deploy_config.UnitDeployConfig
	deployConfigs, err := deploy_config.GetProjectDeployConfig(env, k8sMap)
	if err != nil {
		return err
	}
	for _, dc := range deployConfigs {
		if dc.Unit.App.Team.Name != "应用线-企业效能" || dc.Unit.Status != "online" {
			continue
		}
		if !isContainUnit(dc.Unit.Name, services) && len(services) > 0 {
			continue
		}
		configs = append(configs, deploy_config.UnitDeployConfig{
			Resource:       dc.Resource,
			Values:         dc.Values,
			Default:        dc.Default,
			K8sName:        dc.K8sEnv.AssetsK8sCluster.Name,
			NameSpace:      dc.K8sEnv.NameSpace,
			K8sDescription: dc.K8sEnv.AssetsK8sCluster.Description,
			K8sPrivateId:   dc.K8sEnv.AssetsK8sCluster.PrivateId,
			UnitName:       dc.Unit.Name,
			ObjectId:       dc.Unit.ObjectId,
			TeamName:       dc.Unit.App.Team.Name,
			ChartName:      dc.Chart.Name,
			ChartUrl:       dc.Chart.Url,
			ChartVersion:   dc.Chart.Version,
			ChartType:      dc.Chart.CharType,
			UnitId:         dc.UnitId,
			K8sEnvId:       dc.K8sEnvId,
		})
	}

	subEnvK8sMap := map[string][]int{
		db.Dev:  {},
		db.Test: {46, 35, 36, 190, 288, 294},
		db.Prod: {},
	}
	subEnvDeployConfigs, err := deploy_config.GetProjectSubDeployConfig(env, subEnvK8sMap)
	if err != nil {
		return err
	}
	for _, dc := range subEnvDeployConfigs {
		if dc.Unit.App.Team.Name != "应用线-企业效能" || dc.Unit.Status != "online" {
			continue
		}
		if len(services) > 0 && !isContainUnit(dc.Unit.Name, services) {
			continue
		}
		configs = append(configs, deploy_config.UnitDeployConfig{
			Resource:       dc.Resource,
			Values:         dc.Values,
			Default:        dc.Default,
			K8sName:        dc.SubEnv.BaseEnv.AssetsK8sCluster.Name,
			NameSpace:      dc.SubEnv.NameSpace,
			K8sDescription: dc.SubEnv.Description,
			K8sPrivateId:   dc.SubEnv.BaseEnv.AssetsK8sCluster.PrivateId,
			UnitName:       dc.Unit.Name,
			ObjectId:       dc.Unit.ObjectId,
			TeamName:       dc.Unit.App.Team.Name,
			ChartName:      dc.Chart.Name,
			ChartUrl:       dc.Chart.Url,
			ChartVersion:   dc.Chart.Version,
			ChartType:      dc.Chart.CharType,
			UnitId:         dc.UnitId,
			K8sEnvId:       dc.SubEnvId,
		})
	}

	//err = deploy_config.CreateConfig(configs)
	err = PullConfig(configs, isUpdate, targetProjectName)
	if err != nil {
		return err
	}
	return nil
}

type hpaConfig struct {
	configType string
	chart      HpaChart
}

func PullConfig(configs []deploy_config.UnitDeployConfig, isUpdate bool, targetProjectName string) error {
	dbEnv := db.Prod // 同步环境
	//dbEnv := db.Test

	hpaMap := make(map[string]hpaConfig)
	for _, conf := range configs {
		var tempHpa HpaChart
		if conf.Resource == "HPA" || conf.Resource == "MULTI-HPA" {
			_ = json.Unmarshal(conf.Values, &tempHpa)
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			hpaMap[unitKey] = hpaConfig{
				configType: conf.Resource,
				chart:      tempHpa,
			}
		}
	}
	log.Debugf("hpaMap: %v", hpaMap)

	count := 0
	var successApps []string
	for _, conf := range configs {
		if conf.ChartName == "general-server" {
			var err error
			// 处理数据导入转换逻辑
			log.Debugf("unitId: %v, unitName: %v", conf.UnitId, conf.UnitName)
			var valueConfig WorkloadChart
			_ = json.Unmarshal(conf.Values, &valueConfig)
			var defaultValueConfig DefaultValue
			_ = json.Unmarshal(conf.Default, &defaultValueConfig)
			if (valueConfig.Service.Enabled && valueConfig.Service.Type == "ClusterIP") || !valueConfig.Service.Enabled {
				continue
			}
			log.Infof("[qyxn] unitName: %v, serviceType: %v", conf.UnitName, valueConfig.Service.Type)

			appBaseConfig, err := HandAppBaseConfig(valueConfig, defaultValueConfig)
			if err != nil {
				return err
			}
			appAdvancedConfig, err := HandAppAdvancedConfig(valueConfig)
			if err != nil {
				return err
			}
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			traitConfig, err := HandTraitConfig(valueConfig, unitKey, hpaMap)
			if err != nil {
				return err
			}

			// 数据库操作：查询app、查询/创建deploy_metadata、创建deploy_config、更新app
			// 1.查询app
			teamName := conf.TeamName
			if targetProjectName != "" {
				teamName = targetProjectName
			}
			appId, err := common.FindAppIdBy(dbEnv, teamName, conf.UnitName)
			if err != nil {
				return err
			}
			if appId == 0 {
				log.Debugf("app name: %v is not exist", conf.UnitName)
				continue
			}

			// 2.查询/创建deploy_metadata
			metadata := deploy_config.DeployMetadata{
				Env:       getEnvEnum(conf.K8sName),
				EnvTarget: getEnvTarget(conf.NameSpace),
				Cluster:   getClusterName(conf.K8sName),
				Namespace: conf.NameSpace,
				AppID:     appId,
				AppName:   conf.UnitName,
				ConfigID:  0,
			}
			err = deploy_config.GetDeployConfigLastVersion(dbEnv, &metadata)
			if err != nil {
				return err
			}
			if metadata.Config != nil && metadata.Config.CreatedBy != 71 {
				log.Infof("has existed config, unitId: %v, unitName: %v, createdBy: %v", conf.UnitId, conf.UnitName, metadata.Config.CreatedByChineseName)
			}
			// 只验证数据转换是否可行，不更新
			if !isUpdate {
				log.Infof("ignore update, unitId: %v, unitName: %v", conf.UnitId, conf.UnitName)
				continue
			}
			version := 1
			if metadata.ID != 0 {
				if metadata.Config != nil {
					//version = metadata.Config.Version + 1
					continue
				}
				if metadata.Config.CreatedBy != 71 {
					log.Infof("ignore update, unitId: %v, unitName: %v, createdBy: %v", conf.UnitId, conf.UnitName, metadata.Config.CreatedByChineseName)
					continue
				}
			} else {
				err = deploy_config.CreateDeployMetadata(dbEnv, &metadata)
				if err != nil {
					return err
				}
			}

			// 3.创建deploy_config
			deployConfig := deploy_config.DeployConfig{
				AppBasicConfig:       appBaseConfig,
				AppAdvancedConfig:    appAdvancedConfig,
				TraitConfig:          traitConfig,
				Version:              version,
				CreatedBy:            271, //创建用户id
				CreatedByChineseName: "陈健浒",
				CreatedByEmployeeNo:  "T3380",
				TemplateID:           getTemplateId(int64(metadata.Env)),
				MetadataID:           metadata.ID,
			}
			deployConfig.ConfigType = 1
			err = deploy_config.CreateDeployConfig(dbEnv, &deployConfig)
			if err != nil {
				return err
			}
			metadata.ConfigID = deployConfig.ID
			err = deploy_config.UpdateDeployMetadata(dbEnv, &metadata)
			if err != nil {
				return err
			}

			// 4.更新matchLabels
			matchLabels := make(map[string]string)
			serviceLabels := make(map[string]string)
			matchLabels["app.kubernetes.io/instance"] = conf.UnitName
			matchLabels["app.kubernetes.io/name"] = conf.UnitName
			serviceLabels["app.kubernetes.io/instance"] = conf.UnitName
			serviceLabels["app.kubernetes.io/name"] = conf.UnitName
			params := app.UpdateLabelsParameter{
				AppId:         int(appId),
				MatchLabels:   matchLabels,
				ServiceLabels: serviceLabels,
			}
			err = app.UpgradeAppLabels(dbEnv, &params)
			if err != nil {
				return err
			}

			successApps = append(successApps, conf.UnitName)
			count++
		}
	}
	log.Infof("wefly-finish:%d", count)
	log.Infof("wefly-finish-list: %+v", successApps)
	return nil
}

func HandAppBaseConfig(config WorkloadChart, defaultConfig DefaultValue) ([]byte, error) {
	res := deploy.AppBasicConfig{
		NetworkType:  "",
		NetworkPorts: []*deploy.AppBasicConfig_Port{},
		Annotations:  []*deploy.Pair{},
		Envs:         []*deploy.Pair{},
		Commands:     []string{},
		Configs:      []*deploy.AppBasicConfig_Config{},
	}

	// 网络配置-服务协议和端口
	if config.Service.Enabled {
		res.NetworkType = config.Service.Type
		serviceName := config.Service.Name
		if len(serviceName) >= 15 || serviceName == "" {
			serviceName = "service-0"
		}
		res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
			Name:         serviceName,
			InternalPort: config.Service.TargetPort,
			ExternalPort: config.Service.Port,
		})
	}
	// 网络配置-多端口
	if config.TempNamespace.Service.MultiPorts.Enabled {
		count := 1
		for _, port := range config.Service.MultiPorts {
			multiSvcName := port.Name
			if len(multiSvcName) >= 15 {
				multiSvcName = fmt.Sprintf("service-%d", count)
				count++
			}
			res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
				Name:         multiSvcName,
				InternalPort: port.TargetPort,
				ExternalPort: port.Port,
			})
		}
	}
	// 环境变量
	envMap := make(map[string]string, 0)
	// 先处理value值
	if config.TempNamespace.Deploy.Env.Enabled {
		for _, env := range config.Deploy.Env {
			if env.Name != "" && env.Value != "" {
				envMap[env.Name] = env.Value
			}
		}
	}
	// 再处理default值
	for _, env := range defaultConfig.Global.Deploy.Env {
		if env.Name != "" && env.Value != "" {
			envMap[env.Name] = env.Value
		}
	}
	var keys []string
	for key := range envMap {
		keys = append(keys, key)
	}
	// 对环境变量的key进行排序
	sort.Strings(keys)
	for _, key := range keys {
		res.Envs = append(res.Envs, &deploy.Pair{
			Key:   key,
			Value: envMap[key],
		})
	}
	// 命令参数-执行命令
	for _, command := range config.Deploy.Command {
		res.Commands = append(res.Commands, command)
	}
	// 配置文件
	// 配置文件名称用户自定义
	cmName := config.ConfigMapName
	if cmName != "" {
		dirPath := ""
		volumeName := ""
		for _, volume := range config.Deploy.Volumes {
			if volume.ConfigMap.Name == cmName {
				volumeName = volume.Name
				break
			}
		}
		for _, mount := range config.Deploy.VolumeMounts {
			if volumeName != "" && mount.Name == volumeName {
				dirPath = mount.MountPath
				break
			}
		}
		// 挂载路径不为空才添加配置文件
		if dirPath != "" {
			for fileName, fileContent := range config.ConfigFiles {
				newDirPath := strings.Replace(dirPath, fileName, "", 1)
				res.Configs = append(res.Configs, &deploy.AppBasicConfig_Config{DirPath: newDirPath, FileName: fileName, Content: fileContent})
			}
		}
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func builderHandler() *deploy.HealthCheck_HealthCheckHandler {
	return &deploy.HealthCheck_HealthCheckHandler{
		Type: "",
		Exec: &deploy.ExecAction{
			Command: []string{},
		},
		TcpSocket: &deploy.TCPSocketAction{},
		HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
		Grpc:      &deploy.GRPCAction{},
		Probe: &deploy.HealthCheck_Probe{
			InitialDelaySeconds: 0,
			PeriodSeconds:       10,
			TimeoutSeconds:      1,
			SuccessThreshold:    1,
			FailureThreshold:    3,
		},
	}
}

func HandAppAdvancedConfig(config WorkloadChart) ([]byte, error) {
	res := deploy.AppAdvancedConfig{
		Labels:      []*deploy.Pair{},
		Annotations: []*deploy.Pair{},
		HostAliases: []*deploy.HostAlias{},
		HealthCheck: &deploy.HealthCheck{
			ReadinessProbe: builderHandler(),
			LivenessProbe:  builderHandler(),
			StartupProbe:   builderHandler(),
			Types:          []string{},
		},
		ServiceAccountName: "default",
		Lifecycle: &deploy.Lifecycle{
			PostStart: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			PreStop: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			Types: []string{},
		},
	}

	expectedLabel := map[string]bool{
		"uuid":       true,
		"cluster_id": true,
		"env":        true,
		"lang":       true,
		"type":       true,
	}
	// label 标签
	for key, value := range config.Labels {
		if _, ok := expectedLabel[key]; ok {
			continue
		}
		res.Labels = append(res.Labels, &deploy.Pair{Key: key, Value: value})
	}
	// 注解
	for key, value := range config.Deploy.Annotations {
		// 监控注解
		if key == "prometheus.io/path" ||
			key == "prometheus.io/port" ||
			key == "prometheus.io/scrape" {
			continue
		}
		if key == "sidecar.istio.io/proxyCPU" || key == "sidecar.istio.io/proxyMemory" ||
			key == "sidecar.istio.io/proxyCPULimit" || key == "sidecar.istio.io/proxyMemoryLimit" {
			continue
		}
		res.Annotations = append(res.Annotations, &deploy.Pair{
			Key:   key,
			Value: value,
		})
	}
	// 主机别名
	if config.TempNamespace.HostAliases.Enabled {
		for _, host := range config.Deploy.HostAliases {
			for _, name := range host.Hostnames {
				if name != "" && host.IP != "" {
					res.HostAliases = append(res.HostAliases, &deploy.HostAlias{
						Domain: name,
						Ip:     host.IP,
					})
				}
			}
		}
	}

	//健康检查-就绪探针
	if config.TempNamespace.Deploy.ReadinessProbe.Enabled {
		res.HealthCheck.Types = append(res.HealthCheck.Types, "readinessProbe")
		if len(config.Deploy.ReadinessProbe.Exec.Command) > 0 {
			res.HealthCheck.ReadinessProbe.Type = "exec"
			res.HealthCheck.ReadinessProbe.Exec = &deploy.ExecAction{}
			for _, cmd := range config.Deploy.ReadinessProbe.Exec.Command {
				res.HealthCheck.ReadinessProbe.Exec.Command = append(res.HealthCheck.ReadinessProbe.Exec.Command, cmd)
			}
		} else if config.Deploy.ReadinessProbe.TCPSocket.Port > 0 {
			res.HealthCheck.ReadinessProbe.Type = "tcpSocket"
			res.HealthCheck.ReadinessProbe.TcpSocket = &deploy.TCPSocketAction{
				Port: config.Deploy.ReadinessProbe.TCPSocket.Port,
			}
		} else if config.Deploy.ReadinessProbe.HTTPGet.Port > 0 {
			res.HealthCheck.ReadinessProbe.Type = "httpGet"
			res.HealthCheck.ReadinessProbe.HttpGet = &deploy.HTTPGetAction{
				Path: config.Deploy.ReadinessProbe.HTTPGet.Path,
				Port: config.Deploy.ReadinessProbe.HTTPGet.Port,
			}
			for _, header := range config.Deploy.ReadinessProbe.HTTPGet.HTTPHeaders {
				res.HealthCheck.ReadinessProbe.HttpGet.Headers = append(res.HealthCheck.ReadinessProbe.HttpGet.Headers,
					&deploy.Pair{Key: header.Name, Value: header.Value})
			}
		}

		res.HealthCheck.ReadinessProbe.Probe = &deploy.HealthCheck_Probe{}
		// 探测间隔
		readnessPeriodSeconds := config.Deploy.ReadinessProbe.PeriodSeconds
		if readnessPeriodSeconds > 0 {
			res.HealthCheck.ReadinessProbe.Probe.PeriodSeconds = readnessPeriodSeconds
		} else {
			res.HealthCheck.ReadinessProbe.Probe.PeriodSeconds = 10
		}
		// 启动延时
		readnessInitialDelaySeconds := config.Deploy.ReadinessProbe.InitialDelaySeconds
		if readnessInitialDelaySeconds > 0 {
			res.HealthCheck.ReadinessProbe.Probe.InitialDelaySeconds = readnessInitialDelaySeconds
		} else {
			res.HealthCheck.ReadinessProbe.Probe.InitialDelaySeconds = 3
		}
		// 超时时长
		readnessTimeout := config.Deploy.ReadinessProbe.TimeoutSeconds
		if readnessTimeout > 0 {
			res.HealthCheck.ReadinessProbe.Probe.TimeoutSeconds = readnessTimeout
		} else {
			res.HealthCheck.ReadinessProbe.Probe.TimeoutSeconds = 2
		}
		// 成功阈值
		readnessSuccessThreshold := config.Deploy.ReadinessProbe.SuccessThreshold
		if readnessSuccessThreshold > 0 {
			res.HealthCheck.ReadinessProbe.Probe.SuccessThreshold = readnessSuccessThreshold
		} else {
			res.HealthCheck.ReadinessProbe.Probe.SuccessThreshold = 1
		}
		// 失败阈值
		readnessFailureThreshold := config.Deploy.ReadinessProbe.FailureThreshold
		if readnessFailureThreshold > 0 {
			res.HealthCheck.ReadinessProbe.Probe.FailureThreshold = readnessFailureThreshold
		} else {
			res.HealthCheck.ReadinessProbe.Probe.FailureThreshold = 2
		}
	}

	//健康检查-存活探针
	if config.TempNamespace.Deploy.LivenessProbe.Enabled {
		res.HealthCheck.Types = append(res.HealthCheck.Types, "livenessProbe")
		if len(config.Deploy.LivenessProbe.Exec.Command) > 0 {
			res.HealthCheck.LivenessProbe.Type = "exec"
			res.HealthCheck.LivenessProbe.Exec = &deploy.ExecAction{}
			for _, cmd := range config.Deploy.LivenessProbe.Exec.Command {
				res.HealthCheck.LivenessProbe.Exec.Command = append(res.HealthCheck.LivenessProbe.Exec.Command, cmd)
			}
		} else if config.Deploy.LivenessProbe.TCPSocket.Port > 0 {
			res.HealthCheck.LivenessProbe.Type = "tcpSocket"
			res.HealthCheck.LivenessProbe.TcpSocket = &deploy.TCPSocketAction{
				Port: config.Deploy.LivenessProbe.TCPSocket.Port,
			}
		} else if config.Deploy.LivenessProbe.HTTPGet.Port > 0 {
			res.HealthCheck.LivenessProbe.Type = "httpGet"
			res.HealthCheck.LivenessProbe.HttpGet = &deploy.HTTPGetAction{
				Path: config.Deploy.LivenessProbe.HTTPGet.Path,
				Port: config.Deploy.LivenessProbe.HTTPGet.Port,
			}
			for _, header := range config.Deploy.LivenessProbe.HTTPGet.HTTPHeaders {
				res.HealthCheck.LivenessProbe.HttpGet.Headers = append(res.HealthCheck.LivenessProbe.HttpGet.Headers,
					&deploy.Pair{Key: header.Name, Value: header.Value})
			}
		}

		res.HealthCheck.LivenessProbe.Probe = &deploy.HealthCheck_Probe{}
		// 探测间隔
		livenessPeriodSeconds := config.Deploy.LivenessProbe.PeriodSeconds
		if livenessPeriodSeconds > 0 {
			res.HealthCheck.LivenessProbe.Probe.PeriodSeconds = int32(livenessPeriodSeconds)
		} else {
			res.HealthCheck.LivenessProbe.Probe.PeriodSeconds = 5
		}
		// 启动延时
		livenessInitialDelaySeconds := config.Deploy.LivenessProbe.InitialDelaySeconds
		if livenessInitialDelaySeconds > 0 {
			res.HealthCheck.LivenessProbe.Probe.InitialDelaySeconds = int32(livenessInitialDelaySeconds)
		} else {
			res.HealthCheck.LivenessProbe.Probe.InitialDelaySeconds = 30
		}
		// 超时时长
		livenessTimeout := config.Deploy.LivenessProbe.TimeoutSeconds
		if livenessTimeout > 0 {
			res.HealthCheck.LivenessProbe.Probe.TimeoutSeconds = int32(livenessTimeout)
		} else {
			res.HealthCheck.LivenessProbe.Probe.TimeoutSeconds = 2
		}
		// 成功阈值
		livenessSuccessThreshold := config.Deploy.LivenessProbe.SuccessThreshold
		if livenessSuccessThreshold > 0 {
			res.HealthCheck.LivenessProbe.Probe.SuccessThreshold = int32(livenessSuccessThreshold)
		} else {
			res.HealthCheck.LivenessProbe.Probe.SuccessThreshold = 1
		}
		// 失败阈值
		livenessFailureThreshold := config.Deploy.LivenessProbe.FailureThreshold
		if livenessFailureThreshold > 0 {
			res.HealthCheck.LivenessProbe.Probe.FailureThreshold = int32(livenessFailureThreshold)
		} else {
			res.HealthCheck.LivenessProbe.Probe.FailureThreshold = 10
		}
	}

	// 挂载配置
	res.MountConfig = &deploy.MountConfig{
		Volumes:      []*deploy.Volume{},
		VolumeMounts: []*deploy.VolumeMount{},
	}

	cmVolumeName := ""
	if config.TempNamespace.Deploy.Volumes.Enabled {
		for _, volume := range config.Deploy.Volumes {
			if volume.ConfigMap.Name != "" &&
				config.ConfigMapName != "" &&
				volume.ConfigMap.Name == config.ConfigMapName {
				cmVolumeName = volume.Name
				continue
			}
			// emptyDir
			if volume.Name == "skywalking-agent" || volume.Name == "sw-agent" || volume.Name == "opentelemetry-agent" || volume.Name == "iast-agent" {
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "emptyDir",
					ReadOnly:   false,
					VolumeName: volume.Name,
				})
				continue
			}
			// configMap
			if volume.ConfigMap.Name != config.ConfigMapName {
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "configMap",
					VolumeName: volume.Name,
					RefName:    volume.ConfigMap.Name,
				})
				continue
			}
			// hostPath
			if volume.HostPath.Path != "" {
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "hostPath",
					VolumeName: volume.Name,
					ReadOnly:   true,
					RefName:    volume.HostPath.Path,
				})
			}
		}
	}
	if config.TempNamespace.Deploy.VolumeMounts.Enabled {
		for _, volume := range config.Deploy.VolumeMounts {
			// 挂载目录名称不等于configMapName才添加
			if volume.Name != cmVolumeName {
				res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
					VolumeName: volume.Name,
					MountPoint: volume.MountPath,
					SubPath:    volume.SubPath,
				})
			}
		}
	}

	// lifecycle
	if config.TempNamespace.Deploy.Lifecycle.Enabled {
		isPreStop := true
		if len(config.Deploy.Lifecycle.PreStop.Exec.Command) > 0 {
			res.Lifecycle.PreStop.Type = "exec"
			res.Lifecycle.PreStop.Exec = &deploy.ExecAction{}
			for _, cmd := range config.Deploy.Lifecycle.PreStop.Exec.Command {
				res.Lifecycle.PreStop.Exec.Command = append(res.Lifecycle.PreStop.Exec.Command, cmd)
			}
		} else if config.Deploy.Lifecycle.PreStop.TCPSocket.Port > 0 {
			res.Lifecycle.PreStop.Type = "tcpSocket"
			res.Lifecycle.PreStop.TcpSocket = &deploy.TCPSocketAction{
				Port: config.Deploy.Lifecycle.PreStop.TCPSocket.Port,
			}
		} else if config.Deploy.Lifecycle.PreStop.HttpGet.Port > 0 {
			res.Lifecycle.PreStop.Type = "httpGet"
			res.Lifecycle.PreStop.HttpGet = &deploy.HTTPGetAction{
				Path:   config.Deploy.Lifecycle.PreStop.HttpGet.Path,
				Port:   config.Deploy.Lifecycle.PreStop.HttpGet.Port,
				Scheme: config.Deploy.Lifecycle.PreStop.HttpGet.Scheme,
				Host:   "localhost",
			}
		} else {
			isPreStop = false
		}
		// 使用preStop
		if isPreStop {
			res.Lifecycle.Types = append(res.Lifecycle.Types, "preStop")
		}
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func HandTraitConfig(config WorkloadChart, unitKey string, hpaMap map[string]hpaConfig) ([]byte, error) {
	var res deploy.TraitConfig
	// 资源限制
	res.ResourceConstraints = &deploy.ResourceConstraints{Resources: &deploy.Resources{}, Sidecar: &deploy.ResourceConstraints_Sidecar{}}
	cpuReq := config.Deploy.Resources.Requests.CPU
	if strings.Contains(cpuReq, "m") || strings.Contains(cpuReq, "M") {
		//单位为m
		reqCpu, _ := strconv.ParseFloat(cpuReq[0:len(cpuReq)-1], 32)
		res.ResourceConstraints.Resources.RequestCpu = float32(reqCpu / 1000)
	} else {
		//单位为核
		reqCpu, _ := strconv.ParseFloat(cpuReq, 32)
		res.ResourceConstraints.Resources.RequestCpu = float32(reqCpu)
	}

	memReq, _ := strconv.ParseFloat(config.Deploy.Resources.Requests.Memory[0:len(config.Deploy.Resources.Requests.Memory)-2], 32)
	if strings.Contains(config.Deploy.Resources.Requests.Memory, "Gi") {
		res.ResourceConstraints.Resources.RequestMemory = float32(memReq * 1024)
	} else {
		res.ResourceConstraints.Resources.RequestMemory = float32(memReq)
	}

	cpuLim := config.Deploy.Resources.Limits.CPU
	if strings.Contains(cpuLim, "m") || strings.Contains(cpuLim, "M") {
		//单位为m
		limCpu, _ := strconv.ParseFloat(cpuLim[0:len(cpuLim)-1], 32)
		res.ResourceConstraints.Resources.LimitCpu = float32(limCpu / 1000)
	} else {
		//单位为核
		limCpu, _ := strconv.ParseFloat(cpuLim, 32)
		res.ResourceConstraints.Resources.LimitCpu = float32(limCpu)
	}

	limMem, _ := strconv.ParseFloat(config.Deploy.Resources.Limits.Memory[0:len(config.Deploy.Resources.Limits.Memory)-2], 32)
	if strings.Contains(config.Deploy.Resources.Limits.Memory, "Gi") {
		res.ResourceConstraints.Resources.LimitMemory = float32(limMem * 1024)
	} else {
		res.ResourceConstraints.Resources.LimitMemory = float32(limMem)
	}

	//sidecar注入
	var reqCpu, reqMem1, limCpu, limMem1 float64
	var has bool
	for key, _ := range config.Deploy.Annotations {
		if key == "sidecar.istio.io/proxyCPU" || key == "sidecar.istio.io/proxyMemory" ||
			key == "sidecar.istio.io/proxyCPULimit" || key == "sidecar.istio.io/proxyMemoryLimit" {
			has = true
		}
	}
	//cpu
	if value, ok := config.Deploy.Annotations["sidecar.istio.io/proxyCPU"]; ok {
		if strings.Contains(value, "m") {
			cpu, _ := strconv.ParseFloat(value[0:len(value)-1], 32)
			reqCpu = cpu / 1000
		} else {
			reqCpu, _ = strconv.ParseFloat(value, 32)
		}
	} else if has {
		reqCpu = 0.1
	}
	//memory
	if value, ok := config.Deploy.Annotations["sidecar.istio.io/proxyMemory"]; ok {
		reqMem1, _ = strconv.ParseFloat(value[0:len(value)-2], 32)
		if strings.Contains(value, "Gi") {
			reqMem1 = reqMem1 * 1024
		}
	} else if has {
		reqMem1 = 400
	}
	//cpuLimit
	if value, ok := config.Deploy.Annotations["sidecar.istio.io/proxyCPULimit"]; ok {
		if strings.Contains(value, "m") {
			cpu, _ := strconv.ParseFloat(value[0:len(value)-1], 32)
			limCpu = cpu / 1000
		} else {
			limCpu, _ = strconv.ParseFloat(value, 32)
		}
	} else if has {
		limCpu = 1
	}
	//memoryLimit
	if value, ok := config.Deploy.Annotations["sidecar.istio.io/proxyMemoryLimit"]; ok {
		limMem1, _ = strconv.ParseFloat(value[0:len(value)-2], 32)
		if strings.Contains(value, "Gi") {
			limMem1 = limMem1 * 1024
		}
	} else if has {
		limMem1 = 1024
	}
	if has {
		res.ResourceConstraints.Sidecar = &deploy.ResourceConstraints_Sidecar{}
		res.ResourceConstraints.Sidecar.Enabled = true
		res.ResourceConstraints.Sidecar.RequestCpu = float32(reqCpu)
		res.ResourceConstraints.Sidecar.RequestMemory = float32(reqMem1)
		res.ResourceConstraints.Sidecar.LimitCpu = float32(limCpu)
		res.ResourceConstraints.Sidecar.LimitMemory = float32(limMem1)
	}

	// 伸缩配置
	res.ScalingConfig = &deploy.ScalingConfig{
		Hpa:      &deploy.ScalingConfig_HPA{Types: []string{}},
		MultiHpa: &deploy.ScalingConfig_MultiHPA{Types: []string{}, Cron: []*deploy.ScalingConfig_MultiHPA_Cron{}},
	}
	replicas, _ := strconv.Atoi(config.ReplicaCount)
	if replicas > 0 {
		res.ScalingConfig.Replicas = int32(replicas)
	} else {
		res.ScalingConfig.Replicas = 1
	}
	res.ScalingConfig.Type = "replicas"

	// hpa
	if value, isExist := hpaMap[unitKey]; isExist && (value.chart.Autoscale.CPU.Enabled || value.chart.Autoscale.Memory.Enabled) {
		if value.configType == "HPA" {
			hpaVal := value.chart
			res.ScalingConfig.Hpa = &deploy.ScalingConfig_HPA{
				Min:   hpaVal.Autoscale.Min,
				Max:   hpaVal.Autoscale.Max,
				Types: []string{},
			}
			if hpaVal.Autoscale.CPU.Enabled {
				res.ScalingConfig.Hpa.CpuUtilization = hpaVal.Autoscale.CPU.TargetAverageUtilization
				res.ScalingConfig.Hpa.Types = append(res.ScalingConfig.Hpa.Types, "cpu")
			}
			if hpaVal.Autoscale.Memory.Enabled {
				res.ScalingConfig.Hpa.MemoryUtilization = hpaVal.Autoscale.Memory.TargetAverageUtilization
				res.ScalingConfig.Hpa.Types = append(res.ScalingConfig.Hpa.Types, "memory")
			}
			res.ScalingConfig.Type = "hpa"
		} else if value.configType == "MULTI-HPA" {
			hpaVal := value.chart
			res.ScalingConfig.MultiHpa = &deploy.ScalingConfig_MultiHPA{
				Min:   hpaVal.Autoscale.MinReplicaCount,
				Max:   hpaVal.Autoscale.MaxReplicaCount,
				Types: []string{},
				Cron:  []*deploy.ScalingConfig_MultiHPA_Cron{},
			}
			if hpaVal.Autoscale.CPU.Enabled {
				res.ScalingConfig.MultiHpa.CpuUtilization = hpaVal.Autoscale.CPU.Value
				res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "cpu")
			}
			if hpaVal.Autoscale.Memory.Enabled {
				res.ScalingConfig.MultiHpa.MemoryUtilization = hpaVal.Autoscale.Memory.Value
				res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "memory")
			}
			res.ScalingConfig.Type = "multiHPA"
		}
	}

	// 监控指标-MonitorMetrics
	if config.Telemetry.Enabled {
		res.MonitorMetrics = &deploy.MonitorMetrics{
			Enabled:       true,
			Port:          config.Telemetry.MetricsPort,
			MetricsPath:   config.Telemetry.MetricsPath,
			ContainerName: config.Telemetry.MetricsContainer,
		}
	}

	if value, ok := config.Deploy.Annotations["prometheus.io/scrape"]; ok && value == "true" {
		res.MonitorMetrics = &deploy.MonitorMetrics{}
		var (
			portStr     string
			metricsPath string
		)
		for k, v := range config.Deploy.Annotations {
			if k == "prometheus.io/path" {
				metricsPath = v
			}
			if k == "prometheus.io/port" {
				portStr = v
			}
		}
		metricsPort, _ := strconv.Atoi(portStr)
		if metricsPort != 0 && metricsPath != "" {
			res.MonitorMetrics = &deploy.MonitorMetrics{
				Enabled:     true,
				Port:        int32(metricsPort),
				MetricsPath: metricsPath,
			}
		}
	}

	// 升级策略-upgradeStrategy
	res.UpgradeStrategy = &deploy.UpgradeStrategy{
		Enabled:        false,
		MaxSurge:       25,
		MaxUnavailable: 25,
	}

	// 高级配置-advancedConfig
	res.AdvancedConfig = &deploy.TraitAdvancedConfig{
		SchedulingStrategy: []*deploy.SchedulingStrategy{},
		RateLimiting:       &deploy.RateLimiting{},
		CircuitBreaker:     &deploy.CircuitBreaker{},
		InitContainers:     []*deploy.InitContainer{},
		MultiContainers:    []*deploy.MultiContainer{},
	}
	// initContainers
	if config.TempNamespace.Deploy.InitContainers.Enabled {
		for _, c := range config.Deploy.InitContainers {
			imageName := strings.ReplaceAll(c.Image, "swr.cn-north-4.myhuaweicloud.com", "cr.ttyuyin.com")
			if imageName == "swr.cn-south-1.myhuaweicloud.com/ebc/skywalking-agent:8.6.0" {
				imageName = "cr.ttyuyin.com/ebc/skywalking-agent:8.13.0"
			}
			container := &deploy.InitContainer{
				ImageName:     imageName,
				ContainerName: c.Name,
				Commands:      []string{},
				InitMountPath: "",
				AppMountPath:  "",
				Envs:          []*deploy.Pair{},
				VolumeMounts:  []*deploy.VolumeMount{},
				MountName:     "",
			}
			// 执行命令
			for _, cmd := range c.Command {
				container.Commands = append(container.Commands, cmd)
			}
			// 命令参数
			for _, arg := range c.Args {
				container.Commands = append(container.Commands, arg)
			}
			for _, mount := range c.VolumeMounts {
				container.VolumeMounts = append(container.VolumeMounts, &deploy.VolumeMount{
					VolumeName: mount.Name,
					MountPoint: mount.MountPath,
					SubPath:    mount.MountPath,
				})
			}
			res.AdvancedConfig.InitContainers = append(res.AdvancedConfig.InitContainers, container)
		}
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func getEnvEnum(k8sName string) int8 {
	switch k8sName {
	case "k8s-tc-bj-1-dev":
		return int8(1)
	case "k8s-tc-bj-1-test",
		"火山云集群k8s-hs-bj-1-test":
		return int8(2)
	default:
		return 4
	}
}

func getEnvTarget(ns string) int8 {
	switch ns {
	case "wefly-wefly-auto-testing",
		"wefly-wefly-subenv-group1",
		"wefly-wefly-subenv-group2",
		"wefly-wefly-subenv-group3":
		return 2
	default:
		return 1
	}
}

func getClusterName(name string) string {
	switch name {
	case "火山云集群k8s-hs-bj-1-test":
		return "k8s-hs-bj-1-test"
	case "kubeconfig-k8s-hw-bj-1-prod":
		return "k8s-hw-bj-1-prod"
	default:
		return name
	}
}

func getTemplateId(env int64) int64 {
	switch env {
	//case 1, 2, 4:
	//	return 25
	default:
		return 0
	}
}

func isContainUnit(unit string, keys []string) bool {
	var myMap map[string]bool
	myMap = make(map[string]bool)
	for _, key := range keys {
		myMap[key] = true
	}
	if _, ok := myMap[unit]; ok {
		return true
	}
	return false
}
