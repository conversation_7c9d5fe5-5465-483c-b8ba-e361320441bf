// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.28.3
// source: resourcesync.proto

package constack

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SyncUnifiedClusterSidecarReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 集群名, e.g.: k8s-tc-bj-1-yunwei
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间 e.g.: infra
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// sidecar名 e.g.: cloud-enterprise
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncUnifiedClusterSidecarReq) Reset() {
	*x = SyncUnifiedClusterSidecarReq{}
	mi := &file_resourcesync_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncUnifiedClusterSidecarReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncUnifiedClusterSidecarReq) ProtoMessage() {}

func (x *SyncUnifiedClusterSidecarReq) ProtoReflect() protoreflect.Message {
	mi := &file_resourcesync_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncUnifiedClusterSidecarReq.ProtoReflect.Descriptor instead.
func (*SyncUnifiedClusterSidecarReq) Descriptor() ([]byte, []int) {
	return file_resourcesync_proto_rawDescGZIP(), []int{0}
}

func (x *SyncUnifiedClusterSidecarReq) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *SyncUnifiedClusterSidecarReq) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *SyncUnifiedClusterSidecarReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type SyncUnifiedClusterSidecarResp struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Result        []*BatchCopyResourceResult `protobuf:"bytes,1,rep,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncUnifiedClusterSidecarResp) Reset() {
	*x = SyncUnifiedClusterSidecarResp{}
	mi := &file_resourcesync_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncUnifiedClusterSidecarResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncUnifiedClusterSidecarResp) ProtoMessage() {}

func (x *SyncUnifiedClusterSidecarResp) ProtoReflect() protoreflect.Message {
	mi := &file_resourcesync_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncUnifiedClusterSidecarResp.ProtoReflect.Descriptor instead.
func (*SyncUnifiedClusterSidecarResp) Descriptor() ([]byte, []int) {
	return file_resourcesync_proto_rawDescGZIP(), []int{1}
}

func (x *SyncUnifiedClusterSidecarResp) GetResult() []*BatchCopyResourceResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type BatchCopyResourceResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cluster       string                 `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Namespace     string                 `protobuf:"bytes,4,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCopyResourceResult) Reset() {
	*x = BatchCopyResourceResult{}
	mi := &file_resourcesync_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCopyResourceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCopyResourceResult) ProtoMessage() {}

func (x *BatchCopyResourceResult) ProtoReflect() protoreflect.Message {
	mi := &file_resourcesync_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCopyResourceResult.ProtoReflect.Descriptor instead.
func (*BatchCopyResourceResult) Descriptor() ([]byte, []int) {
	return file_resourcesync_proto_rawDescGZIP(), []int{2}
}

func (x *BatchCopyResourceResult) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *BatchCopyResourceResult) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BatchCopyResourceResult) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BatchCopyResourceResult) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *BatchCopyResourceResult) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

var File_resourcesync_proto protoreflect.FileDescriptor

var file_resourcesync_proto_rawDesc = []byte{
	0x0a, 0x12, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x22, 0x6a, 0x0a, 0x1c, 0x53, 0x79, 0x6e, 0x63, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x69, 0x64, 0x65, 0x63, 0x61, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x6e,
	0x0a, 0x1d, 0x53, 0x79, 0x6e, 0x63, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x53, 0x69, 0x64, 0x65, 0x63, 0x61, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x4d, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x97,
	0x01, 0x0a, 0x17, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0xae, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x96, 0x01, 0x0a, 0x19, 0x53, 0x79, 0x6e, 0x63, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x69, 0x64, 0x65, 0x63, 0x61, 0x72, 0x12, 0x3a,
	0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x53, 0x79,
	0x6e, 0x63, 0x55, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x53, 0x69, 0x64, 0x65, 0x63, 0x61, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x3b, 0x2e, 0x71, 0x75, 0x77,
	0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x55, 0x6e,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x69, 0x64, 0x65,
	0x63, 0x61, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x47, 0x5a, 0x45, 0x67, 0x6f, 0x6c,
	0x61, 0x6e, 0x67, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x3b, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_resourcesync_proto_rawDescOnce sync.Once
	file_resourcesync_proto_rawDescData = file_resourcesync_proto_rawDesc
)

func file_resourcesync_proto_rawDescGZIP() []byte {
	file_resourcesync_proto_rawDescOnce.Do(func() {
		file_resourcesync_proto_rawDescData = protoimpl.X.CompressGZIP(file_resourcesync_proto_rawDescData)
	})
	return file_resourcesync_proto_rawDescData
}

var file_resourcesync_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_resourcesync_proto_goTypes = []any{
	(*SyncUnifiedClusterSidecarReq)(nil),  // 0: quwan.cloud.constack.v1alpha.SyncUnifiedClusterSidecarReq
	(*SyncUnifiedClusterSidecarResp)(nil), // 1: quwan.cloud.constack.v1alpha.SyncUnifiedClusterSidecarResp
	(*BatchCopyResourceResult)(nil),       // 2: quwan.cloud.constack.v1alpha.BatchCopyResourceResult
}
var file_resourcesync_proto_depIdxs = []int32{
	2, // 0: quwan.cloud.constack.v1alpha.SyncUnifiedClusterSidecarResp.result:type_name -> quwan.cloud.constack.v1alpha.BatchCopyResourceResult
	0, // 1: quwan.cloud.constack.v1alpha.ResourceSyncService.SyncUnifiedClusterSidecar:input_type -> quwan.cloud.constack.v1alpha.SyncUnifiedClusterSidecarReq
	1, // 2: quwan.cloud.constack.v1alpha.ResourceSyncService.SyncUnifiedClusterSidecar:output_type -> quwan.cloud.constack.v1alpha.SyncUnifiedClusterSidecarResp
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_resourcesync_proto_init() }
func file_resourcesync_proto_init() {
	if File_resourcesync_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_resourcesync_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_resourcesync_proto_goTypes,
		DependencyIndexes: file_resourcesync_proto_depIdxs,
		MessageInfos:      file_resourcesync_proto_msgTypes,
	}.Build()
	File_resourcesync_proto = out.File
	file_resourcesync_proto_rawDesc = nil
	file_resourcesync_proto_goTypes = nil
	file_resourcesync_proto_depIdxs = nil
}
