// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.4
// source: quwan/cloud/constack/v1alpha/kubeconfig.proto

package constack

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	KubeConfigService_GetConfig_FullMethodName   = "/quwan.cloud.constack.v1alpha.KubeConfigService/GetConfig"
	KubeConfigService_FindCluster_FullMethodName = "/quwan.cloud.constack.v1alpha.KubeConfigService/FindCluster"
	KubeConfigService_FindNS_FullMethodName      = "/quwan.cloud.constack.v1alpha.KubeConfigService/FindNS"
)

// KubeConfigServiceClient is the client API for KubeConfigService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type KubeConfigServiceClient interface {
	// get
	GetConfig(ctx context.Context, in *GetConfigRequest, opts ...grpc.CallOption) (*GetConfigResponse, error)
	FindCluster(ctx context.Context, in *ClusterReq, opts ...grpc.CallOption) (*ClusterResp, error)
	FindNS(ctx context.Context, in *NamespaceReq, opts ...grpc.CallOption) (*NamespaceResp, error)
}

type kubeConfigServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewKubeConfigServiceClient(cc grpc.ClientConnInterface) KubeConfigServiceClient {
	return &kubeConfigServiceClient{cc}
}

func (c *kubeConfigServiceClient) GetConfig(ctx context.Context, in *GetConfigRequest, opts ...grpc.CallOption) (*GetConfigResponse, error) {
	out := new(GetConfigResponse)
	err := c.cc.Invoke(ctx, KubeConfigService_GetConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *kubeConfigServiceClient) FindCluster(ctx context.Context, in *ClusterReq, opts ...grpc.CallOption) (*ClusterResp, error) {
	out := new(ClusterResp)
	err := c.cc.Invoke(ctx, KubeConfigService_FindCluster_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *kubeConfigServiceClient) FindNS(ctx context.Context, in *NamespaceReq, opts ...grpc.CallOption) (*NamespaceResp, error) {
	out := new(NamespaceResp)
	err := c.cc.Invoke(ctx, KubeConfigService_FindNS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// KubeConfigServiceServer is the server API for KubeConfigService service.
// All implementations must embed UnimplementedKubeConfigServiceServer
// for forward compatibility
type KubeConfigServiceServer interface {
	// get
	GetConfig(context.Context, *GetConfigRequest) (*GetConfigResponse, error)
	FindCluster(context.Context, *ClusterReq) (*ClusterResp, error)
	FindNS(context.Context, *NamespaceReq) (*NamespaceResp, error)
	mustEmbedUnimplementedKubeConfigServiceServer()
}

// UnimplementedKubeConfigServiceServer must be embedded to have forward compatible implementations.
type UnimplementedKubeConfigServiceServer struct {
}

func (UnimplementedKubeConfigServiceServer) GetConfig(context.Context, *GetConfigRequest) (*GetConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfig not implemented")
}
func (UnimplementedKubeConfigServiceServer) FindCluster(context.Context, *ClusterReq) (*ClusterResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindCluster not implemented")
}
func (UnimplementedKubeConfigServiceServer) FindNS(context.Context, *NamespaceReq) (*NamespaceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNS not implemented")
}
func (UnimplementedKubeConfigServiceServer) mustEmbedUnimplementedKubeConfigServiceServer() {}

// UnsafeKubeConfigServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to KubeConfigServiceServer will
// result in compilation errors.
type UnsafeKubeConfigServiceServer interface {
	mustEmbedUnimplementedKubeConfigServiceServer()
}

func RegisterKubeConfigServiceServer(s grpc.ServiceRegistrar, srv KubeConfigServiceServer) {
	s.RegisterService(&KubeConfigService_ServiceDesc, srv)
}

func _KubeConfigService_GetConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KubeConfigServiceServer).GetConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KubeConfigService_GetConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KubeConfigServiceServer).GetConfig(ctx, req.(*GetConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KubeConfigService_FindCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClusterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KubeConfigServiceServer).FindCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KubeConfigService_FindCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KubeConfigServiceServer).FindCluster(ctx, req.(*ClusterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KubeConfigService_FindNS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KubeConfigServiceServer).FindNS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KubeConfigService_FindNS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KubeConfigServiceServer).FindNS(ctx, req.(*NamespaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

// KubeConfigService_ServiceDesc is the grpc.ServiceDesc for KubeConfigService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var KubeConfigService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "quwan.cloud.constack.v1alpha.KubeConfigService",
	HandlerType: (*KubeConfigServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetConfig",
			Handler:    _KubeConfigService_GetConfig_Handler,
		},
		{
			MethodName: "FindCluster",
			Handler:    _KubeConfigService_FindCluster_Handler,
		},
		{
			MethodName: "FindNS",
			Handler:    _KubeConfigService_FindNS_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "quwan/cloud/constack/v1alpha/kubeconfig.proto",
}
