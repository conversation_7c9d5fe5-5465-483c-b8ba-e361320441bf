package v1

import (
	"context"
	"strconv"
	"time"

	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/pipeline/internal/dao"
	"52tt.com/cicd/services/pipeline/internal/model"
	bizerr "52tt.com/cicd/services/pipeline/pkg/errors"
	"52tt.com/cicd/services/pipeline/pkg/tekton/cdrunner"
)

func makeEmptyTektonPipelineRun(namespace, name string, annotations map[string]string, timeouts map[string]time.Duration) *v1beta1.PipelineRun {
	pr := &v1beta1.PipelineRun{
		ObjectMeta: metav1.ObjectMeta{
			Name:        name,
			Namespace:   namespace,
			Annotations: annotations,
		},
		Spec: v1beta1.PipelineRunSpec{
			Timeouts: &v1beta1.TimeoutFields{
				Pipeline: &metav1.Duration{Duration: timeouts["pipelineTimeouts"]},
				Tasks:    &metav1.Duration{Duration: timeouts["tasksTimeouts"]},
				Finally:  &metav1.Duration{Duration: timeouts["finallyTimeouts"]},
			},
			PipelineSpec: &v1beta1.PipelineSpec{
				Tasks: []v1beta1.PipelineTask{},
			},
		},
	}
	return pr
}

// New 创建的接口逻辑是变更集之前的cd逻辑
func New(prt *dao.PipelineRunTask, pr *dao.PipelineRun, isRetry bool, requestDate string) *DefaultRunner {
	return &DefaultRunner{
		pr:          pr,
		prePRT:      prt,
		isRetry:     isRetry,
		requestDate: requestDate,
	}
}

type DefaultRunner struct {
	// FYI: env type param list "dev", "testing", "preview", "production"
	env string
	// pr need preload Stage, Stage.Task
	pr *dao.PipelineRun
	// prePRT should be the last task of CI stage
	prePRT      *dao.PipelineRunTask
	tektonPR    *v1beta1.PipelineRun
	isRetry     bool
	requestDate string
	// tekton pipeline timeouts config: https://tekton.dev/docs/pipelines/pipelineruns/#configuring-a-failure-timeout
	timeouts map[string]time.Duration
}

func (r *DefaultRunner) SetEnv(env string) {
	r.env = env
}

func (r *DefaultRunner) SetTimeouts(timeouts map[string]time.Duration) {
	r.timeouts = timeouts
}

func (r *DefaultRunner) Build() *v1beta1.PipelineRun {
	r.tektonPR = makeEmptyTektonPipelineRun(r.Namespace(), r.Name(), r.Annotations(), r.timeouts)
	return r.tektonPR
}

func (r *DefaultRunner) Namespace() string {
	return r.pr.TektonNamespace
}

func (r *DefaultRunner) Name() string {
	return makeTektonPipelineRunName(r.env, constants.CDPipelineRun, r.pr.ID)
}

func (r *DefaultRunner) Annotations() map[string]string {
	return map[string]string{
		string(constants.PipelineRunIdKey): strconv.FormatInt(r.pr.ID, 10),
	}
}

func (r *DefaultRunner) Validate() error {
	err := bizerr.ErrPipelineRunDataInvalid
	if r.pr == nil {
		return err
	}
	if r.pr.Pipeline == nil {
		return err
	}
	if len(r.pr.Stages) == 0 {
		return err
	}
	return nil
}

func (r *DefaultRunner) BuildTasks(ctx context.Context) ([]v1beta1.PipelineTask, error) {
	var (
		pts      []v1beta1.PipelineTask
		runAfter []string
	)

	isCiStage := checkIsCIStage(r.prePRT.StageRun.Type)
	for _, s := range r.pr.Stages {
		for _, t := range s.Tasks {
			if isCiStage {
				// 临时这么处理ci/cd逻辑,后续再看
				if t.ID <= r.prePRT.ID {
					// skip task before the last task of CI stage
					continue
				}
			} else {
				if t.ID < r.prePRT.ID {
					continue
				}
			}

			taskBuilder := r.buildTaskBuilder(&s, &t)
			taskBuilder.SetRunAfter(runAfter)
			task, err := taskBuilder.Build(ctx)
			if err != nil {
				log.ErrorWithCtx(ctx, "build CD Task %s failed %s", t.Name, err)
				return nil, err
			}

			runAfterNames := []string{task.Name}
			log.InfoWithCtx(ctx, "buildTasks task %q, run after %v", task.Name, runAfter)
			pts = append(pts, *task)

			runAfter = runAfterNames
		}
	}

	return pts, nil
}

func (r *DefaultRunner) buildMultiCloudTasks(ctx context.Context, s *dao.PipelineRunStage, t *dao.PipelineRunTask, runAfter []string) ([]v1beta1.PipelineTask, error) {
	// 子任务的触发模式可以通过父任务的触发模式来判断
	var deployConfig model.AutomationDeploy
	if err := t.UnmarshalConfig(&deployConfig); err != nil {
		log.ErrorWithCtx(ctx, "buildMultiCloudTasks UnmarshalConfig error: %v", err)
		return nil, bizerr.ErrConfigUnmarshal
	}

	approvalTypeName := "manualDeploy"
	if constants.TriggerModeType(deployConfig.TriggerMode) == constants.AUTO {
		approvalTypeName = "autoDeploy"
	}

	tasks := make([]v1beta1.PipelineTask, 0, len(t.SubRunTasks))
	for _, st := range t.SubRunTasks {
		builder := NewMultiCloudDeployTaskBuilder(s, t, &st, approvalTypeName)
		builder.SetEnv(r.env)
		builder.SetRunAfter(runAfter)

		task, _ := builder.Build(ctx)
		tasks = append(tasks, *task)
	}
	return tasks, nil
}

func (r *DefaultRunner) buildTaskBuilder(s *dao.PipelineRunStage, t *dao.PipelineRunTask) cdrunner.TaskBuilder {
	var builder cdrunner.TaskBuilder

	switch t.GetType() {
	case constants.TASK_API_AUTOMATION_TEST:
		builder = NewAPIAutoTestTaskBuilder(s, t, r.pr)
	case constants.TASK_CUSTOM_SHELL:
		builder = NewCustomShellTaskBuilder(s, t, r.pr, r.tektonPR, r.requestDate)
	case constants.TASK_PAUSE:
		builder = NewFakeDeployTaskBuilder(s, t, r.pr)
	case constants.TASK_AUTOMATION_DEPLOY:
		if s.GetType() == constants.STAGE_DEPLOY_PROD_ENV {
			// 生产阶段的部署任务全部用新的短流程部署方式
			builder = NewFakeDeployTaskBuilder(s, t, r.pr)
		} else {
			builder = NewDeployTaskBuilder(s, t, r.pr, r.prePRT, r.isRetry)
		}
	case constants.TASK_AUTOMATION_DEPLOY_SENV, constants.TASK_DEPLOY_ORIGIN, constants.TASK_DEPLOY_SUB,
		constants.TASK_DEPLOY_STAGING, constants.TASK_DEPLOY_CANARY,
		constants.TASK_OFFLINE_CANARY,
		constants.TASK_DEV_ENV_IMAGE_SYNC, constants.TASK_TEST_ENV_IMAGE_SYNC:
		// 所有的部署类型任务列表
		builder = NewFakeDeployTaskBuilder(s, t, r.pr)

		// 需要兼容老的镜像同步任务的重试场景 (后续老数据流水线全部跑完后，可以去掉)
		if t.GetType().IsImageSyncTask() && len(t.SubRunTasks) == 0 {
			builder = NewDefaultTaskBuilder(s, t)
		}
	default:
		// for now, just approval ticket task
		builder = NewDefaultTaskBuilder(s, t)
	}
	builder.SetEnv(r.env)
	return builder
}
