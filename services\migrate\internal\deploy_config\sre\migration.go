package sre

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/protocol/deploy"
	"52tt.com/cicd/services/migrate/internal/deploy_config"
	"52tt.com/cicd/services/migrate/internal/deploy_config/common"
	db "52tt.com/cicd/services/migrate/pkg/database"
)

func ExportAllConfig(envs []string, services []string, isUpdate bool, targetProjectName string) error {
	for _, env := range envs {
		err := ExportConfig(env, services, isUpdate, targetProjectName)
		if err != nil {
			return err
		}
	}
	return nil
}

func ExportConfig(env string, services []string, isUpdate bool, targetProjectName string) error {
	k8sMap := map[string][]int{
		db.Dev:  {},
		db.Test: {13, 104, 110, 112, 115, 123, 167},
		db.Prod: {33, 73, 129, 139, 144, 156, 162, 174, 187},
	}
	var configs []deploy_config.UnitDeployConfig
	deployConfigs, err := deploy_config.GetProjectDeployConfig(env, k8sMap)
	if err != nil {
		return err
	}
	for _, dc := range deployConfigs {
		if dc.Unit.App.Team.Name != "sre平台研发组" || dc.Unit.Status != "online" {
			continue
		}
		if len(services) > 0 && !isContainUnit(dc.Unit.Name, services) {
			continue
		}
		configs = append(configs, deploy_config.UnitDeployConfig{
			Resource:       dc.Resource,
			Values:         dc.Values,
			Default:        dc.Default,
			K8sName:        dc.K8sEnv.AssetsK8sCluster.Name,
			NameSpace:      dc.K8sEnv.NameSpace,
			K8sDescription: dc.K8sEnv.AssetsK8sCluster.Description,
			K8sPrivateId:   dc.K8sEnv.AssetsK8sCluster.PrivateId,
			UnitName:       dc.Unit.Name,
			ObjectId:       dc.Unit.ObjectId,
			TeamName:       dc.Unit.App.Team.Name,
			ChartName:      dc.Chart.Name,
			ChartUrl:       dc.Chart.Url,
			ChartVersion:   dc.Chart.Version,
			ChartType:      dc.Chart.CharType,
			UnitId:         dc.UnitId,
			K8sEnvId:       dc.K8sEnvId,
		})
	}
	//err = deploy_config.CreateConfig(configs)
	err = PullConfig(configs, isUpdate, targetProjectName)
	if err != nil {
		return err
	}
	return nil
}

func PullConfig(configs []deploy_config.UnitDeployConfig, isUpdate bool, targetProjectName string) error {
	dbEnv := db.Prod // 同步环境
	//dbEnv := db.Dev

	hpaMap := make(map[string]HpaChart)
	for _, conf := range configs {
		var tempHpa HpaChart
		if conf.Resource == "HPA" || conf.Resource == "MULTI-HPA" {
			_ = json.Unmarshal(conf.Values, &tempHpa)
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			hpaMap[unitKey] = tempHpa
		}
	}
	log.Debugf("hpaMap: %v", hpaMap)

	count := 0
	var unitNames []string
	for _, conf := range configs {
		if conf.ChartName == "telemetry" {
			if conf.K8sName == "k8s-tc-bj-1-test" {
				log.Debugf("unitId: %v, unitName: %v skip k8s-tc-bj-1-test", conf.UnitId, conf.UnitName)
				continue
			}
			var err error
			log.Debugf("unitId: %v, unitName: %v", conf.UnitId, conf.UnitName)
			var valueConfig WorkloadChart
			_ = json.Unmarshal(conf.Values, &valueConfig)
			if (valueConfig.Service.Enabled && valueConfig.Service.Type == "ClusterIP") || !valueConfig.Service.Enabled {
				continue
			}
			log.Infof("[sre] unitName: %v, serviceType: %v", conf.UnitName, valueConfig.Service.Type)

			appBaseConfig, err := HandAppBaseConfig(valueConfig)
			if err != nil {
				return err
			}
			appAdvancedConfig, err := HandAppAdvancedConfig(valueConfig, conf.UnitName)
			if err != nil {
				return err
			}
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			traitConfig, err := HandTraitConfig(valueConfig, unitKey, hpaMap)
			if err != nil {
				return err
			}

			// 数据库操作：查询app、查询/创建deploy_metadata、创建deploy_config
			// 1.查询app
			teamName := conf.TeamName
			if targetProjectName != "" {
				teamName = targetProjectName
			}
			appId, err := common.FindAppIdBy(dbEnv, teamName, conf.UnitName)
			if err != nil {
				return err
			}
			if appId == 0 {
				log.Debugf("app name: %v is not exist", conf.UnitName)
				continue
			}
			metadata := deploy_config.DeployMetadata{
				Env:       GetEnvEnum(conf.K8sName),
				EnvTarget: 1, //默认基准环境
				Cluster:   getClusterName(conf.K8sName),
				Namespace: conf.NameSpace,
				AppID:     appId,
				AppName:   conf.UnitName,
				ConfigID:  0,
			}
			err = deploy_config.GetDeployConfigLastVersion(dbEnv, &metadata)
			if err != nil {
				return err
			}
			if metadata.Config != nil && metadata.Config.CreatedBy != 71 {
				log.Infof("has existed config, unitId: %v, unitName: %v, createdBy: %v", conf.UnitId, conf.UnitName, metadata.Config.CreatedByChineseName)
			}

			// 只验证数据转换是否可行，不更新
			if !isUpdate {
				log.Infof("ignore update, unitId: %v, unitName: %v", conf.UnitId, conf.UnitName)
				continue
			}
			version := 1
			if metadata.ID != 0 {
				if metadata.Config != nil {
					version = metadata.Config.Version + 1
					//continue
				}
				// 只更新指定用户的数据
				if metadata.Config.CreatedBy != 71 {
					log.Infof("has existed config ignore update, unitId: %v, unitName: %v, createdBy: %v", conf.UnitId, conf.UnitName, metadata.Config.CreatedByChineseName)
					continue
				}
			} else {
				err = deploy_config.CreateDeployMetadata(dbEnv, &metadata)
				if err != nil {
					return err
				}
			}
			deployConfig := deploy_config.DeployConfig{
				AppBasicConfig:       appBaseConfig,
				AppAdvancedConfig:    appAdvancedConfig,
				TraitConfig:          traitConfig,
				Version:              version,
				CreatedBy:            71, //创建用户id
				CreatedByChineseName: "陈伟良",
				CreatedByEmployeeNo:  "T2517",
				TemplateID:           0,
				MetadataID:           metadata.ID,
				ConfigType:           1,
			}
			err = deploy_config.CreateDeployConfig(dbEnv, &deployConfig)
			if err != nil {
				return err
			}
			metadata.ConfigID = deployConfig.ID
			err = deploy_config.UpdateDeployMetadata(dbEnv, &metadata)
			if err != nil {
				return err
			}
			count++
			unitNames = append(unitNames, conf.UnitName)
		}
	}
	log.Infof("count: %d", count)
	log.Infof("successful unit: %v", unitNames)
	return nil
}

func HandAppBaseConfig(config WorkloadChart) ([]byte, error) {
	res := deploy.AppBasicConfig{
		NetworkType:       "",
		NetworkPorts:      []*deploy.AppBasicConfig_Port{},
		Annotations:       []*deploy.Pair{},
		Envs:              []*deploy.Pair{},
		ResourceFieldEnvs: []*deploy.ResourceFieldEnv{},
		Commands:          []string{},
		Configs:           []*deploy.AppBasicConfig_Config{},
	}

	// 网络配置-服务协议和端口
	if config.Service.Enabled {
		res.NetworkType = config.Service.Type
		serviceName := config.Service.Name
		if len(serviceName) >= 15 || serviceName == "" {
			serviceName = "service-0"
		}
		res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
			Name:         serviceName,
			InternalPort: config.Service.TargetPort,
			ExternalPort: config.Service.Port,
		})
	}
	// 网络配置-多端口
	if config.TempNamespace.Service.MultiPorts.Enabled {
		count := 1
		for _, port := range config.Service.MultiPorts {
			multiSvcName := port.Name
			if len(multiSvcName) >= 15 {
				multiSvcName = fmt.Sprintf("service-%d", count)
				count++
			}
			res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
				Name:         multiSvcName,
				InternalPort: port.TargetPort,
				ExternalPort: port.Port,
			})
		}
	}
	// 注解
	for k, v := range config.Service.Annotations {
		res.Annotations = append(res.Annotations, &deploy.Pair{
			Key:   k,
			Value: v,
		})
	}

	// 环境变量
	if config.TempNamespace.Deploy.Env.Enabled {
		for _, env := range config.Deploy.Env {
			if env.Name != "" && env.Value != "" {
				res.Envs = append(res.Envs, &deploy.Pair{Key: env.Name, Value: env.Value})
			}
		}
	}

	// 命令参数
	for _, command := range config.Deploy.Command {
		res.Commands = append(res.Commands, command)
	}

	// 配置文件
	VolumeMountMap := make(map[string]VolumeMounts)
	for _, volumeMounts := range config.VolumeMounts {
		VolumeMountMap[volumeMounts.Name] = volumeMounts
	}
	for fileName, fileContent := range config.ConfigFiles {
		if value, ok := VolumeMountMap["config"]; ok {
			path := value.MountPath
			if value.SubPath != "" {
				strings.Replace(path, "/"+value.SubPath, "", 0)
			}
			res.Configs = append(res.Configs, &deploy.AppBasicConfig_Config{
				DirPath:  path,
				FileName: fileName,
				Content:  fileContent})
		} else {
			res.Configs = append(res.Configs, &deploy.AppBasicConfig_Config{DirPath: "/config2", FileName: fileName, Content: fileContent})
		}
	}
	//for fileName, fileContent := range config.SecretFiles {
	//	if value, ok := VolumeMountMap["secret"]; ok {
	//		path := value.MountPath
	//		if value.SubPath != "" {
	//			strings.Replace(path, "/"+value.SubPath, "", 0)
	//		}
	//		res.Configs = append(res.Configs, &deploy.AppBasicConfig_Config{
	//			DirPath:  path,
	//			FileName: fileName,
	//			Content:  fileContent})
	//	} else {
	//		res.Configs = append(res.Configs, &deploy.AppBasicConfig_Config{DirPath: "/config", FileName: fileName, Content: fileContent})
	//	}
	//}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func builderHandler() *deploy.HealthCheck_HealthCheckHandler {
	return &deploy.HealthCheck_HealthCheckHandler{
		Type: "",
		Exec: &deploy.ExecAction{
			Command: []string{},
		},
		TcpSocket: &deploy.TCPSocketAction{},
		HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
		Grpc:      &deploy.GRPCAction{},
		Probe: &deploy.HealthCheck_Probe{
			InitialDelaySeconds: 0,
			PeriodSeconds:       10,
			TimeoutSeconds:      1,
			SuccessThreshold:    1,
			FailureThreshold:    3,
		},
	}
}

func HandAppAdvancedConfig(config WorkloadChart, unitName string) ([]byte, error) {
	res := deploy.AppAdvancedConfig{
		Labels:      []*deploy.Pair{},
		Annotations: []*deploy.Pair{},
		HostAliases: []*deploy.HostAlias{},
		HealthCheck: &deploy.HealthCheck{
			ReadinessProbe: builderHandler(),
			LivenessProbe:  builderHandler(),
			StartupProbe:   builderHandler(),
			Types:          []string{},
		},
		ServiceAccountName: "default",
		Lifecycle: &deploy.Lifecycle{
			PostStart: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			PreStop: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			Types: []string{},
		},
	}

	expectedLabel := map[string]bool{
		"uuid":       true,
		"cluster_id": true,
		"env":        true,
		"lang":       true,
		"type":       true,
	}
	// label 标签
	for key, value := range config.Labels {
		if _, ok := expectedLabel[key]; ok {
			continue
		}
		res.Labels = append(res.Labels, &deploy.Pair{Key: key, Value: value})
	}
	// 注解
	for k, v := range config.Deploy.Annotations {
		// 监控相关
		if k == "telemetry.mesh.quwan.io/customMetricsPath" ||
			k == "telemetry.mesh.quwan.io/customMetricsPort" ||
			k == "telemetry.mesh.quwan.io/customMetricsScrape" ||
			k == "telemetry.mesh.quwan.io/customMetricsContainer" {
			continue
		}
		// sidecar
		if k == "sidecar.istio.io/proxyCPU" ||
			k == "sidecar.istio.io/proxyMemory" ||
			k == "sidecar.istio.io/proxyCPULimit" ||
			k == "sidecar.istio.io/proxyMemoryLimit" {
			continue
		}
		res.Annotations = append(res.Annotations, &deploy.Pair{
			Key:   k,
			Value: v,
		})
	}

	// 主机别名
	if config.TempNamespace.HostAliases.Enabled {
		for _, host := range config.HostAliases {
			for _, name := range host.Hostnames {
				if name != "" && host.IP != "" {
					res.HostAliases = append(res.HostAliases, &deploy.HostAlias{
						Domain: name,
						Ip:     host.IP,
					})
				}
			}
		}
	}

	// 就绪探针
	if config.Probe.Enabled && config.TempNamespace.Probe.ReadinessProbe.Enabled {
		res.HealthCheck.Types = append(res.HealthCheck.Types, "readinessProbe")
		if len(config.Probe.ReadinessProbe.Exec.Command) > 0 {
			res.HealthCheck.ReadinessProbe.Type = "exec"
			res.HealthCheck.ReadinessProbe.Exec = &deploy.ExecAction{}
			for _, cmd := range config.Probe.ReadinessProbe.Exec.Command {
				res.HealthCheck.ReadinessProbe.Exec.Command = append(res.HealthCheck.ReadinessProbe.Exec.Command, cmd)
			}
		} else if config.Probe.ReadinessProbe.TCPSocket.Port > 0 {
			res.HealthCheck.ReadinessProbe.Type = "tcpSocket"
			res.HealthCheck.ReadinessProbe.TcpSocket = &deploy.TCPSocketAction{
				Port: config.Probe.ReadinessProbe.TCPSocket.Port,
			}
		} else if config.Probe.ReadinessProbe.HTTPGet.Port > 0 {
			res.HealthCheck.ReadinessProbe.Type = "httpGet"
			res.HealthCheck.ReadinessProbe.HttpGet = &deploy.HTTPGetAction{
				Path:   config.Probe.ReadinessProbe.HTTPGet.Path,
				Port:   config.Probe.ReadinessProbe.HTTPGet.Port,
				Scheme: config.Probe.ReadinessProbe.HTTPGet.Scheme,
			}
			for _, header := range config.Probe.ReadinessProbe.HTTPGet.HTTPHeaders {
				res.HealthCheck.ReadinessProbe.HttpGet.Headers = append(res.HealthCheck.ReadinessProbe.HttpGet.Headers,
					&deploy.Pair{Key: header.Name, Value: header.Value})
			}
		}

		res.HealthCheck.ReadinessProbe.Probe = &deploy.HealthCheck_Probe{}
		// 探测间隔
		readnessPeriodSeconds := config.Probe.ReadinessProbe.PeriodSeconds
		if readnessPeriodSeconds > 0 {
			res.HealthCheck.ReadinessProbe.Probe.PeriodSeconds = readnessPeriodSeconds
		} else {
			res.HealthCheck.ReadinessProbe.Probe.PeriodSeconds = 10
		}
		// 启动延时
		readnessInitialDelaySeconds := config.Probe.ReadinessProbe.InitialDelaySeconds
		if readnessInitialDelaySeconds > 0 {
			res.HealthCheck.ReadinessProbe.Probe.InitialDelaySeconds = readnessInitialDelaySeconds
		} else {
			res.HealthCheck.ReadinessProbe.Probe.InitialDelaySeconds = 3
		}
		// 超时时长
		readnessTimeout := config.Probe.ReadinessProbe.TimeoutSeconds
		if readnessTimeout > 0 {
			res.HealthCheck.ReadinessProbe.Probe.TimeoutSeconds = readnessTimeout
		} else {
			res.HealthCheck.ReadinessProbe.Probe.TimeoutSeconds = 2
		}
		// 成功阈值
		readnessSuccessThreshold := config.Probe.ReadinessProbe.SuccessThreshold
		if readnessSuccessThreshold > 0 {
			res.HealthCheck.ReadinessProbe.Probe.SuccessThreshold = readnessSuccessThreshold
		} else {
			res.HealthCheck.ReadinessProbe.Probe.SuccessThreshold = 1
		}
		// 失败阈值
		readnessFailureThreshold := config.Probe.ReadinessProbe.FailureThreshold
		if readnessFailureThreshold > 0 {
			res.HealthCheck.ReadinessProbe.Probe.FailureThreshold = readnessFailureThreshold
		} else {
			res.HealthCheck.ReadinessProbe.Probe.FailureThreshold = 2
		}
	}

	// 存活探针
	if config.Probe.Enabled && config.TempNamespace.Probe.LivenessProbe.Enabled {
		res.HealthCheck.Types = append(res.HealthCheck.Types, "livenessProbe")
		if len(config.Probe.LivenessProbe.Exec.Command) > 0 {
			res.HealthCheck.LivenessProbe.Type = "exec"
			res.HealthCheck.LivenessProbe.Exec = &deploy.ExecAction{}
			for _, cmd := range config.Probe.LivenessProbe.Exec.Command {
				res.HealthCheck.LivenessProbe.Exec.Command = append(res.HealthCheck.LivenessProbe.Exec.Command, cmd)
			}
		} else if config.Probe.LivenessProbe.TCPSocket.Port > 0 {
			res.HealthCheck.LivenessProbe.Type = "tcpSocket"
			res.HealthCheck.LivenessProbe.TcpSocket = &deploy.TCPSocketAction{
				Port: config.Probe.LivenessProbe.TCPSocket.Port,
			}
		} else if config.Probe.LivenessProbe.HTTPGet.Port > 0 {
			res.HealthCheck.LivenessProbe.Type = "httpGet"
			res.HealthCheck.LivenessProbe.HttpGet = &deploy.HTTPGetAction{
				Path:   config.Probe.LivenessProbe.HTTPGet.Path,
				Port:   config.Probe.LivenessProbe.HTTPGet.Port,
				Scheme: config.Probe.LivenessProbe.HTTPGet.Scheme,
			}
			for _, header := range config.Probe.LivenessProbe.HTTPGet.HTTPHeaders {
				res.HealthCheck.LivenessProbe.HttpGet.Headers = append(res.HealthCheck.LivenessProbe.HttpGet.Headers,
					&deploy.Pair{Key: header.Name, Value: header.Value})
			}
		}

		res.HealthCheck.LivenessProbe.Probe = &deploy.HealthCheck_Probe{}
		// 探测间隔
		livenessPeriodSeconds := config.Probe.LivenessProbe.PeriodSeconds
		if livenessPeriodSeconds > 0 {
			res.HealthCheck.LivenessProbe.Probe.PeriodSeconds = int32(livenessPeriodSeconds)
		} else {
			res.HealthCheck.LivenessProbe.Probe.PeriodSeconds = 5
		}
		// 启动延时
		livenessInitialDelaySeconds := config.Probe.LivenessProbe.InitialDelaySeconds
		if livenessInitialDelaySeconds > 0 {
			res.HealthCheck.LivenessProbe.Probe.InitialDelaySeconds = int32(livenessInitialDelaySeconds)
		} else {
			res.HealthCheck.LivenessProbe.Probe.InitialDelaySeconds = 30
		}
		// 超时时长
		livenessTimeout := config.Probe.LivenessProbe.TimeoutSeconds
		if livenessTimeout > 0 {
			res.HealthCheck.LivenessProbe.Probe.TimeoutSeconds = int32(livenessTimeout)
		} else {
			res.HealthCheck.LivenessProbe.Probe.TimeoutSeconds = 2
		}
		// 成功阈值
		livenessSuccessThreshold := config.Probe.LivenessProbe.SuccessThreshold
		if livenessSuccessThreshold > 0 {
			res.HealthCheck.LivenessProbe.Probe.SuccessThreshold = int32(livenessSuccessThreshold)
		} else {
			res.HealthCheck.LivenessProbe.Probe.SuccessThreshold = 1
		}
		// 失败阈值
		livenessFailureThreshold := config.Probe.LivenessProbe.FailureThreshold
		if livenessFailureThreshold > 0 {
			res.HealthCheck.LivenessProbe.Probe.FailureThreshold = int32(livenessFailureThreshold)
		} else {
			res.HealthCheck.LivenessProbe.Probe.FailureThreshold = 10
		}
	}

	// 挂载配置
	res.MountConfig = &deploy.MountConfig{
		Volumes:      []*deploy.Volume{},
		VolumeMounts: []*deploy.VolumeMount{},
	}

	if config.TempNamespace.Volumes.Enabled {
		for _, volume := range config.Volumes {
			res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
				Type:       "secret",
				ReadOnly:   true,
				VolumeName: volume.Name,
				RefName:    volume.Secret.SecretName,
			})
		}
	}
	if config.TempNamespace.VolumeMounts.Enabled {
		for index, volumeMount := range config.VolumeMounts {
			volumeName := volumeMount.Name
			if volumeMount.Name == "secret" {
				volumeName = fmt.Sprintf("secret-%d", index+1)
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "secret",
					ReadOnly:   true,
					VolumeName: volumeName,
					RefName:    unitName,
				})
			}
			if volumeMount.Name == "config" {
				// config 忽略，已经在配置文件中处理
				log.Debugf("%s ignore config volume", unitName)
				continue
			} else {
				res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
					VolumeName: volumeName,
					MountPoint: volumeMount.MountPath,
					SubPath:    volumeMount.SubPath,
				})
			}
		}
	}

	// lifecycle
	periods, _ := strconv.Atoi(config.Deploy.TerminationGracePeriodSeconds)
	res.Lifecycle.TerminationGracePeriod = int64(periods)

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func HandTraitConfig(config WorkloadChart, unitKey string, hpaMap map[string]HpaChart) ([]byte, error) {
	var res deploy.TraitConfig
	// 资源限制
	res.ResourceConstraints = &deploy.ResourceConstraints{Resources: &deploy.Resources{}, Sidecar: &deploy.ResourceConstraints_Sidecar{}}
	cpuReq := config.Resources.Requests.CPU
	if strings.Contains(cpuReq, "m") || strings.Contains(cpuReq, "M") {
		//单位为m
		reqCpu, _ := strconv.ParseFloat(cpuReq[0:len(cpuReq)-1], 32)
		res.ResourceConstraints.Resources.RequestCpu = float32(reqCpu / 1000)
	} else {
		//单位为核
		reqCpu, _ := strconv.ParseFloat(cpuReq, 32)
		res.ResourceConstraints.Resources.RequestCpu = float32(reqCpu)
	}

	memReq, _ := strconv.ParseFloat(config.Resources.Requests.Memory[0:len(config.Resources.Requests.Memory)-2], 32)
	if strings.Contains(config.Resources.Requests.Memory, "Gi") {
		res.ResourceConstraints.Resources.RequestMemory = float32(memReq * 1024)
	} else {
		res.ResourceConstraints.Resources.RequestMemory = float32(memReq)
	}

	cpuLim := config.Resources.Limits.CPU
	if strings.Contains(cpuLim, "m") || strings.Contains(cpuLim, "M") {
		//单位为m
		limCpu, _ := strconv.ParseFloat(cpuLim[0:len(cpuLim)-1], 32)
		res.ResourceConstraints.Resources.LimitCpu = float32(limCpu / 1000)
	} else {
		//单位为核
		limCpu, _ := strconv.ParseFloat(cpuLim, 32)
		res.ResourceConstraints.Resources.LimitCpu = float32(limCpu)
	}

	limMem, _ := strconv.ParseFloat(config.Resources.Limits.Memory[0:len(config.Resources.Limits.Memory)-2], 32)
	if strings.Contains(config.Resources.Limits.Memory, "Gi") {
		res.ResourceConstraints.Resources.LimitMemory = float32(limMem * 1024)
	} else {
		res.ResourceConstraints.Resources.LimitMemory = float32(limMem)
	}

	// 伸缩配置
	res.ScalingConfig = &deploy.ScalingConfig{
		Hpa:      &deploy.ScalingConfig_HPA{Types: []string{}},
		MultiHpa: &deploy.ScalingConfig_MultiHPA{Types: []string{}, Cron: []*deploy.ScalingConfig_MultiHPA_Cron{}},
	}
	replicas, _ := strconv.Atoi(config.Replicas)
	if replicas > 0 {
		res.ScalingConfig.Replicas = int32(replicas)
	} else {
		res.ScalingConfig.Replicas = 1
	}
	res.ScalingConfig.Type = "replicas"

	// hpa
	if value, isExist := hpaMap[unitKey]; isExist && (value.Autoscale.CPU.Enabled || value.Autoscale.Memory.Enabled) {
		res.ScalingConfig.MultiHpa = &deploy.ScalingConfig_MultiHPA{
			Min:   value.Autoscale.MinReplicaCount,
			Max:   value.Autoscale.MaxReplicaCount,
			Types: []string{},
			Cron:  []*deploy.ScalingConfig_MultiHPA_Cron{},
		}
		if value.Autoscale.CPU.Enabled {
			res.ScalingConfig.MultiHpa.CpuUtilization = value.Autoscale.CPU.Value
			res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "cpu")
		}
		if value.Autoscale.Memory.Enabled {
			res.ScalingConfig.MultiHpa.MemoryUtilization = value.Autoscale.Memory.Value
			res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "memory")
		}
		res.ScalingConfig.Type = "multiHPA"
	}

	// 监控指标-MonitorMetrics
	if value, ok := config.Deploy.Annotations["telemetry.mesh.quwan.io/customMetricsScrape"]; ok && value == "true" {
		res.MonitorMetrics = &deploy.MonitorMetrics{}
		var (
			portStr       string
			metricsPath   string
			containerName string
		)
		for k, v := range config.Deploy.Annotations {
			if k == "telemetry.mesh.quwan.io/customMetricsPath" {
				metricsPath = v
			}
			if k == "telemetry.mesh.quwan.io/customMetricsPort" {
				portStr = v
			}
			if k == "telemetry.mesh.quwan.io/customMetricsContainer" {
				containerName = v
			}
		}
		metricsPort, _ := strconv.Atoi(portStr)
		if metricsPort != 0 && metricsPath != "" {
			res.MonitorMetrics = &deploy.MonitorMetrics{
				Enabled:       true,
				Port:          int32(metricsPort),
				MetricsPath:   metricsPath,
				ContainerName: containerName,
			}
		}
	}

	// 升级策略-upgradeStrategy
	res.UpgradeStrategy = &deploy.UpgradeStrategy{
		Enabled:        false,
		MaxSurge:       25,
		MaxUnavailable: 25,
	}

	// 高级配置-advancedConfig
	res.AdvancedConfig = &deploy.TraitAdvancedConfig{
		SchedulingStrategy: []*deploy.SchedulingStrategy{},
		RateLimiting:       &deploy.RateLimiting{},
		CircuitBreaker:     &deploy.CircuitBreaker{},
		InitContainers:     []*deploy.InitContainer{},
		MultiContainers:    []*deploy.MultiContainer{},
	}

	// 亲和性
	if config.TempNamespace.Affinity.Enabled && len(config.Affinity.PodAntiAffinity.PreferredDuringSchedulingIgnoredDuringExecution) > 0 {
		antiAffinity := config.Affinity.PodAntiAffinity.PreferredDuringSchedulingIgnoredDuringExecution[0]
		rules := []*deploy.SchedulingStrategy_Rule{
			{
				Key:      antiAffinity.PodAffinityTerm.LabelSelector.MatchExpressions[0].Key,
				Operator: "In",
				Value:    antiAffinity.PodAffinityTerm.LabelSelector.MatchExpressions[0].Values[0],
			},
		}
		res.AdvancedConfig.SchedulingStrategy = append(res.AdvancedConfig.SchedulingStrategy, &deploy.SchedulingStrategy{
			Type:         "podAntiAffinity",
			Priority:     "Preferred",
			Topology_Key: antiAffinity.PodAffinityTerm.TopologyKey,
			Weight:       int32(antiAffinity.Weight),
			Rules:        rules,
		})
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func getClusterName(name string) string {
	switch name {
	case "火山云集群k8s-hs-bj-1-test":
		return "k8s-hs-bj-1-test"
	default:
		return name
	}
}

func GetEnvEnum(k8sName string) int8 {
	switch k8sName {
	case "k8s-hw-gz-yw-telemetry":
		return int8(2)
	case "k8s-tc-bj-1-test":
		return int8(2)
	case "火山云集群k8s-hs-bj-1-test":
		return int8(2)
	default:
		return 4
	}
}

func isContainUnit(unit string, keys []string) bool {
	var myMap map[string]bool
	myMap = make(map[string]bool)
	for _, key := range keys {
		myMap[key] = true
	}
	if _, ok := myMap[unit]; ok {
		return true
	}
	return false
}
