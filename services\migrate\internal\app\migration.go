package app

import (
	"context"
	"fmt"
	"github.com/hashicorp/go-multierror"
	"strings"
	"time"

	pkgdb "52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/tools"
	"52tt.com/cicd/pkg/tools/cond"
	"52tt.com/cicd/pkg/tools/set"
	"52tt.com/cicd/pkg/tools/vec"
	"52tt.com/cicd/services/migrate/internal/conf"
	"52tt.com/cicd/services/migrate/internal/iam"
	"52tt.com/cicd/services/migrate/pkg/cmdb"
	db "52tt.com/cicd/services/migrate/pkg/database"
	"go.uber.org/multierr"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	DevOfTeam = "dev"
	OrgOfTeam = "org"
	Available = 1
)

const (
	pageSize int = 300
)

func SyncTeamData(p *SyncParameter) error {
	var teams []OrgTeam
	env := p.Env
	if p.IsPartial() {
		names := strings.Split(p.Name, ",")
		if err := db.V2(env).Where("name in ?", names).Find(&teams).Error; err != nil {
			return err
		}
	} else {
		if err := db.V2(env).Where("1=1").Find(&teams).Error; err != nil {
			return err
		}
	}
	var projects []Project
	for _, team := range teams {
		projects = append(projects, Project{
			Name:        team.Name,
			Type:        DevOfTeam,
			Description: cond.Or(team.Description != "", team.Description, team.Name),
		})
	}
	if len(projects) > 0 {
		if err := db.V3(env).Clauses(clause.Insert{Modifier: "IGNORE"}).Create(&projects).Error; err != nil {
			return err
		}
	}
	return nil
}

func NewCmdb() cmdb.Cmdb {
	var cmdbApi *cmdb.App
	cmdbConfig := conf.GlobalConfig.Cmdb
	for _, p := range cmdbConfig.Profiles {
		if p.Name == cmdbConfig.Default {
			cmdbApi = cmdb.NewApp(p.Url, p.Token)
			break
		}
	}
	return cmdbApi
}

func ManualSyncAppData(parameter *ManualSyncAppParameter) error {
	var projects []Project
	env := parameter.Env
	log.DebugWithCtx(context.Background(), env)
	if err := db.V3(env).Where("name =?", parameter.Name).Find(&projects).Error; err != nil {
		return err
	}
	if len(projects) > 0 {
		projectId := projects[0].ID
		cmdbIds := vec.MapTo(parameter.Apps, func(a ManualSyncApp) string { return a.ObjectId })
		cmdbApi := NewCmdb()
		rs, err := cmdbApi.GetOwnerList(cmdbIds)
		if err != nil {
			log.Errorf("获取cmdb数据失败：%s", err.Error())
			return err
		}
		var apps []App
		for _, ap := range parameter.Apps {
			apps = append(apps, App{
				ProjectID:   projectId,
				Name:        ap.Name,
				Code:        ap.Code,
				Status:      Available,
				RepoAddr:    ap.RepoAddr,
				CmdbID:      ap.ObjectId,
				BuildPath:   ap.BuildPath,
				Description: ap.Description,
				LangName:    getLanguageName(ap.LangType),
				LangVersion: getLanguageVersion(ap.LangType),
			})
		}
		return saveAppData(env, apps, &rs)
	}
	return nil
}

func UpgradeAppData(parameter *AppParameter) error {
	updateFields := map[string]any{
		"updated_at": time.Now(),
	}
	if parameter.Name != "" {
		updateFields["name"] = parameter.Name
	}
	if parameter.RepoAddr != "" {
		updateFields["repo_addr"] = parameter.RepoAddr
	}
	if parameter.BuildPath != "" {
		updateFields["build_path"] = parameter.BuildPath
	}
	if parameter.CmdbId != "" {
		updateFields["cmdb_id"] = parameter.CmdbId
	}
	if err := db.V3(db.Prod).Model(&App{}).Where("id = ?", parameter.Id).Updates(updateFields).Error; err != nil {
		log.Errorf("更新应用信息失败：%s", err.Error())
		return err
	}
	return nil
}

func UpgradeBuildPath(p *SyncParameter) error {
	var teams []OrgTeam
	env := p.Env
	if p.IsPartial() {
		names := strings.Split(p.Name, ",")
		if err := db.V2(env).Preload("Apps.Units.SourceCode").Preload("Apps.Units").Preload("Apps").Where("name in ?", names).Find(&teams).Error; err != nil {
			return err
		}
	} else {
		if err := db.V2(env).Preload("Apps.Units.SourceCode").Preload("Apps.Units").Preload("Apps").Where("1=1").Find(&teams).Error; err != nil {
			return err
		}
	}
	var projects []Project
	teamNames := vec.MapTo(teams, func(t OrgTeam) string { return t.Name })
	if err := db.V3(env).Where("name in ?", teamNames).Find(&projects).Error; err != nil {
		return err
	}

	if len(projects) > 0 {
		dbErr := db.V3(env).Transaction(func(tx *gorm.DB) error {
			for _, team := range teams {
				for _, app := range team.Apps {
					for _, unit := range app.Units {
						if err := tx.Model(&App{}).Where("cmdb_id = ?", unit.ObjectID).Update("build_path", cond.Or(unit.SourcePath != "", unit.SourcePath, "./")).Error; err != nil {
							log.Errorf("更新build_path失败：%s", err.Error())
							return err
						}
					}
				}
			}
			return nil
		})
		if dbErr != nil {
			return dbErr
		}

	}
	return nil
}

func SyncAppData(p *SyncAppParameter) error {
	var multiErr error
	var teams []OrgTeam
	env := p.Env
	targetEnv := p.TargetEnv
	if targetEnv == "" {
		targetEnv = env
	}
	if p.Action == "partial" {
		names := strings.Split(p.Name, ",")
		if err := db.V2(env).Preload("Apps.Units.SourceCode").Preload("Apps.Units").Preload("Apps").Where("name in ?", names).Find(&teams).Error; err != nil {
			return err
		}
	} else if p.Action == "all" {
		if err := db.V2(env).Preload("Apps.Units.SourceCode").Preload("Apps.Units").Preload("Apps").Where("1=1").Find(&teams).Error; err != nil {
			return err
		}
	} else {
		return fmt.Errorf("无需迁移")
	}
	var projects []Project
	teamNames := vec.MapTo(teams, func(t OrgTeam) string { return t.Name })
	if p.TargetProject != "" {
		// 如果指定了目标项目，则只迁移指定项目
		teamNames = []string{p.TargetProject}
	}
	if err := db.V3(targetEnv).Where("name in ?", teamNames).Find(&projects).Error; err != nil {
		return err
	}
	if len(projects) > 0 {
		var apps []App
		cmdbApi := NewCmdb()
		cmdbIds := vec.Flat(vec.MapTo(teams, func(t OrgTeam) []string {
			return vec.Flat(vec.MapTo(t.Apps, func(a AppGroup) []string {
				return vec.Filter(vec.MapTo(a.Units, func(u Unit) string {
					return u.ObjectID
				}), func(cmdbId string) bool {
					return cmdbId != ""
				})
			}))
		}))

		results := make(cmdb.Results, 0)
		if len(cmdbIds) > pageSize {
			for n := 0; n < len(cmdbIds); n += pageSize {
				end := n + pageSize
				if end > len(cmdbIds) {
					end = len(cmdbIds)
				}
				rs, err := cmdbApi.GetOwnerList(cmdbIds[n:end])
				if err != nil {
					log.Errorf("获取cmdb数据失败：%s", err.Error())
					return err
				}
				results = append(results, rs...)
			}
		} else {
			rs, err := cmdbApi.GetOwnerList(cmdbIds)
			if err != nil {
				log.Errorf("获取cmdb数据失败：%s", err.Error())
				return err
			}
			results = append(results, rs...)
		}
		for _, team := range teams {
			targetProjects := vec.Filter(projects, func(p Project) bool { return p.Name == team.Name })
			if p.TargetProject != "" {
				// 如果指定了目标项目，则只迁移指定项目
				targetProjects = vec.Filter(projects, func(param Project) bool { return param.Name == p.TargetProject })
			}
			if len(targetProjects) > 0 {
				for _, app := range team.Apps {
					for _, unit := range app.Units {
						if len(p.Services) > 0 && !isContainUnit(unit.Name, p.Services) {
							continue
						}
						// 跳过指定服务
						if len(p.SkipServices) > 0 && isContainUnit(unit.Name, p.SkipServices) {
							continue
						}
						if unit.Status != "online" {
							continue
						}
						var oldApps []App
						if err := db.V3(targetEnv).Model(&App{}).Where("name = ? and project_id = ?", unit.Name, targetProjects[0].ID).Find(&oldApps).Error; err != nil {
							errInfo := fmt.Errorf("查询应用%s错误，忽略该应用", unit.Name)
							multiErr = multierror.Append(multiErr, errInfo)
							continue
						}
						if len(oldApps) > 0 {
							errInfo := fmt.Errorf("应用%s已存在，忽略该应用", unit.Name)
							multiErr = multierror.Append(multiErr, errInfo)
							continue
						}

						if unit.SourceCode == nil {
							errInfo := fmt.Errorf("找不到服务%s的源码信息，忽略该服务", unit.Name)
							multiErr = multierror.Append(multiErr, errInfo)
							continue
						}
						result := results.GetResult(unit.ObjectID)
						if result == nil {
							errInfo := fmt.Errorf("找不到服务%s的cmdb信息信息，忽略该服务", unit.Name)
							multiErr = multierror.Append(multiErr, errInfo)
							continue
						}

						// buildPath适配cpp项目
						buildPath := unit.SourcePath
						if unit.SourceCode.SourceUrl == "https://gitlab.ttyuyin.com/TT-AppServices/appsvr.git" {
							buildPath = fmt.Sprintf("src/%s", unit.SourcePath)
						}
						apps = append(apps, App{
							Name:        unit.Name,
							Code:        fmt.Sprintf("%s(%s)", unit.Name, app.Name),
							BuildPath:   cond.Or(unit.SourcePath != "", buildPath, "./"),
							RepoAddr:    cond.Or(unit.SourceCode != nil, unit.SourceCode.SourceUrl, ""),
							Status:      Available,
							CmdbID:      unit.ObjectID,
							LangName:    getLanguageName(unit.LangType),
							LangVersion: getLanguageVersion(unit.LangType),
							Description: cond.Or(unit.Description != "", unit.Description, fmt.Sprintf("批量迁入[%s]", unit.Name)),
							ProjectID:   targetProjects[0].ID,
						})
					}
				}
			}
		}

		log.InfoWithCtx(context.Background(), "开始同步应用数据，共%d个", len(apps))
		go saveAppData(targetEnv, apps, &results)
	}
	return multiErr
}

func isContainUnit(unit string, keys []string) bool {
	var myMap map[string]bool
	myMap = make(map[string]bool)
	for _, key := range keys {
		myMap[key] = true
	}
	if _, ok := myMap[unit]; ok {
		return true
	}
	return false
}

func UpgradeAppLabels(env string, p *UpdateLabelsParameter) error {
	MatchLabels, _ := pkgdb.DbStrMap(p.MatchLabels).Value()
	ServiceLabels, _ := pkgdb.DbStrMap(p.ServiceLabels).Value()
	fmt.Println(MatchLabels, ServiceLabels)
	if err := db.V3(env).Model(&App{}).Where("id = ?", p.AppId).Updates(map[string]interface{}{"match_labels": MatchLabels, "service_labels": ServiceLabels}).Error; err != nil {
		log.Errorf("更新labels失败：%s", err.Error())
		return err
	}
	return nil
}

func UpdAppLabelEnvs(p AppLabelEnvsParm) (err error) {
	if p.ProjectId <= 0 || p.AppName == "" {
		return
	}
	if err := db.V3(db.Prod).Model(&App{}).Where("project_id = ? AND name = ? ", p.ProjectId, p.AppName).
		Updates(map[string]interface{}{"stand_label_envs": strings.Join(p.LabelEnvs, ",")}).Error; err != nil {
		log.Errorf("更新labelEnvs失败：%s", err.Error())
		return err
	}
	return
}

func UpdAppLabelsByName(env string, p UpdateLabelsParms) (err error) {
	if p.ProjectId <= 0 || p.AppName == "" {
		return
	}
	MatchLabels, _ := pkgdb.DbStrMap(p.MatchLabels).Value()
	ServiceLabels, _ := pkgdb.DbStrMap(p.ServiceLabels).Value()
	fmt.Println(MatchLabels, ServiceLabels)
	updParms := map[string]interface{}{"match_labels": MatchLabels, "service_labels": ServiceLabels}
	if len(p.MatchLabels) == 0 && len(p.ServiceLabels) == 0 {
		updParms["stand_label_envs"] = ""
	}
	if err := db.V3(env).Model(&App{}).Where("project_id = ? AND name = ? ", p.ProjectId, p.AppName).
		Updates(updParms).Error; err != nil {
		log.Errorf("更新labels失败：%s", err.Error())
		return err
	}
	return
}

func UpdateNeedExternalRoute(ctx context.Context, p *UpdateNeedExternalRouteParameter) error {
	if p.Env == "" || p.ProjectID <= 0 || len(p.AppIDs) == 0 {
		return nil
	}

	if err := db.V3(p.Env).Model(&App{}).Where("project_id = ? AND id in (?) ", p.ProjectID, p.AppIDs).
		Update("need_external_route", p.NeedExternalRoute).Error; err != nil {
		log.ErrorWithCtx(ctx, "更新外部路由失败：%v, req: %+v", err, p)
		return err
	}
	return nil
}

func saveAppData(env string, apps []App, rs *cmdb.Results) error {
	var result error
	if len(apps) > 0 {
		log.Infof("开始同步应用数据，共%d个", len(apps))
		err := db.V3(env).Transaction(func(tx *gorm.DB) error {
			for _, app := range apps {
				log.Debugf("开始同步应用%s", app.Name)
				if err := tx.Clauses(clause.Insert{Modifier: "IGNORE"}).Create(&app).Error; err != nil {
					errInfo := fmt.Errorf("插入应用数据失败：%v", err)
					result = multierr.Append(result, errInfo)
					return errInfo
				}
				if app.ID == 0 {
					errInfo := fmt.Errorf("应用%s插入未生效，已经忽略", app.Name)
					result = multierr.Append(result, errInfo)
					continue
				}
				var appUsers []AppUser
				cmdbResult := rs.GetResult(app.CmdbID)
				if cmdbResult == nil || len(cmdbResult.DevelopUserList) == 0 {
					errInfo := fmt.Errorf("找不到应用%s的cmdb信息，忽略该应用", app.Name)
					result = multierr.Append(result, errInfo)
					continue
				}
				emails := vec.MapTo(cmdbResult.DevelopUserList, func(o cmdb.Owner) string {
					return o.Email
				})
				users, err := iam.ListUserByEmails(env, emails)
				if err != nil {
					errInfo := fmt.Errorf("获取用户信息失败：%v", err)
					result = multierr.Append(result, errInfo)
					return errInfo
				}
				for _, user := range users {
					appUsers = append(appUsers, AppUser{
						AppID:  app.ID,
						UserID: user.ID,
					})
				}
				tx.Clauses(clause.Insert{Modifier: "IGNORE"}).Create(&appUsers)
			}
			return nil
		})

		if err != nil {
			errInfo := fmt.Errorf("插入应用数据失败：%v", err)
			result = multierr.Append(result, errInfo)
			return errInfo
		}
	}
	return result
}

func SyncTeamMemberData(p *SyncParameter) error {
	var teams []OrgTeam
	env := p.Env
	if p.IsPartial() {
		names := strings.Split(p.Name, ",")
		if err := db.V2(env).Model(&OrgTeam{}).Preload("Members.User").Preload("Members").Where("name in ?", names).Find(&teams).Error; err != nil {
			return err
		}
	} else {
		if err := db.V2(env).Model(&OrgTeam{}).Preload("Members.User").Preload("Members").Where("1=1").Find(&teams).Error; err != nil {
			return err
		}
	}
	var projects []Project
	teamNames := vec.MapTo(teams, func(t OrgTeam) string { return t.Name })
	if err := db.V3(env).Where("name in ?", teamNames).Find(&projects).Error; err != nil {
		return err
	}
	if len(projects) > 0 {
		var users []iam.ProjectUser
		roles, err := iam.ListRole(env)
		if err != nil {
			log.Errorf("获取新cicd的角色信息失败：%v", err)
			return err
		}
		for _, team := range teams {
			emails := set.Of(vec.MapTo(team.Members, func(m OrgTeamMember) string { return m.User.Email })).Slice()
			members, err := iam.ListUserByEmails(env, emails)
			if err != nil {
				log.Errorf("获取新cicd的用户信息失败：%v", err)
				return err
			}
			targetProjects := tools.Filter(projects, func(p Project) bool { return p.Name == team.Name })
			if len(targetProjects) > 0 {
				for _, member := range members {
					roleName := team.Members.getRoleName(member.Email)
					users = append(users, iam.ProjectUser{
						ProjectID: targetProjects[0].ID,
						UserID:    member.ID,
						RoleID:    roles.GetRoleId(roleName),
					})
				}
			}
		}
		if len(users) > 0 {
			return db.V3(env).Create(users).Error
		}
	}
	return nil
}

func DelTeamMemberData(p *IdsParameter) error {
	return db.V3(p.Env).Transaction(func(tx *gorm.DB) error {
		for _, id := range p.Ids {
			tx.Delete(&iam.ProjectUser{}, "project_id=? AND user_id=?", p.ProjectId, id)
		}
		return nil
	})
}

func Delete(p *IdsParameter) error {
	return db.V3(p.Env).Transaction(func(tx *gorm.DB) error {
		for _, id := range p.Ids {
			tx.Model(&App{}).Delete("id=?", id)
			tx.Model(&AppUser{}).Delete("app_id=?", id)
		}
		return nil
	})
}

func UpdateUserGitlabId(p *UpdateUserGitlabIdParameter) error {
	return db.V3(p.Env).Model(&iam.User{}).Where("username=?", p.Username).Update("gitlab_id", p.GitlabId).Error
}
