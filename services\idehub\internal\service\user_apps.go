//go:generate mockgen -destination=user_apps_mock.go -package=service -source=user_apps.go
package service

import (
	"context"
	"fmt"
	"strings"

	cctx "52tt.com/cicd/pkg/context"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/tools"
	pbapp "52tt.com/cicd/protocol/app"
	pbiam "52tt.com/cicd/protocol/iam"
	"52tt.com/cicd/services/idehub/internal/dao"
	"52tt.com/cicd/services/idehub/internal/model"
)

var _ UserAppsService = (*UserAppsSvc)(nil)

type UserAppsService interface {
	GetUserApps(ctx context.Context, req model.UserAppsReq) ([]*model.UserAppsItem, error)
}

type UserAppsSvc struct {
	debugLogRepo      dao.DebugLogRepository
	appClient         pbapp.AppServiceClient
	projectUserClient pbiam.ProjectUserServiceClient
	appConfigSvc      AppConfigService
}

func NewUserAppsService(dbRepo dao.DebugLogRepository,
	appClient pbapp.AppServiceClient,
	projectUserClient pbiam.ProjectUserServiceClient, appConfigSvc AppConfigService) *UserAppsSvc {
	userAppsSvc := UserAppsSvc{
		debugLogRepo:      dbRepo,
		appClient:         appClient,
		projectUserClient: projectUserClient,
		appConfigSvc:      appConfigSvc,
	}
	return &userAppsSvc
}

func (ua *UserAppsSvc) GetUserApps(ctx context.Context, req model.UserAppsReq) ([]*model.UserAppsItem, error) {
	targetAllUserApps := make([]*model.UserAppsItem, 0)
	operator := cctx.GetUserinfo(ctx)
	operatorId := operator.UserID
	//operatorId = 100
	// 查询当前用户的调试记录
	logQuery := model.ListDebugLogReq{
		UserID:  operatorId,
		AppName: req.Name,
	}
	debugLogs, err := ua.debugLogRepo.ListDebugLogs(ctx, logQuery)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetUserApps] get debug logs list error: %v, userID[%d]", err, operatorId)
		return nil, err
	}
	debugLogsMap := make(map[int64]*dao.DebugLog, 0)
	for _, logItem := range debugLogs {
		debugLogsMap[logItem.AppID] = &logItem
	}

	debugLogsMapWithClusterNamespaceAppId := make(map[string]*dao.DebugLog, 0)
	for _, logItem := range debugLogs {
		debugLogsMapWithClusterNamespaceAppId[fmt.Sprintf("%s:%s:%d", logItem.Cluster, logItem.Namespace, logItem.AppID)] = &logItem
	}

	// 查询当前用户有授权角色的项目列表
	projectList, err := ua.projectUserClient.GetUserProjectsBy(ctx, &pbiam.UserProParam{
		UserId: operatorId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetUserApps] get user projects error: %v, userID[%d]", err, operatorId)
		return nil, err
	}
	if len(projectList.ProjectUsers) == 0 {
		return targetAllUserApps, nil
	}

	projectIds := tools.MapTo(projectList.ProjectUsers, func(pro *pbiam.ProjectUser) int64 {
		return pro.GetProjectId()
	})

	allUserApps := make([]*model.UserAppsItem, 0)
	userAppsQuery := &pbapp.GetUserAppsReq{
		UserId:     operatorId,
		ProjectIds: projectIds,
		AppName:    req.Name,
	}
	// 查询当前用户收藏的服务列表
	userPreferAppList, err := ua.appClient.GetUserPreferenceApps(ctx, userAppsQuery)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetUserApps] get user preference apps error: %v, query: %+v", err, userAppsQuery)
		return nil, err
	}
	userPreferApps := generateUserPreferenceAppsList(userPreferAppList.Apps, debugLogsMap)
	if len(userPreferApps.Apps) > 0 {
		allUserApps = append(allUserApps, userPreferApps)
	}

	// 查询当前用户所在项目组的服务列表
	userAppList, err := ua.appClient.GetUserApps(ctx, userAppsQuery)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetUserApps] get user apps error: %v, query: %+v", err, userAppsQuery)
		return nil, err
	}
	userApps := generateUserAppsList(userAppList.Apps, debugLogsMap)
	allUserApps = append(allUserApps, userApps...)

	prjIds := make([]int64, 0, 64)
	for _, app := range allUserApps {
		prjIds = append(prjIds, app.ProjectID)
	}

	//从projectIds中 获取好运行的cluster和namespace
	appItemMap, err := ua.appConfigSvc.GetAppClusterInfoWithProjectIds(ctx, nil, prjIds...)
	if err != nil {
		return nil, err
	}

	runningAppsItems := make([]*model.AppsItem, 0)
	runningAppsMap := make(map[int64]*model.AppsItem) // 用于去重
	for _, app := range allUserApps {
		targetAppsItems := make([]*model.AppsItem, 0, len(app.Apps))

		// 取一下status
		for _, item := range app.Apps {
			// 过滤脏数据
			if appItemMap[item.ID] == nil || !strings.Contains(appItemMap[item.ID].Cluster, "k8s") {
				continue
			}

			debugLog := debugLogsMap[item.ID]
			debugLogWithClusterNamespace := debugLogsMapWithClusterNamespaceAppId[fmt.Sprintf("%s:%s:%d", appItemMap[item.ID].Cluster, appItemMap[item.ID].Namespace, item.ID)]
			if debugLog == nil {
				appItemMap[item.ID].Status = item.Status
			}

			// 通过deploy服务中的数据 获取真实的debuglog中状态
			if debugLogWithClusterNamespace != nil {
				appItemMap[item.ID].Status = debugLogWithClusterNamespace.Status.String()
			}
			if appItemMap[item.ID].Status != constants.DebugStatusPending.String() {
				runningAppsMap[item.ID] = appItemMap[item.ID] // 使用map去重
			}
			targetAppsItems = append(targetAppsItems, appItemMap[item.ID])
		}

		targetAllUserApps = append(targetAllUserApps, &model.UserAppsItem{
			ProjectID:   app.ProjectID,
			ProjectName: app.ProjectName,
			Apps:        targetAppsItems,
		})
	}

	// 将去重后的运行中应用转换为slice
	for _, item := range runningAppsMap {
		runningAppsItems = append(runningAppsItems, item)
	}
	// 生成运行中的服务列表
	runningApps := generateUserRunningAppsList(runningAppsItems)
	if len(runningApps.Apps) > 0 {
		targetAllUserApps = append([]*model.UserAppsItem{runningApps}, targetAllUserApps...)
	}

	return targetAllUserApps, nil
}

func generateUserRunningAppsList(runningApps []*model.AppsItem) *model.UserAppsItem {

	return &model.UserAppsItem{
		ProjectID:   -1,
		ProjectName: "运行中",
		Apps:        runningApps,
	}
}

func generateUserAppsList(apps []*pbapp.APP, debugLogsMap map[int64]*dao.DebugLog) []*model.UserAppsItem {
	appsMap := make(map[int64]*model.UserAppsItem, 0)
	projectIds := make([]int64, 0)
	for _, app := range apps {
		projectId := app.ProjectID
		projectName := app.ProjectName
		var (
			appStatus string
			cluster   string
			namespace string
		)
		if debugLog, ok := debugLogsMap[app.Id]; ok {
			appStatus = debugLog.Status.String()
			cluster = debugLog.Cluster
			namespace = debugLog.Namespace
		} else {
			appStatus = constants.DebugStatusPending.String()
		}

		if _, ok := appsMap[projectId]; !ok {
			appsMap[projectId] = &model.UserAppsItem{
				ProjectID:   projectId,
				ProjectName: projectName,
				Apps:        []*model.AppsItem{},
			}
			projectIds = append(projectIds, projectId)
		}
		appsMap[projectId].Apps = append(appsMap[projectId].Apps, &model.AppsItem{
			ID:        app.Id,
			Name:      app.Name,
			Status:    appStatus,
			Cluster:   cluster,
			Namespace: namespace,
		})
	}

	userApps := make([]*model.UserAppsItem, 0, len(appsMap))
	for _, projectId := range projectIds {
		app := appsMap[projectId]
		userApps = append(userApps, app)
	}

	return userApps
}

func generateUserPreferenceAppsList(apps []*pbapp.APP, debugLogsMap map[int64]*dao.DebugLog) *model.UserAppsItem {
	preferApps := make([]*model.AppsItem, 0, len(apps))
	for _, app := range apps {
		var (
			appStatus string
			cluster   string
			namespace string
		)
		if debugLog, ok := debugLogsMap[app.Id]; ok {
			appStatus = debugLog.Status.String()
			cluster = debugLog.Cluster
			namespace = debugLog.Namespace
		} else {
			appStatus = constants.DebugStatusPending.String()
		}
		preferApps = append(preferApps, &model.AppsItem{
			ID:        app.Id,
			Name:      fmt.Sprintf("%v-%v", app.Name, app.ProjectName),
			Status:    appStatus,
			Namespace: namespace,
			Cluster:   cluster,
		})
	}

	return &model.UserAppsItem{
		ProjectID:   0,
		ProjectName: "我的收藏",
		Apps:        preferApps,
	}
}
