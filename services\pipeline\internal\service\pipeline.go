//go:generate mockgen -destination=pipeline_mock.go -package=service -source=pipeline.go
package service

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"regexp"
	"slices"
	"sort"
	"strconv"
	"strings"
	"time"

	"52tt.com/cicd/pkg/constants"
	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/event"
	"52tt.com/cicd/pkg/gitlab"
	"52tt.com/cicd/pkg/godis"
	"52tt.com/cicd/pkg/harbor"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/page"
	"52tt.com/cicd/pkg/timex"
	"52tt.com/cicd/pkg/tools"
	"52tt.com/cicd/pkg/tools/cache"
	"52tt.com/cicd/pkg/tools/cond"
	"52tt.com/cicd/pkg/tools/set"
	pbapp "52tt.com/cicd/protocol/app"
	pbEvent "52tt.com/cicd/protocol/event"
	pbiam "52tt.com/cicd/protocol/iam"
	"52tt.com/cicd/services/pipeline/internal/conf"
	"52tt.com/cicd/services/pipeline/internal/dao"
	"52tt.com/cicd/services/pipeline/internal/model"
	pipelineErr "52tt.com/cicd/services/pipeline/pkg/error"
	"52tt.com/cicd/services/pipeline/pkg/tekton"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	gitlab2 "github.com/xanzy/go-gitlab"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

var _ PipelineService = (*PipelineSvc)(nil)
var allowedPreMergeActions = set.Of([]string{"open", "update", "reopen"})

type PipelineService interface {
	GetPipelineById(ctx context.Context, id int64) (*model.Pipeline, error)
	GetPipelineByAppId(ctx context.Context, appId int64) ([]model.Pipeline, error)
	GetAppPipelineByName(ctx context.Context, appId int64, name string) (*model.Pipeline, error)
	UpdatePipelineStateConfig(ctx context.Context, config *model.PipelineStateConfig) error
	GetPipelineApp(ctx context.Context, id int64) (*model.AppInPipeline, error)
	CheckCreatePipelineParams(ctx context.Context, pipeline *model.Pipeline) error
	UpdatePipeline(ctx context.Context, pipeline *model.Pipeline) error
	DeletePipeline(ctx context.Context, pipeline *model.PipelineParam) error
	CreatePipeline(ctx context.Context, pipeline *model.Pipeline) (int64, error)
	CheckImpactOnPipes(ctx context.Context, id int64) (*model.RelatedPipelines, error)
	CheckPipeline(ctx context.Context, pipeline *model.Pipeline) (bool, error)
	CheckAppBranchIsExisted(ctx context.Context, branch string, appId int64) (bool, error)
	GetPipelineList(ctx context.Context, query *model.PipelineQuery) (page.Paginator, error)
	GetAppsByIds(ctx context.Context, ids []int64, projectId int64) ([]model.AppBasicInfo, error)
	RetryCiPipeline(ctx context.Context, pipelineRun *dao.PipelineRun, args model.PipelineRunArgs) error
	ExecGitEventCallback(ctx context.Context, gitEvents map[string]interface{}) error
	ManualRunPipeline(ctx context.Context, pipelineId int64, args model.PipelineRunArgs) (*model.PipelineRun, error)
	GetPipelineNewInfo(ctx context.Context, lastRunId int64) (*model.PipelineListItem, error)
	CleanPipelineConfig(ctx context.Context, id int64) error
	UpdatePipelineConfig(ctx context.Context, updateReq *model.UpdatePipelineConfigParams) error
	UpdatePipelineBasic(ctx context.Context, pl *model.Pipeline) error
	GenerateChangeSetRunTask(ctx context.Context, pipelineRun *dao.PipelineRun, changeSet *dao.ChangeSet) ([]dao.ChangeSetRunTask, error)
	GenerateChangeSetRunTaskByRetry(ctx context.Context, pipelineRun *dao.PipelineRun, changeSet *dao.ChangeSet, retryPrTaskRun *dao.PipelineRunTask) ([]dao.ChangeSetRunTask, error)
	HandlePrepareingPipelineRun(ctx context.Context) error
	ClearCache(ctx context.Context, pipeline *model.PipelineParam) error
	DelPipelines(ctx context.Context, pipeline model.PipelineDelParam) error
}

type PipelineSvc struct {
	appClient         pbapp.AppServiceClient
	userClient        pbiam.UserServiceClient
	pipelineRepo      dao.PipelineRepository
	pipelineGroupRepo dao.PipelineGroupRepository
	tmplRepo          dao.TemplateRepository
	pipelineRunRepo   dao.PipelineRunRepository
	tektonClient      tekton.Service
	gitlabClient      gitlab.Service
	changeSetRepo     dao.ChangeSetRepository
	eventDispatcher   event.Sender
	delCache          *cache.BoolsCache
}

func NewPipelineSvc(repo dao.PipelineRepository,
	tmplRepo dao.TemplateRepository,
	pipelineRunRepo dao.PipelineRunRepository,
	pipelineGroupRepo dao.PipelineGroupRepository,
	tektonClient tekton.Service,
	gitlabClient gitlab.Service,
	appClient pbapp.AppServiceClient, userClient pbiam.UserServiceClient, changeSetRepo dao.ChangeSetRepository, eventDispatcher event.Sender) *PipelineSvc {
	return &PipelineSvc{
		appClient:         appClient,
		userClient:        userClient,
		pipelineRepo:      repo,
		pipelineGroupRepo: pipelineGroupRepo,
		tmplRepo:          tmplRepo,
		pipelineRunRepo:   pipelineRunRepo,
		tektonClient:      tektonClient,
		gitlabClient:      gitlabClient,
		changeSetRepo:     changeSetRepo,
		eventDispatcher:   eventDispatcher,
		delCache:          cache.NewBoolsCache(),
	}
}

func (p *PipelineSvc) CheckCreatePipelineParams(ctx context.Context, pipeline *model.Pipeline) error {
	if len([]rune(pipeline.Name)) > 30 {
		return errors.New("流水线名称最长30个字符")
	}
	if pipeline.TriggerByChange != 0 && pipeline.TriggerByChange != 1 {
		return errors.New("请选择是否变更触发")
	}
	if pipeline.TriggerByChange != 0 && pipeline.TriggerType != constants.MERGE.String() && pipeline.TriggerType != constants.PUSH.String() {
		return errors.New("请输入正确的触发事件类型")
	}
	if pipeline.TargetBranchType == constants.REGEX.String() {
		_, err := regexp.Compile(pipeline.TargetBranchContent)
		if err != nil {
			return errors.New("请输入正确的正则表达式")
		}
	}
	return nil
}

func (p *PipelineSvc) CheckAppBranchIsExisted(ctx context.Context, targetBranch string, appId int64) (bool, error) {
	branches, err := p.appClient.GetAppBranchList(ctx, &pbapp.AppParam{Id: appId, BranchSearch: targetBranch})
	if err != nil {
		log.ErrorWithCtx(ctx, "查询当前服务[%d]代码库分支出错", appId)
		return false, err
	}
	for _, branch := range branches.BranchList {
		if branch == targetBranch {
			return true, nil
		}
	}
	return false, nil
}

// RunPipelineAsGroup 批量运行一组流水线 (自动触发，匹配多条)
func (p *PipelineSvc) runPipelineAsGroup(ctx context.Context, event gitlab.GitEventResp, pipelines []dao.Pipeline) error {
	if event.UserID == 0 {
		return fmt.Errorf("根据gitlab user id获取用户信息失败, gitlab 等于0")
	}
	query := pbiam.GitlabQuery{GitlabId: event.UserID}
	user, err := p.userClient.GetUserByGitlabId(ctx, &query)
	if err != nil {
		log.ErrorWithCtx(ctx, "gitlab webhook rpc调用iam获取用户信息错误: %v", err)
		return err
	}
	if user == nil {
		return fmt.Errorf("gitlab webhook 根据gitlab user id获取用户信息失败")
	}

	runArgs := model.PipelineRunArgs{
		SourceBranch:         event.SourceBranch,
		Branch:               event.Branch,
		TriggerBy:            user.Id,
		TriggerByChineseName: user.ChineseName,
		TriggerByEmployeeNo:  user.EmployeeNo,
		GitlabEvents:         event.Event,
		GitlabEventType:      event.Name,
		Iid:                  event.IID,
		Description:          event.CommitMsg,
	}

	discussParameter := gitlab.DiscussionParameter{
		ProjectID: event.ProjectID,
		MrID:      event.IID,
	}
	for _, pl := range pipelines {
		pipelineRun, err := p.runPipeline(ctx, &pl, runArgs)
		if err != nil {
			log.ErrorWithCtx(ctx, "gitlab webhook 自动触发运行流水线错误: %v", err)
			return err
		}
		if constants.IsPreMergePipeline(pl.Type) && pipelineRun.IID != 0 {
			plNote := model.NewPipelineNote(pl.ID, pipelineRun.BuildNumber)
			discussId, _ := p.gitlabClient.AddDiscussion(&discussParameter, plNote)
			updates := map[string]any{
				"discuss_id": discussId,
			}
			_ = p.updatePipelineRun(ctx, pipelineRun.ID, updates)
			// 相同代码仓库 同时运行必然拉去源码失败
			time.Sleep(time.Second * 30)
		}

	}
	return nil
}

func (p *PipelineSvc) getMatchedPipelines(ctx context.Context, gitEventResp gitlab.GitEventResp) ([]dao.Pipeline, error) {
	var pipelines []dao.Pipeline
	if err := p.listMatchPipelines(ctx, gitEventResp, &pipelines); err != nil {
		log.ErrorWithCtx(ctx, "gitlab webhook获取匹配流水线信息错误: %v", err)
		return nil, err
	}
	log.InfoWithCtx(ctx, "gitlab的%s事件,操作人:%s,变更匹配流水线数量: %d", gitEventResp.Name, gitEventResp.Email, len(pipelines))

	matchBuildPath := func(changes []string, p dao.Pipeline) bool {
		// gitlab没有变更文件直接返回false，服务路径需要加个/来匹配变动的文件
		buildPathWithEndLabel := p.BuildPath
		if !strings.HasSuffix(p.BuildPath, "/") {
			buildPathWithEndLabel += "/"
		}
		return len(changes) > 0 && ((p.BuildPath != "" && tools.PrefixMatch(changes, buildPathWithEndLabel)) || (p.BuildPath == "./"))
	}
	matchBranchStrategy := func(event gitlab.GitEventResp, p dao.Pipeline) bool {
		branch := event.Branch
		if p.TargetBranchType == constants.EXACT.String() {
			return branch != "" && branch == p.TargetBranchContent
		}
		if p.TargetBranchType == constants.REGEX.String() {
			regex := regexp.MustCompile(p.TargetBranchContent)
			return regex.MatchString(branch)
		}
		return false
	}
	// first match by build path, then match by branch strategy
	var matchedPipelines []dao.Pipeline
	for _, p := range pipelines {
		if matchBuildPath(gitEventResp.Changes, p) && matchBranchStrategy(gitEventResp, p) {
			matchedPipelines = append(matchedPipelines, p)
		}
	}
	log.InfoWithCtx(ctx, "[gitlab webhook]本次%s事件变更文件匹配到 [%d] 条流水线，信息如下：\n", gitEventResp.Name, len(matchedPipelines))
	for _, p := range matchedPipelines {
		log.InfoWithCtx(ctx, "gitlab webhook [id: %d, 名称: %s, 类型: %s, 触发事件类型: %s, 触发事件分支: %s, 分支类型: %s, 构建路径: %s]",
			p.ID, p.Name, p.Type, p.TriggerType, p.TargetBranchContent, p.TargetBranchType, p.BuildPath)
	}
	return matchedPipelines, nil
}

func (p *PipelineSvc) listMatchPipelines(ctx context.Context, gitEvent gitlab.GitEventResp, pipelines interface{}) error {
	eventName := gitEvent.Name
	action := gitEvent.Action
	// 普通mr事件,则只考虑action是merge即可
	if gitlab.IsPushEvent(eventName) || (gitlab.IsMergeEvent(eventName) && gitlab.IsMergeAction(action)) {
		query := &dao.Pipeline{
			RepoAddr:        gitEvent.GitHttpUrl,
			TriggerType:     eventName,
			TriggerByChange: 1,
			Type:            constants.PRE_MERGE.String(),
		}
		if err := p.pipelineRepo.ListPipelineByRepoAddr(ctx, query, pipelines); err != nil {
			errWrap := fmt.Errorf("根据gitlab仓库地址查询对应的流水线信息错误: %w", err)
			log.ErrorWithCtx(ctx, errWrap.Error())
			return errWrap
		}
	} else if gitlab.IsMergeEvent(eventName) &&
		allowedPreMergeActions.Exists(action) {
		//&&p.gitlabClient.IsCanBeMerged(gitEvent.ProjectID, gitEvent.IID) //这个先注释掉了，感觉没啥用处。
		//如果是预合并触发事件，则考虑action是["open","update","reopen"]
		query := &dao.Pipeline{
			RepoAddr:        gitEvent.GitHttpUrl,
			TriggerType:     eventName,
			TriggerByChange: 1,
			Type:            constants.PRE_MERGE.String(),
		}
		if err := p.pipelineRepo.ListPreMergePipeline(ctx, query, pipelines); err != nil {
			errWrap := fmt.Errorf("gitlab自动触发查询预合并流水线信息错误: %w", err)
			log.ErrorWithCtx(ctx, errWrap.Error())
			return errWrap
		}
	}
	return nil
}

func (p *PipelineSvc) ExecGitEventCallback(ctx context.Context, gitEvents map[string]interface{}) error {
	gitlabEventObj, err := p.gitlabClient.GetGitlabEventMsg(gitEvents)
	if err != nil {
		log.ErrorWithCtx(ctx, "解析gitlab自动触发事件错误: %v", err)
		return err
	}
	if err != nil {
		return err
	}
	log.InfoWithCtx(ctx, "[ExecGitEventCallback]gitlab webhook 本次%s事件获取的变更数据为: %+v", gitlabEventObj.Name, gitlabEventObj)
	pipelines, err := p.getMatchedPipelines(ctx, *gitlabEventObj)
	if err != nil {
		log.ErrorWithCtx(ctx, "自动触发获取匹配流水线错误: %v", err)
		return err
	}
	// 以流水线组的形式运行一组流水线
	if len(pipelines) > 0 {
		if gitlabEventObj.LastCommitHash != "" && gitlabEventObj.SourceBranch != "" && gitlabEventObj.Name == constants.MERGE.String() && allowedPreMergeActions.Exists(gitlabEventObj.Action) {
			// 给MR添加流水线，锁住MR
			p.setPipelineOfMR(ctx, gitlabEventObj, constants.GitlabStatusRunning)
		}
		if err := p.runPipelineAsGroup(ctx, *gitlabEventObj, pipelines); err != nil {
			log.ErrorWithCtx(ctx, "gitlab webhook自动触发,批量运行流水线信息错误: %v", err)
			return err
		}
	} else {
		// 没触发MR的流水线，给gitlab 增加一个mr成功的流水线
		if gitlabEventObj.LastCommitHash != "" && gitlabEventObj.SourceBranch != "" && gitlabEventObj.Name == constants.MERGE.String() && allowedPreMergeActions.Exists(gitlabEventObj.Action) {
			p.setPipelineOfMR(ctx, gitlabEventObj, constants.GitlabStatusSuccess)
		}
	}
	return nil
}

func (p *PipelineSvc) setPipelineOfMR(ctx context.Context, gitlabEventObj *gitlab.GitEventResp, state constants.GitlabPipelineType) {
	// 给MR添加流水线，锁住MR
	setPipelineOfCommitParams := gitlab.SetPipelineOfCommitParams{
		ProjectID: gitlabEventObj.ProjectID,
		ShaID:     gitlabEventObj.LastCommitHash,
		Name:      constants.GitlabMrPipelineName,
		State:     state,
		Ref:       gitlabEventObj.SourceBranch,
	}
	err := p.gitlabClient.SetPipelineOfCommit(&setPipelineOfCommitParams)
	if err != nil {
		log.ErrorWithCtx(ctx, "gitlab webhook MR自动触发,给MR添加流水线%+v，错误: %v", setPipelineOfCommitParams, err)
	}
}

func (p *PipelineSvc) GetPipelineById(ctx context.Context, id int64) (*model.Pipeline, error) {
	var result model.Pipeline
	pipeline, err := p.pipelineRepo.GetPipeline(ctx, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "%v", err)
		return nil, err
	}
	if pipeline == nil {
		return nil, nil
	}
	err = mapstructure.Decode(pipeline, &result)
	if err != nil {
		log.ErrorWithCtx(ctx, "%v", err)
		return nil, err
	}
	err = json.Unmarshal(pipeline.Config, &(result.Config))
	if err != nil {
		log.ErrorWithCtx(ctx, "%v", err)
		return nil, err
	}
	return &result, nil
}

func (p *PipelineSvc) GetPipelineByAppId(ctx context.Context, appId int64) ([]model.Pipeline, error) {
	var results []model.Pipeline
	pipelines, err := p.pipelineRepo.GetPipelineByAppIds(ctx, []int64{appId})
	if err != nil {
		log.ErrorWithCtx(ctx, "%v", err)
		return nil, err
	}
	for _, pipe := range pipelines {
		var result model.Pipeline
		err = mapstructure.Decode(pipe, &result)
		if err != nil {
			log.ErrorWithCtx(ctx, "%v", err)
			return nil, err
		}
		marshalJSON, err := pipe.Config.MarshalJSON()
		if err != nil {
			log.ErrorWithCtx(ctx, "%v", err)
			return nil, err
		}
		err = json.Unmarshal(marshalJSON, &(result.Config))
		if err != nil {
			log.ErrorWithCtx(ctx, "%v", err)
			return nil, err
		}
		results = append(results, result)
	}
	return results, nil
}

func (p *PipelineSvc) GetAppPipelineByName(ctx context.Context, appId int64, name string) (*model.Pipeline, error) {
	var result model.Pipeline
	pipeline, err := p.pipelineRepo.GetPipelineByAppIdAndName(ctx, appId, name)
	if err != nil {
		log.ErrorWithCtx(ctx, "%v", err)
		return nil, err
	}
	if pipeline == nil {
		return nil, nil
	}
	err = mapstructure.Decode(pipeline, &result)
	if err != nil {
		log.ErrorWithCtx(ctx, "%v", err)
		return nil, err
	}
	return &result, nil
}

func (p *PipelineSvc) GetPipelineApp(ctx context.Context, id int64) (*model.AppInPipeline, error) {
	upp := pbapp.AppParam{
		Id: id,
	}
	info, err := p.appClient.GetApp(ctx, &upp)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据ID[%d]查询服务错误： %v", id, err)
		return nil, err
	}

	if info.Name == "" {
		return nil, nil
	}

	var app *model.AppInPipeline
	err = mapstructure.Decode(info, &app)
	if err != nil {
		log.ErrorWithCtx(ctx, "%v", err)
		return nil, err
	}
	return app, nil
}

func (p *PipelineSvc) UpdatePipelineStateConfig(ctx context.Context, config *model.PipelineStateConfig) error {
	pipeline, err := p.pipelineRepo.GetPipeline(ctx, config.ID)
	if err != nil {
		log.ErrorWithCtx(ctx, "更新流水线[%d]配置, 发生异常: %v", config.ID, err)
		return err
	}
	if pipeline == nil {
		log.ErrorWithCtx(ctx, "更新流水线[%d]配置, 获取流水线信息为空", config.ID)
		return pipelineErr.ErrPipelineNotFound
	}
	log.DebugWithCtx(ctx, "更新流水线[%d]配置, 获取当前的流水线配置：%+v", config.ID, pipeline.Config)

	tmpl, err := p.tmplRepo.GetTemplateById(ctx, pipeline.TemplateID)
	if err != nil {
		log.ErrorWithCtx(ctx, "更新流水线[%d]配置, 获取流水线模板信息发生异常: %v", pipeline.ID, err)
		return err
	}
	if tmpl == nil {
		log.ErrorWithCtx(ctx, "更新流水线[%d]配置, 获取流水线模板信息为空", pipeline.ID)
		return pipelineErr.ErrPipelineTemplateNotFound
	}

	var hCfg model.PipelineConfig
	err = json.Unmarshal(pipeline.Config, &hCfg)
	if err != nil {
		log.ErrorWithCtx(ctx, "更新流水线[%d]配置，配置解码为结构体出现异常: %v", pipeline.ID, err)
		return errors.New("配置解码异常导致更新异常")
	}

	mergeCfg, err := MergePipelineConfig(ctx, hCfg, config.Config)
	if err != nil {
		log.ErrorWithCtx(ctx, "更新流水线[%d]配置，合并增量配置异常: %v", pipeline.ID, err)
		return errors.New("配置合并异常导致更新异常")
	}

	tmplPipelineCfg := TemplateToPipelineConfig(ctx, tmpl)
	fullCfg, err := MergePipelineConfig(ctx, tmplPipelineCfg, mergeCfg)

	taskMapping := tmpl.ToTaskMapping()
	tasks := make([]model.Task, 0, len(fullCfg))
	for id, cfg := range fullCfg {
		// 流水线更换了模板，但config里没有清除旧任务的信息，当时讨论过，由于key是taskID是唯一的，因此当时结论是先缓存着
		// 由于上述原因，因此这里合并时需要判断下，把非当前模板的任务过滤掉
		t, ok := taskMapping[id]
		if !ok {
			continue
		}
		task := model.Task{
			ID:     id,
			Type:   t.Type,
			Name:   t.Name,
			Config: cfg,
		}
		tasks = append(tasks, task)
	}
	validateErr := model.PipelineConfigValidate(tmpl.Type, tasks)
	if validateErr != nil {
		log.ErrorWithCtx(ctx, "更新流水线[%d]配置，配置校验异常: %v", pipeline.ID, validateErr)
		return validateErr
	}

	mergedConfig, _ := json.Marshal(mergeCfg)
	return p.pipelineRepo.UpdatePipelineStateConfig(ctx, config.ID, mergedConfig)
}

func (p *PipelineSvc) CleanPipelineConfig(ctx context.Context, id int64) error {
	return p.pipelineRepo.UpdatePipelineStateConfig(ctx, id, datatypes.JSON(`{}`))
}

func (p *PipelineSvc) UpdatePipeline(ctx context.Context, pipeline *model.Pipeline) error {
	pip := dao.Pipeline{}
	err := mapstructure.Decode(pipeline, &pip)
	if err != nil {
		return err
	}
	pip.Config, err = json.Marshal(pipeline.Config)
	if err != nil {
		return err
	}
	return p.pipelineRepo.UpdatePipeline(ctx, &pip)
}

func (p *PipelineSvc) DeletePipeline(ctx context.Context, pipeline *model.PipelineParam) error {
	userInfo := cctx.GetUserinfo(ctx)
	pipelineObj, err := p.pipelineRepo.GetPipeline(ctx, pipeline.ID)
	groupId := pipelineObj.PipelineGroupID
	if err != nil {
		return err
	}
	if pipelineObj == nil {
		return pipelineErr.ErrPipelineNotFound
	}
	// 非批量流水线的删除
	pipelineObj.IsDeleted, pipelineObj.PipelineGroupID = 1, 0
	err = p.pipelineRepo.UpdatePipelineCols(ctx, pipelineObj, "IsDeleted", "PipelineGroupID")
	if err != nil {
		return err
	}
	if groupId != 0 {
		// 批量流水线的删除，需要去除pipeline_group表里的appIDs对应的app id
		pipelineGroup, err := p.pipelineGroupRepo.FindPipelineGroup(ctx, groupId)
		if err != nil {
			return err
		}
		appIDs := strings.Split(pipelineGroup.AppIDs, ",")
		var converted []string
		for _, elem := range appIDs {
			converted = append(converted, elem)
		}
		appIDStr := strconv.FormatInt(pipelineObj.AppID, 10)
		pipelineGroup.AppIDs = strings.Join(tools.RemoveElement(converted, appIDStr), ",")
		err = p.pipelineGroupRepo.UpdatePipelineGroup(ctx, pipelineGroup)
		if err != nil {
			return err
		}
	}
	log.InfoWithCtx(ctx, "%s删除流水线[%s]成功", userInfo.ChineseName, pipelineObj.Name)
	// 发流水线删除事件
	pipelineDelEvent := &pbEvent.DeletePipelineResourceEvent{
		From:                  pbEvent.DeletePipelineResourceEvent_PIPELINE,
		PipelineId:            pipelineObj.ID,
		ProjectId:             pipelineObj.ProjectID,
		CreateTime:            timestamppb.New(time.Now()),
		OperatorBy:            userInfo.UserID,
		OperatorByChineseName: userInfo.ChineseName,
		OperatorByEmployeeNo:  userInfo.EmployeeNo,
	}
	pipelineDelEventResult := p.eventDispatcher.Send(context.Background(), event.New(pipelineDelEvent, event.WithEventType(constants.ResourceDeleteEvent)))
	if event.IsUndelivered(pipelineDelEventResult) {
		log.ErrorWithCtx(ctx, "send kafka event %+v of DeletePipelineResourceEvent err: %v", pipelineDelEvent, pipelineDelEventResult)
		return pipelineDelEventResult
	}
	return nil
}

func (p *PipelineSvc) CreatePipeline(ctx context.Context, pipeline *model.Pipeline) (int64, error) {
	pip := dao.Pipeline{}
	err := mapstructure.Decode(pipeline, &pip)
	if err != nil {
		return 0, err
	}
	upp := pbapp.AppParam{
		Id: pipeline.AppId,
	}
	info, err := p.appClient.GetApp(ctx, &upp)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据ID[%d]查询服务错误： %v", pipeline.AppId, err)
		return 0, err
	}
	pip.ProjectID = info.ProjectID
	pip.Config = datatypes.JSON("{}")
	pip.Language = fmt.Sprintf("%s(%s)", info.LangName, info.LangVersion)
	pip.RepoAddr = info.RepoAddr
	pip.AppName = info.Name
	err = p.pipelineRepo.CreatePipeline(ctx, &pip)
	return pip.ID, err
}

func (p *PipelineSvc) CheckImpactOnPipes(ctx context.Context, id int64) (*model.RelatedPipelines, error) {
	pipelines, err := p.pipelineRepo.GetPipelinesByTemplateID(ctx, id)
	if err != nil {
		return nil, err
	}

	var pipelineNames []string
	for _, pipeline := range pipelines {
		pipelineNames = append(pipelineNames, pipeline.Name)
	}

	result := &model.RelatedPipelines{
		Number: len(pipelineNames),
		Names:  pipelineNames,
	}

	return result, nil
}

func (p *PipelineSvc) CheckPipeline(ctx context.Context, pipeline *model.Pipeline) (bool, error) {
	pi, err := p.pipelineRepo.GetPipelineByAppIdAndName(ctx, pipeline.AppId, pipeline.Name)
	if err != nil {
		return false, err
	}

	if pi != nil && pi.ID != pipeline.ID {
		return true, nil
	}
	upp := pbapp.AppParam{
		Id: pipeline.AppId,
	}
	app, err := p.appClient.GetApp(ctx, &upp)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据ID[%d]查询服务错误： %v", pi.AppID, err)
		return false, err
	}
	tmpl, err := p.tmplRepo.GetTemplateById(ctx, pipeline.TemplateId)
	if err != nil {
		log.ErrorWithCtx(ctx, "更新流水线[%d]基础配置, 获取流水线模板信息发生异常: %v", pipeline.ID, err)
		return false, err
	}
	if tmpl == nil {
		log.ErrorWithCtx(ctx, "更新流水线[%d]配置, 获取流水线模板信息为空", pipeline.ID)
		return false, err
	}

	if strings.TrimSpace(app.LangName) != strings.TrimSpace(tmpl.Language) {
		msg := fmt.Sprintf("当前服务[%s]的语言与流水线模板语言不匹配", app.Name)
		log.ErrorWithCtx(ctx, msg)
		return false, errors.New(msg)
	}
	return false, nil
}

func (p *PipelineSvc) GetPipelineList(ctx context.Context, query *model.PipelineQuery) (page.Paginator, error) {
	var total int64
	pipelines, err := p.pipelineRepo.PagePipeline(ctx, query, &total)
	if err != nil {
		log.DebugWithCtx(ctx, "查询流水线列表发生错误： %v", err)
		return nil, err
	}
	if query.Status != "" && query.Status.IsBelongTo(constants.PENDING) {
		//查询那些从未运行的
		var totalNeverRun int64
		neverRunPipelines, err := p.pipelineRepo.PagePipelinesNeverRun(ctx, query, &totalNeverRun)
		if err != nil {
			log.ErrorWithCtx(ctx, "查询从未运行的流水线列表发生错误： %v", err)
			return nil, err
		}
		total = total + totalNeverRun
		pipelines = append(pipelines, neverRunPipelines...)
	}
	// 获取应用id列表
	var appIds []int64
	for _, pipeline := range pipelines {
		appIds = append(appIds, pipeline.AppID)
	}
	apps, getAppErr := p.GetAppsByIds(ctx, appIds, 0)
	if getAppErr != nil {
		return nil, getAppErr
	}
	appMapping := make(map[int64]string)
	for _, app := range apps {
		appMapping[app.ID] = app.Name
	}

	pipeList := make([]model.PipelineListItem, 0)
	for _, pipeline := range pipelines {
		pipeList = append(pipeList, p.buildPipeInfo(ctx, &pipeline, appMapping))
	}
	pg := page.PageOf(query, pipeList, total)

	return pg, nil
}

func (p *PipelineSvc) GetAppsByIds(ctx context.Context, ids []int64, projectId int64) ([]model.AppBasicInfo, error) {
	info, err := p.appClient.GetAppListByIds(ctx, &pbapp.AppsReq{Id: ids, ProjectId: projectId})
	if err != nil {
		log.ErrorWithCtx(ctx, "根据应用id[%v]查询应用列表出错:%v", ids, err)
		return nil, err
	}
	var apps []model.AppBasicInfo
	err = mapstructure.Decode(info.Apps, &apps)
	if err != nil {
		log.ErrorWithCtx(ctx, "应用数据转换，发生异常: %v", err)
		return nil, err
	}
	return apps, nil
}

func (p *PipelineSvc) buildPipeInfo(ctx context.Context, pipeline *dao.Pipeline, appMapping map[int64]string) model.PipelineListItem {
	latestRunInfo := p.buildPipeRunInfo(ctx, pipeline)
	pipeInfo := model.PipelineListItem{
		Pipeline: model.Pipeline{
			ID:                  pipeline.ID,
			Name:                pipeline.Name,
			Type:                pipeline.Type,
			Language:            pipeline.Language,
			RepoAddr:            pipeline.RepoAddr,
			BuildPath:           pipeline.BuildPath,
			AppName:             appMapping[pipeline.AppID],
			AppId:               pipeline.AppID,
			TriggerType:         pipeline.TriggerType,
			TriggerByChange:     pipeline.TriggerByChange,
			TemplateId:          pipeline.TemplateID,
			TemplateName:        pipeline.Template.Name,
			TargetBranchType:    pipeline.TargetBranchType,
			TargetBranchContent: pipeline.TargetBranchContent,
			IsAutoMerge:         pipeline.IsAutoMerge,
			ProjectID:           pipeline.ProjectID,
		},
		LatestRunInfo: latestRunInfo,
	}
	pipelineGroupId := pipeline.PipelineGroupID
	if pipelineGroupId != 0 {
		pipeInfo.PipelineGroupID = &pipelineGroupId
	}
	return pipeInfo
}

func (p *PipelineSvc) buildPipeRunInfo(ctx context.Context, pipeline *dao.Pipeline) *model.LatestRunInfo {
	latestRunID := pipeline.LastRunID
	if latestRunID != 0 {
		runInst, err := p.pipelineRunRepo.FindPipelineRunStages(ctx, latestRunID)
		if err != nil {
			log.DebugWithCtx(ctx, "查询流水线实例信息错误: %v", err)
			return nil
		}
		if runInst == nil {
			return nil
		}

		stageSequences := strings.Split(runInst.StageSequence, ",")
		stages := runInst.Stages
		var sortedStages []model.StageRunInfo
		for _, stageSeq := range stageSequences {
			stageResult := tools.Get(stages, func(stage dao.PipelineRunStage) bool {
				stageId, _ := strconv.ParseInt(stageSeq, 10, 64)
				return stage.ID == stageId
			})
			sortedStages = append(sortedStages, model.StageRunInfo{
				Name:   stageResult.Name,
				Status: stageResult.Status,
			})
		}

		latestRunInfo := model.LatestRunInfo{
			PipelineRunId:      runInst.ID,
			Status:             runInst.Status,
			SourceBranch:       runInst.SourceBranch,
			Branch:             runInst.Branch,
			StartedTime:        &runInst.CreatedAt,
			CompletedTime:      &runInst.CompletedTime,
			Stages:             sortedStages,
			TriggerUserId:      runInst.TriggerBy,
			TriggerChineseName: runInst.TriggerByChineseName,
			TriggerEmployeeNo:  runInst.TriggerByEmployeeNo,
		}
		return &latestRunInfo
	}
	return nil
}

func (p *PipelineSvc) ManualRunPipeline(ctx context.Context, pipelineId int64, arg model.PipelineRunArgs) (*model.PipelineRun, error) {
	pipeline, err := p.pipelineRepo.GetPipelineRelated(ctx, pipelineId)
	if err != nil || pipeline == nil {
		log.ErrorWithCtx(ctx, "manual run pipeline %s not found", pipelineId)
		return nil, fmt.Errorf("manual run pipeline[%d] error: %v", pipelineId, err)
	}
	if constants.IsPreMergePipeline(pipeline.Type) {
		err = fmt.Errorf("can not manual run PreMerge Pipeline")
		return nil, err
	}
	_, err = p.gitlabClient.GetBranch(pipeline.RepoAddr, arg.Branch)
	if err != nil {
		log.ErrorWithCtx(ctx, "manual run pipeline get branch %s failed: %v", arg.Branch, err)
		return nil, fmt.Errorf("%w %s", pipelineErr.ErrBranchNotFound, err)
	}
	run, err := p.runPipeline(ctx, pipeline, arg)
	if err != nil {
		log.ErrorWithCtx(ctx, "manual run pipeline %d failed: %v", pipelineId, err)
		return nil, err
	}

	return &model.PipelineRun{BaseModel: db.BaseModel{ID: run.ID}}, nil
}

func (p *PipelineSvc) RetryCiPipeline(ctx context.Context, pipelineRun *dao.PipelineRun, args model.PipelineRunArgs) error {
	tektonPipeline, err := p.tektonClient.CreatePipelineFromPr(ctx, pipelineRun, args)
	if err != nil {
		return err
	}
	appInfo, err := p.appClient.GetAppInfo(ctx, &pbapp.GetAppInfoReq{AppId: pipelineRun.Pipeline.AppID})
	if err != nil {
		log.ErrorWithCtx(ctx, "重试流水线运行CI阶段，获取服务信息，发生异常: %v", err)
		return err
	}
	log.InfoWithCtx(ctx, "retry create tekton pipeline success: %s", tektonPipeline.Name)
	// 创建pipeline run
	// create pipeline_run pipeline_run_stage pipeline_run_task
	changeSetID := int64(0)
	if err := db.Transaction(func(tx *gorm.DB) error {
		txCtx := db.CtxWithTX(ctx, tx)
		tPipelineRun := *pipelineRun
		requestDataParam := tekton.WithRequestDate(timex.ToYearMonth(args.RequestDate)) // 流水线重试时，RequestDate 需要继续使用第一次运行得，不然会导致PV的只读层重新进行挂载
		identityParam := tekton.WithProjectIdentity(appInfo.GetProject().GetIdentity())
		appParam := tekton.WithAppInfo(appInfo.GetApp().GetName(), appInfo.GetApp().GetCmdbId())
		tpr, err := p.tektonClient.CreatePipelineRun(ctx, &tPipelineRun, tektonPipeline, requestDataParam, tekton.WithChangesetID(changeSetID), identityParam, appParam)
		if err != nil {
			return err
		}
		pipelineRun.TektonNamespace = tpr.Namespace
		pipelineRun.TektonName = tpr.Name
		if err := p.pipelineRunRepo.UpdatePipelineRunTektonField(ctx, tx, pipelineRun); err != nil {
			return err
		}
		// update pipeline last run id
		if err := p.pipelineRepo.UpdatePipelineLastRunId(txCtx, pipelineRun.PipelineId, pipelineRun.ID); err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "retry create pipeline run error: %v", err)
		return err
	}
	return nil
}

func (p *PipelineSvc) ensurePipeline(ctx context.Context, pipeline *dao.Pipeline, args model.PipelineRunArgs) (*v1beta1.Pipeline, error) {
	tektonPipeline, err := p.tektonClient.GetPipeline(ctx, pipeline.Template.TektonNamespace, pipeline.Template.TektonName)
	if err != nil {
		if errors.Is(err, pipelineErr.ErrTektonPipelineNotFound) || !pipeline.Template.HasTektonFiled() {
			// create Tekton Pipeline again
			if tektonPipeline, err = p.tektonClient.CreatePipeline(ctx, pipeline.Template, args); err != nil {
				return nil, err
			} else {
				pipeline.Template.TektonName = tektonPipeline.Name
				pipeline.Template.TektonNamespace = tektonPipeline.Namespace
				err = db.Transaction(func(tx *gorm.DB) error {
					if err = p.tmplRepo.UpdateTemplateTektonField(ctx, pipeline.Template); err != nil {
						return err
					}
					return nil
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "bind tekton Pipeline with template failed: %v", err)
					return nil, err
				}
			}
			log.InfoWithCtx(ctx, "create tekton pipeline success: %s", tektonPipeline.Name)
		} else {
			log.ErrorWithCtx(ctx, "get tekton Pipeline faild: %v", err)
			return nil, err
		}
	}
	return tektonPipeline, nil
}

// runPipeline for manual and automation run pipeline
// 限流的说明：大批量请求进入时，以 Project 为单位加锁，以达到串行的效果。
// 未获取到锁的请求，进入“等待中”队列。这里就有可能出现：2 个请求并发且为达到限流阈值，但是未获取到锁的请求无法执行，只能等待。这里通过自旋的方式，等待获取锁。
func (p *PipelineSvc) runPipeline(ctx context.Context, pipeline *dao.Pipeline, args model.PipelineRunArgs) (*dao.PipelineRun, error) {

	var (
		tektonPipeline *v1beta1.Pipeline
		err            error
		changeSet      *dao.ChangeSet
		changeSetId    int64
		isPrepareingPR bool
	)

	canRun, err := p.checkOneRunning(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	if !canRun {
		return nil, pipelineErr.ErrMustOneByOne
	}

	// ensure Tekton Pipeline and database template are the same
	if tektonPipeline, err = p.ensurePipeline(ctx, pipeline, args); err != nil {
		return nil, err
	}
	// query the change set associated with the pipeline
	changeSetPipeline, err := p.changeSetRepo.FindRelatedPipelinesById(ctx, pipeline.ID)
	if err != nil {
		log.ErrorWithCtx(ctx, "查询关联pipeline[%d]的变更集失败: %v", pipeline.ID, err)
		return nil, err
	}
	if changeSetPipeline != nil && changeSetPipeline.ChangeSet != nil {
		changeSet = changeSetPipeline.ChangeSet
		changeSetId = changeSet.ID
	}
	pipelineRun, err := p.generatePipelineRun(ctx, pipeline, changeSet, args)
	if err != nil {
		log.ErrorWithCtx(ctx, "generate dao pipeline run failed %v", err)
		return nil, err
	}
	projectCommit, appCommit, err := p.getPipelineRunCommitInfo(ctx, pipeline, args)
	if err != nil {
		log.ErrorWithCtx(ctx, "get pipeline run commit info failed %v", err)
		return nil, err
	}
	// todo 这里改成webhook里拿出commitId更精准,放到args里去
	if projectCommit != nil {
		projectCommitEvent, _ := json.Marshal(projectCommit)
		pipelineRun.CommittedEvent = projectCommitEvent
		pipelineRun.CommittedID = projectCommit.ID
		pipelineRun.CommittedDate = *projectCommit.CommittedDate
	}
	if appCommit != nil {
		appCommitEvent, _ := json.Marshal(appCommit)
		pipelineRun.AppCommittedDate = *appCommit.CommittedDate
		pipelineRun.AppCommittedEvent = appCommitEvent
	}
	appInfo, err := p.appClient.GetAppInfo(ctx, &pbapp.GetAppInfoReq{AppId: pipeline.AppID})
	if err != nil {
		log.ErrorWithCtx(ctx, "运行流水线之前，获取服务信息，发生异常: %v", err)
		return nil, err
	}
	// 抢占一个计算 pipeline_run quota 的锁，若未抢到则意味着现在有大量的创建请求，或者是锁释放存在问题。这种情况只能创建“准备中” pipeline_run。
	quotaLockKey := fmt.Sprintf("pipelineQuotaProject%dLock", pipeline.ProjectID)
	quotaLock, err := godis.LockWithSpin(ctx, quotaLockKey, "1", 30, time.Millisecond*100) // 带自旋锁的强制
	if err != nil {
		log.ErrorWithCtx(ctx, "get %s error: %s", quotaLockKey, err)
		return nil, err
	}
	if !quotaLock {
		log.WarnWithCtx(ctx, "can't get quota lock for project %d, create prepareing pipeline_run", pipeline.ProjectID)
	}
	if err := db.Transaction(func(tx *gorm.DB) error {
		txCtx := db.CtxWithTX(ctx, tx)
		// Tekton 资源配额检查
		projectQuota, err := p.calculateProjectPipelineRunQuota(ctx, []int64{appInfo.Project.Id})
		if err != nil {
			log.ErrorWithCtx(ctx, "calculate project pipeline run quota failed: %v", err)
			return err
		}
		quota, ok := projectQuota[pipeline.ProjectID]
		if !ok {
			log.ErrorWithCtx(ctx, "project %d not found in projectQuota", appInfo.Project.Id)
			return fmt.Errorf("project %d not found in projectQuota", appInfo.Project.Id)
		}
		if quota > 0 && quotaLock {
			pipelineRun.Status = constants.PENDING
		} else {
			// 该项目的 Tekton 资源没有配额，只能创建数据，Tekton 资源后续定时任务会根据资源情况创建 Tekton。
			log.WarnWithCtx(ctx, "project[%d] pipeline_run %d quota %d hit limit, can't create Tekton PipelineRun", appInfo.Project.Id, pipelineRun.ID, quota)
			isPrepareingPR = true
			pipelineRun.Status = constants.PREPARING
		}
		// create pipeline_run pipeline_run_stage pipeline_run_task
		if err := p.pipelineRunRepo.CreatePipelineRun(txCtx, pipelineRun, false); err != nil {
			return err
		}
		// update pipeline last run id
		if err := p.pipelineRepo.UpdatePipelineLastRunId(txCtx, pipelineRun.PipelineId, pipelineRun.ID); err != nil {
			return err
		}
		//update automation Deploy task run approvalTaskRunId of config
		_, err = p.updateApprovalTaskRunIdField(pipelineRun)
		if err != nil {
			log.ErrorWithCtx(ctx, "update ApprovalTaskRunIdField failed")
			return err
		}
		if err = p.pipelineRunRepo.TxUpdatePipelineRunAssociation(txCtx, pipelineRun); err != nil {
			log.ErrorWithCtx(ctx, "update ApprovalTaskRunIdField failed")
			return err
		}
		//generate and create multiCloudTaskRun
		multiCloudTaskRun, err := p.generateSubTask(ctx, pipelineRun)
		if err != nil {
			log.ErrorWithCtx(ctx, "generate dao multiCloudTask run failed %v", err)
			return err
		}
		if err := p.pipelineRepo.CreateMultiCloudTaskRun(txCtx, multiCloudTaskRun); err != nil {
			log.ErrorWithCtx(ctx, "create dao multiCloudTask run failed %v", err)
			return err
		}
		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "create pipeline run error: %v", err)
		return nil, err
	}

	godis.Unlock(ctx, quotaLockKey)

	if isPrepareingPR {
		log.InfoWithCtx(ctx, "send pipeline_run %d prepareing kafka message", pipelineRun.ID)
		e := &pbEvent.PipelineRunEvent{
			Id:                   pipelineRun.ID,
			StartedTime:          timestamppb.New(time.Now()),
			PipelineId:           pipelineRun.PipelineId,
			AppId:                pipeline.AppID,
			Status:               constants.PREPARING.String(),
			TriggerById:          pipelineRun.TriggerBy,
			TriggerByChineseName: pipelineRun.TriggerByChineseName,
			BuildNumber:          pipelineRun.BuildNumber,
		}
		ce := event.NewPipelineRelatedEvent(e, event.WithEventType("pipelinerun"))
		ce.TktEvtId = "not from tekton"
		result := p.eventDispatcher.Send(ctx, ce)
		if event.IsUndelivered(result) {
			log.ErrorWithCtx(ctx, "send pipeline_run %d prepareing kafka event %+v of PipelineRunEvent err: %v", pipelineRun.ID, e, result)
		} else {
			log.InfoWithCtx(ctx, "send pipeline_run %d prepareing kafka event %+v of PipelineRunEvent success", pipelineRun.ID, e)
		}
	} else {
		// 创建 Tekton 资源，没有事务保障，可能出错而没有创建资源。
		tPipelineRun := *pipelineRun
		tPipelineRun.Pipeline = pipeline
		requestDataParam := tekton.WithRequestDate(timex.ToYearMonth(tPipelineRun.CreatedAt))
		identityParam := tekton.WithProjectIdentity(appInfo.GetProject().GetIdentity())
		appParam := tekton.WithAppInfo(appInfo.GetApp().GetName(), appInfo.GetApp().GetCmdbId())
		tpr, err := p.tektonClient.CreatePipelineRun(ctx, &tPipelineRun, tektonPipeline, requestDataParam, tekton.WithChangesetID(changeSetId), identityParam, appParam)
		var updateFields map[string]interface{}
		if err != nil {
			log.ErrorWithCtx(ctx, "create tekton PipelineRun for pipeline_run %d failed: %v", pipelineRun.ID, err)
			updateFields = map[string]interface{}{ // Notice: 通过Prepareing状态，然后定时任务来尝试创建资源。（没时间做系统设计，只能先这样实现功能）
				"status": constants.PREPARING,
			}
		} else {
			pipelineRun.TektonNamespace = tpr.Namespace
			pipelineRun.TektonName = tpr.Name
			updateFields = map[string]interface{}{
				"tekton_namespace": tpr.Namespace,
				"tekton_name":      tpr.Name,
				"tekton_at":        time.Now(),
			}
		}
		if err = p.pipelineRunRepo.UpdatePipelineRunFields(ctx, pipelineRun.ID, updateFields); err != nil {
			log.ErrorWithCtx(ctx, "update pipeline run tekton and status failed: %v", err)
			return nil, err
		}
	}

	// generate changeSetRunTask and update changeSetPipeline
	if changeSet != nil && changeSet.Status != constants.SUCCESSFUL {
		changeSetRunTask, err := p.GenerateChangeSetRunTask(ctx, pipelineRun, changeSet)
		if err != nil {
			log.ErrorWithCtx(ctx, "generate dao changeSetTask run failed %v", err)
			return nil, err
		}
		err = p.changeSetRepo.CreateChangeSetRunTask(ctx, changeSetRunTask)
		if err != nil {
			log.ErrorWithCtx(ctx, "create dao changeSetTask run failed %v", err)
			return nil, err
		}
		if changeSet.Status.IsBelongTo(constants.PENDING, constants.CANCEL, constants.FAILED, constants.UNHANDLED) {
			updates := UpdateFields{
				"pipeline_run_id": pipelineRun.ID,
			}
			if changeSet.Status != constants.PENDING && changeSet.Status != constants.UNHANDLED {
				updates["tip"] = constants.ALREADY_UPDATE
			}
			if err = p.changeSetRepo.UpdateChSetPipelineByChSetByAndPipeId(context.Background(), changeSetId, pipeline.ID, updates); err != nil {
				log.ErrorWithCtx(ctx, "update pipelineRunId and tip of changeSetPipeline failed %v", err)
				return nil, err
			}
		}
	}
	return pipelineRun, nil
}

func (p *PipelineSvc) getPipelineRunCommitInfo(ctx context.Context, pipeline *dao.Pipeline, args model.PipelineRunArgs) (*gitlab2.Commit, *gitlab2.Commit, error) {
	var projectCommit, appCommit *gitlab2.Commit
	project, err := p.gitlabClient.GetProject(pipeline.RepoAddr)
	if err != nil {
		log.ErrorWithCtx(ctx, "[getPipelineRunCommitInfo] get gitlab project error: %v, repoAddr", err, pipeline.RepoAddr)
		return nil, nil, err
	}
	// 获取当前项目的提交记录
	// 预发布流水线获取源分支的commit
	refName := cond.Or(constants.IsPreMergePipeline(pipeline.Type), args.SourceBranch, args.Branch)
	params := map[string]string{
		"ref_name": refName,
		"per_page": "1", // 只获取最新的提交记录
	}
	commits, err := p.gitlabClient.GetCommits(ctx, project.ID, params)
	if err != nil {
		log.ErrorWithCtx(ctx, "[getPipelineRunCommitInfo] get gitlab project[%d] commits error: %v, params: %+v", err, project.ID, params)
		return nil, nil, err
	}
	if len(commits) > 0 {
		projectCommit = &commits[0]
	}
	// 获取当前服务路径下的提交记录
	params["path"] = pipeline.BuildPath
	log.DebugWithCtx(ctx, "[getPipelineRunCommitInfo] get commit info params: %+v", params)
	commits, err = p.gitlabClient.GetCommits(ctx, project.ID, params)
	if err != nil {
		log.ErrorWithCtx(ctx, "[getPipelineRunCommitInfo] get gitlab project[%d] path[%v] commits error: %v, params: %+v", err, project.ID, pipeline.BuildPath, params)
		return nil, nil, err
	}
	if len(commits) > 0 {
		appCommit = &commits[0]
	}

	return projectCommit, appCommit, nil
}

func (p *PipelineSvc) GenerateChangeSetRunTask(ctx context.Context, pipelineRun *dao.PipelineRun, changeSet *dao.ChangeSet) ([]dao.ChangeSetRunTask, error) {
	changeSetStageIdMap := make(map[int64]int64)
	changeSetTaskIdMap := make(map[int64]int64)
	for _, changeSetStage := range changeSet.Stages {
		changeSetStageIdMap[changeSetStage.StageID] = changeSetStage.ID
		for _, changeSetTask := range changeSetStage.Tasks {
			changeSetTaskIdMap[changeSetTask.TaskID] = changeSetTask.ID
		}
	}
	tektonConfig := conf.AppConfig.Tekton
	// build changeSetRunTask
	var res []dao.ChangeSetRunTask
	changeSetStages := conf.GetInChangeSetStageConfig()
	isInChangeSet := false
	firstChangeSetStage := -1
	for i, stage := range pipelineRun.Stages {
		if !isInChangeSet {
			_, isInChangeSet = changeSetStages[stage.Type]
			firstChangeSetStage = i
		}
		for j, task := range stage.Tasks {
			if isInChangeSet {
				// 变更集第一个阶段第一个任务是提测审批任务 不加隐身节点
				if firstChangeSetStage != i || j != 0 || task.Type != constants.TASK_TEST_APPROVAL.String() {
					// 变更集管控CD阶段每个任务前加隐身节点
					res = append(res, dao.ChangeSetRunTask{
						ChangeSetID:        pipelineRun.ChangeSetId,
						ChangeSetStageID:   changeSetStageIdMap[stage.StageId],
						ChangeSetTaskID:    changeSetTaskIdMap[task.TaskId],
						PipelineID:         pipelineRun.PipelineId,
						PipelineRunID:      pipelineRun.ID,
						PipelineRunStageID: stage.ID,
						PipelineRunTaskID:  0,
						Type:               constants.TASK_VIRTUAL_NODE,
						TektonNamespace:    tektonConfig.Namespace,
						TektonLabel:        fmt.Sprintf("%s-cd-change-set-run-task-%d", tektonConfig.Env, changeSetTaskIdMap[task.TaskId]),
					})
				}
			}
			changeSetRuntask := dao.ChangeSetRunTask{
				ChangeSetID:        pipelineRun.ChangeSetId,
				ChangeSetStageID:   changeSetStageIdMap[stage.StageId],
				ChangeSetTaskID:    changeSetTaskIdMap[task.TaskId],
				PipelineID:         pipelineRun.PipelineId,
				PipelineRunID:      pipelineRun.ID,
				PipelineRunStageID: stage.ID,
				PipelineRunTaskID:  task.ID,
				Type:               task.GetType(),
				TektonNamespace:    tektonConfig.Namespace,
			}
			res = append(res, changeSetRuntask)
		}
	}
	return res, nil
}

func (p *PipelineSvc) GenerateChangeSetRunTaskByRetry(ctx context.Context, pipelineRun *dao.PipelineRun, changeSet *dao.ChangeSet, retryPrTaskRun *dao.PipelineRunTask) ([]dao.ChangeSetRunTask, error) {
	log.InfoWithCtx(ctx, "GenerateChangeSetRunTaskByRetry, pipelineRun: %+v, changeSet: %+v, retryPrTaskRun: %+v", pipelineRun, changeSet, retryPrTaskRun)
	changeSetStageIdMap := make(map[int64]int64)
	changeSetTaskIdMap := make(map[int64]int64)
	for _, changeSetStage := range changeSet.Stages {
		changeSetStageIdMap[changeSetStage.StageID] = changeSetStage.ID
		for _, changeSetTask := range changeSetStage.Tasks {
			changeSetTaskIdMap[changeSetTask.TaskID] = changeSetTask.ID
		}
	}
	tektonConfig := conf.AppConfig.Tekton
	// build changeSetRunTask
	var res []dao.ChangeSetRunTask
	changeSetStages := conf.GetInChangeSetStageConfig()
	isInChangeSet := false
	for _, stage := range pipelineRun.Stages {
		if !isInChangeSet {
			_, isInChangeSet = changeSetStages[stage.Type]
		}
		for _, task := range stage.Tasks {
			if isInChangeSet {
				// 重试节点前不创建虚拟任务
				if task.ID != retryPrTaskRun.ID {
					res = append(res, dao.ChangeSetRunTask{
						ChangeSetID:        pipelineRun.ChangeSetId,
						ChangeSetStageID:   changeSetStageIdMap[stage.StageId],
						ChangeSetTaskID:    changeSetTaskIdMap[task.TaskId],
						PipelineID:         pipelineRun.PipelineId,
						PipelineRunID:      pipelineRun.ID,
						PipelineRunStageID: stage.ID,
						PipelineRunTaskID:  0,
						Type:               constants.TASK_VIRTUAL_NODE,
						TektonNamespace:    tektonConfig.Namespace,
						TektonLabel:        fmt.Sprintf("%s-cd-change-set-run-task-%d", tektonConfig.Env, changeSetTaskIdMap[task.TaskId]),
					})
				}
			}
			changeSetRuntask := dao.ChangeSetRunTask{
				ChangeSetID:        pipelineRun.ChangeSetId,
				ChangeSetStageID:   changeSetStageIdMap[stage.StageId],
				ChangeSetTaskID:    changeSetTaskIdMap[task.TaskId],
				PipelineID:         pipelineRun.PipelineId,
				PipelineRunID:      pipelineRun.ID,
				PipelineRunStageID: stage.ID,
				PipelineRunTaskID:  task.ID,
				Type:               task.GetType(),
				TektonNamespace:    tektonConfig.Namespace,
			}
			res = append(res, changeSetRuntask)
		}
	}
	return res, nil
}

func (p *PipelineSvc) updateApprovalTaskRunIdField(pipelineRun *dao.PipelineRun) (*dao.PipelineRun, error) {
	for i := 0; i < len(pipelineRun.Stages); i++ {
		for j := 0; j < len(pipelineRun.Stages[i].Tasks); j++ {
			if pipelineRun.Stages[i].Tasks[j].Type != string(constants.TASK_AUTOMATION_DEPLOY) {
				continue
			}

			if j-1 >= 0 && pipelineRun.Stages[i].Tasks[j-1].GetType().IsBelongTo(
				constants.TASK_TEST_APPROVAL,
				constants.TASK_GRAY_UPGRADE_APPROVAL,
				constants.TASK_UPGRADE_APPROVAL) {

				automationDeployConfig := model.AutomationDeploy{MultiEnv: []model.MultiEnv{}}
				err := json.Unmarshal(pipelineRun.Stages[i].Tasks[j].Config, &automationDeployConfig)
				if err != nil {
					return nil, err
				}
				automationDeployConfig.ApprovalTaskRunId = pipelineRun.Stages[i].Tasks[j-1].ID

				automationDeployConfigByte, err := json.Marshal(automationDeployConfig)
				if err != nil {
					return nil, err
				}

				pipelineRun.Stages[i].Tasks[j].Config = automationDeployConfigByte
			}
		}
	}
	return pipelineRun, nil
}

// generatePipelineRun just create dao object
func (p *PipelineSvc) generatePipelineRun(ctx context.Context, pipeline *dao.Pipeline, changeSet *dao.ChangeSet, args model.PipelineRunArgs) (*dao.PipelineRun, error) {
	var buildNumber int64
	lastRun, err := p.pipelineRunRepo.GetLastRunByPipeline(context.Background(), pipeline.ID)
	if err != nil {
		log.ErrorWithCtx(ctx, "get last pipeline run failed: %v", err)
		return nil, err
	}
	if lastRun == nil {
		buildNumber = 1
	} else {
		buildNumber = lastRun.BuildNumber + 1
	}

	events, _ := json.Marshal(args.GitlabEvents)

	pipelineRun := dao.PipelineRun{
		PipelineId:           pipeline.ID,
		AppId:                pipeline.AppID,
		BuildNumber:          buildNumber,
		RepoAddress:          pipeline.RepoAddr,
		SourceBranch:         args.SourceBranch,
		Branch:               args.Branch,
		TriggerBy:            args.TriggerBy,
		TriggerByChineseName: args.TriggerByChineseName,
		TriggerByEmployeeNo:  args.TriggerByEmployeeNo,
		GitlabEventType:      args.GitlabEventType,
		GitlabEvents:         events,
		IID:                  args.Iid,
		IsAutoMerge:          pipeline.IsAutoMerge,
		Status:               constants.PENDING,
		Description:          args.Description,
	}
	stages := make([]dao.PipelineRunStage, len(pipeline.Template.Stages))
	tempConfig := TemplateToPipelineConfig(ctx, pipeline.Template)
	var pipelineConfig model.PipelineConfig
	var mergedConfig model.PipelineConfig
	if pipeline.Config != nil {
		err = json.Unmarshal(pipeline.Config, &pipelineConfig)
		if err != nil {
			log.ErrorWithCtx(ctx, "pipeline %d config %s invalid: %v", pipeline.ID, pipeline.Config, err)
			return nil, err
		}
		mergedConfig, err = MergePipelineConfig(ctx, tempConfig, pipelineConfig)
		if err != nil {
			log.ErrorWithCtx(ctx, "merge pipeline config TemplateConfig %v PipelineConfig %v error: %v", err, tempConfig, pipelineConfig)
			return nil, err
		}
	} else {
		mergedConfig = tempConfig
	}
	if len(mergedConfig) == 0 {
		log.ErrorWithCtx(ctx, "generate PipelineRun config is empty")
		return nil, errors.New("generate PipelineRun config is empty")
	}
	// genearte pipeline_run_task pipeline_run_stage
	for sIndex, stageId := range pipeline.Template.GetSequence() {
		s := pipeline.Template.GetStageById(ctx, stageId)
		if s == nil {
			log.ErrorWithCtx(ctx, "template [%d] stage %d not found in sequence", pipeline.TemplateID, stageId)
			return nil, fmt.Errorf("template [%d] stage %d not found in sequence", pipeline.TemplateID, stageId)
		}
		tasks := make([]dao.PipelineRunTask, len(s.Tasks))
		for tIndex, taskId := range s.GetSequence() {
			t := s.GetTaskById(taskId)
			if t == nil {
				log.ErrorWithCtx(ctx, "template [%d] stage %d task %d not found in sequence", pipeline.TemplateID, stageId, taskId)
				return nil, fmt.Errorf("template [%d] stage %d task %d not found in sequence", pipeline.TemplateID, stageId, taskId)
			}
			cfgResult := mergedConfig[t.ID]
			taskRunConfig, err := json.Marshal(cfgResult)
			if err != nil {
				log.ErrorWithCtx(ctx, "marshal run_task config %v error: %v", mergedConfig[t.ID], err)
				return nil, err
			}

			taskRun := dao.PipelineRunTask{
				PipelineRun: &pipelineRun,
				TaskId:      t.ID,
				Name:        t.Name,
				Type:        t.Type,
				Status:      constants.PENDING,
				Config:      taskRunConfig,
			}
			// 拉取镜像任务，查看最近5次运行记录，并计算耗时
			if t.GetType() == constants.TASK_GENERATE_PUSH_IMAGE {
				taskRun.NeedSuperNode = p.needSuperNode(ctx, pipeline, t)
			}

			tasks[tIndex] = taskRun
		}
		stages[sIndex] = dao.PipelineRunStage{
			Name:    s.Name,
			Type:    s.Type,
			Status:  constants.PENDING,
			Tasks:   tasks,
			StageId: s.ID,
		}
	}
	pipelineRun.Stages = stages
	// determines whether to associate change sets
	if changeSet != nil && changeSet.Status != constants.SUCCESSFUL {
		pipelineRun.ChangeSetId = changeSet.ID
	}
	return &pipelineRun, nil
}

// 计算是否要调度到超级节点
func (p *PipelineSvc) needSuperNode(ctx context.Context, pipeline *dao.Pipeline, task *dao.Task) (needSuperNode bool) {
	// 特定项目组，使用超级节点
	if conf.AppConfig.Tekton.UseSpuerNode == pipeline.ProjectID {
		return true
	}

	superPrjs := strings.Split(conf.AppConfig.Tekton.SpuerNodePrjs, ",")
	if slices.Contains(superPrjs, fmt.Sprintf("%d", pipeline.ProjectID)) {
		needSuperNode = true
		return
	}

	// needSuperNode = true
	// // 查询该流水线  历史成功的拉取镜像任务的耗时
	// objs, err := p.pipelineRunRepo.FindTaskRunByPipeline(ctx, pipeline.ID, model.TaskRunSearch{
	// 	Type:   task.Type,
	// 	TaskId: task.ID,
	// 	Limit:  3,
	// 	Status: constants.SUCCESSFUL,
	// })
	// if err != nil {
	// 	log.ErrorWithCtx(ctx, "needSuperNode find taskruns by pipeline %d task %d error: %v", pipeline.ID, task.ID, err)
	// 	return
	// }
	// // 样本太少，往超级节点调度
	// if len(objs) < 3 {
	// 	return
	// }

	// allDura := lo.SumBy(objs, func(item dao.PipelineRunTask) (taken time.Duration) {
	// 	return item.DurationModel.ElapsedTime()
	// })

	// // 最近3次拉取镜像任务的总耗时小于 24 分钟，不往超级节点调度
	// if allDura < 24*time.Minute {
	// 	needSuperNode = false
	// 	return
	// }

	return
}

func shouldContainSubTask(stage dao.PipelineRunStage, task dao.PipelineRunTask) bool {
	if task.GetType().IsDeployTask() {
		if task.Type == constants.TASK_AUTOMATION_DEPLOY.String() && stage.Type != constants.STAGE_DEPLOY_PROD_ENV.String() {
			return false
		} else {
			return true
		}
	}
	if task.GetType().IsImageSyncTask() {
		return true
	}
	return false
}

func createSyncSubtasks(task dao.PipelineRunTask) ([]dao.PipelineRunSubtask, error) {
	subtask := dao.PipelineRunSubtask{
		Config:            task.Config,
		PipelineRunTaskId: task.ID,
		Name:              task.Name,
		Type:              task.Type,
		Status:            task.Status,
		PipelineRunID:     task.PipelineRunId,
		Enabled:           true,
	}
	return []dao.PipelineRunSubtask{subtask}, nil
}

// createDeployBaseSubtasks 将部署基准的主任务，创建对应的子任务数据
func createDeployBaseSubtasks(task dao.PipelineRunTask) ([]dao.PipelineRunSubtask, error) {
	var (
		subtasks   []dao.PipelineRunSubtask
		deployBase model.AutomationDeploy
		configByte []byte
		err        error
	)
	if err = json.Unmarshal(task.Config, &deployBase); err != nil {
		return nil, errors.Wrapf(err, "unmarshal task %d automation deploy config failed", task.ID)
	}
	//旧流水线部署生产环境也要生成记录到子任务表里
	if len(deployBase.MultiEnv) == 0 {
		deployBase.MultiEnv = append(deployBase.MultiEnv, model.MultiEnv{
			Cluster:   deployBase.Cluster,
			Namespace: deployBase.Namespace,
			ConfigId:  deployBase.ConfigId,
			Senv:      deployBase.Senv,
		})
	}

	for _, env := range deployBase.MultiEnv {
		multiCloudConfig := model.TaskRunMultiCloudEnvConfig{
			TriggerMode:       deployBase.TriggerMode,
			DeployEnv:         deployBase.DeployEnv,
			EnvTarget:         deployBase.EnvTarget,
			IsCreateSubEnv:    deployBase.IsCreateSubEnv,
			TrafficMark:       deployBase.TrafficMark,
			ConfigMap:         deployBase.ConfigMap,
			Secret:            deployBase.Secret,
			ServiceAccount:    deployBase.ServiceAccount,
			Cluster:           env.Cluster,
			Namespace:         env.Namespace,
			ConfigId:          env.ConfigId,
			ApprovalTaskRunId: deployBase.ApprovalTaskRunId,
			Senv:              env.Senv,
			IsOrigin:          env.IsOrigin,
		}
		if task.Type == constants.TASK_DEPLOY_SUB.String() {
			if env.IsOrigin {
				multiCloudConfig.EnvTarget = constants.SUB_V2.String()
			} else {
				multiCloudConfig.EnvTarget = constants.SUB.String()
			}
		}
		if configByte, err = json.Marshal(multiCloudConfig); err != nil {
			return nil, errors.Wrapf(err, "marshal task %d automation deploy config failed", task.ID)
		}
		t := dao.PipelineRunSubtask{
			Config:            configByte,
			PipelineRunTaskId: task.ID,
			Name:              task.Name,
			Type:              task.Type,
			Status:            task.Status,
			PipelineRunID:     task.PipelineRunId,
			Enabled:           true,
		}
		subtasks = append(subtasks, t)
	}
	return subtasks, nil
}

func createDeployStagingSubtasks(task dao.PipelineRunTask) ([]dao.PipelineRunSubtask, error) {
	var (
		subtasks      []dao.PipelineRunSubtask
		deployStaging model.DeployStaging
		configByte    []byte
		err           error
	)
	if err = json.Unmarshal(task.Config, &deployStaging); err != nil {
		return nil, errors.Wrapf(err, "unmarshal task %d deploy staging config failed", task.ID)
	}
	for _, env := range deployStaging.MultiEnv {
		config := model.DeployStagingConfig{
			Cluster:   env.Cluster,
			Namespace: env.Namespace,
			ConfigId:  env.ConfigId,
			Senv:      env.Senv,
			SubEnvTag: env.SubEnvTag,
			EnvTarget: deployStaging.EnvTarget,
		}
		if configByte, err = json.Marshal(config); err != nil {
			return nil, errors.Wrapf(err, "marshal task %d deploy staging config failed", task.ID)
		}
		subtasks = append(subtasks, dao.PipelineRunSubtask{
			Config:            configByte,
			PipelineRunTaskId: task.ID,
			Name:              task.Name,
			Type:              task.Type,
			Status:            task.Status,
			PipelineRunID:     task.PipelineRunId,
			Enabled:           true,
		})
	}
	return subtasks, nil
}

func createDeployCanarySubtasks(task dao.PipelineRunTask) ([]dao.PipelineRunSubtask, error) {
	var (
		subtasks     []dao.PipelineRunSubtask
		deployCanary model.DeployCanary
		configByte   []byte
		err          error
	)
	if err = json.Unmarshal(task.Config, &deployCanary); err != nil {
		return nil, errors.Wrapf(err, "unmarshal task %d deploy canary config failed", task.ID)
	}
	for _, env := range deployCanary.MultiEnv {
		config := model.DeployCanaryConfig{
			Cluster:   env.Cluster,
			Namespace: env.Namespace,
			ConfigId:  env.ConfigId,
			Senv:      env.Senv,
			SubEnvTag: env.SubEnvTag,
			EnvTarget: deployCanary.EnvTarget,
		}
		if configByte, err = json.Marshal(config); err != nil {
			return nil, errors.Wrapf(err, "marshal task %d deploy canary config failed", task.ID)
		}
		subtasks = append(subtasks, dao.PipelineRunSubtask{
			Config:            configByte,
			PipelineRunTaskId: task.ID,
			Name:              task.Name,
			Type:              task.Type,
			Status:            task.Status,
			PipelineRunID:     task.PipelineRunId,
			Enabled:           true,
		})
	}
	return subtasks, nil
}

func (p *PipelineSvc) generateSubTask(ctx context.Context, pipelineRun *dao.PipelineRun) ([]dao.PipelineRunSubtask, error) {
	var (
		subtasks  []dao.PipelineRunSubtask
		maintasks []dao.PipelineRunTask
	)
	// filter deploy tasks which should create subtask
	for _, stage := range pipelineRun.Stages {
		if !stage.GetType().IsDeployStage() && !stage.GetType().IsImageSyncStage() {
			continue
		}
		for _, task := range stage.Tasks {
			if shouldContainSubTask(stage, task) {
				maintasks = append(maintasks, task)
			}
		}
	}
	for _, task := range maintasks {
		var (
			tasks []dao.PipelineRunSubtask
			err   error
		)
		// generate subtask config in byte
		switch task.GetType() {
		case constants.TASK_DEPLOY_STAGING:
			tasks, err = createDeployStagingSubtasks(task)
		case constants.TASK_DEPLOY_CANARY:
			tasks, err = createDeployCanarySubtasks(task)
		case constants.TASK_AUTOMATION_DEPLOY, constants.TASK_DEPLOY_ORIGIN:
			tasks, err = createDeployBaseSubtasks(task)
		case constants.TASK_AUTOMATION_DEPLOY_SENV, constants.TASK_DEPLOY_SUB:
			tasks, err = createDeployBaseSubtasks(task)
		case constants.TASK_TEST_ENV_IMAGE_SYNC, constants.TASK_DEV_ENV_IMAGE_SYNC:
			tasks, err = createSyncSubtasks(task)
		case constants.TASK_OFFLINE_CANARY:
			log.InfoWithCtx(ctx, "%s will not create db record", task.Type)
		default:
			return nil, errors.Errorf("unknown task type %s", task.Type)
		}

		if err != nil {
			return nil, err
		}

		subtasks = append(subtasks, tasks...)
	}
	return subtasks, nil

}

func (p *PipelineSvc) GetPipelineNewInfo(ctx context.Context, lastRunId int64) (*model.PipelineListItem, error) {
	pipeline, err := p.pipelineRepo.GetPipelineByLastRunId(ctx, lastRunId)
	if err != nil {
		log.DebugWithCtx(ctx, "查询流水线信息发生错误： %v", err)
		return nil, err
	}
	if pipeline == nil {
		return nil, nil
	}

	appIds := []int64{pipeline.AppID}
	apps, getAppErr := p.GetAppsByIds(ctx, appIds, pipeline.ProjectID)
	if getAppErr != nil {
		return nil, getAppErr
	}
	appMapping := make(map[int64]string)
	for _, app := range apps {
		appMapping[app.ID] = app.Name
	}

	pipelineItem := p.buildPipeInfo(ctx, pipeline, appMapping)
	return &pipelineItem, nil
}

func (p *PipelineSvc) UpdatePipelineConfig(ctx context.Context, updateReq *model.UpdatePipelineConfigParams) error {
	pipe, err := p.pipelineRepo.GetPipelineByPipelineRunId(ctx, updateReq.PipelineRunId)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据流水线运行记录Id[%d]获取流水线数据发生异常: %v", updateReq.PipelineRunId, err)
		return err
	}
	if pipe == nil {
		msg := fmt.Sprintf("根据流水线运行记录Id[%d]获取流水线数据为空", updateReq.PipelineRunId)
		log.ErrorWithCtx(ctx, msg)
		return errors.New(msg)
	}
	pipelineCurrentConfig, mergeErr := p.generateMergedPipelineConfig(ctx, pipe, updateReq)
	if mergeErr != nil {
		return mergeErr
	}
	mergedConfig, _ := json.Marshal(pipelineCurrentConfig)
	updateErr := db.Transaction(func(tx *gorm.DB) error {
		if updateTaskRunErr := p.pipelineRunRepo.UpdateTaskRunConfig(ctx, updateReq.TaskRunId, updateReq.UpdatedTaskRunConfig); updateTaskRunErr != nil {
			log.ErrorWithCtx(ctx, "更新流水线运行记录任务配置失败Id %d: %v", updateReq.TaskRunId, updateTaskRunErr)
			return updateTaskRunErr
		}

		if updatePipelineConfigErr := p.pipelineRepo.UpdatePipelineStateConfig(ctx, pipe.ID, mergedConfig); updatePipelineConfigErr != nil {
			log.ErrorWithCtx(ctx, "更新流水线任务配置失败流水线Id, 任务Id %d,%d: %v", pipe.ID, updateReq.TaskId, updatePipelineConfigErr)
			return updatePipelineConfigErr
		}
		return nil
	})
	if updateErr != nil {
		return updateErr
	}
	return nil
}

func (p *PipelineSvc) generateMergedPipelineConfig(ctx context.Context, pipeline *dao.Pipeline, updateReq *model.UpdatePipelineConfigParams) (model.PipelineConfig, error) {
	var updateTaskConfig model.Config
	var pipelineCurrentConfig model.PipelineConfig
	json.Unmarshal(updateReq.UpdatedPipelineConfig, &updateTaskConfig)
	json.Unmarshal(pipeline.Config, &pipelineCurrentConfig)
	task, findTaskErr := p.pipelineRepo.GetTaskById(ctx, updateReq.TaskId)
	if findTaskErr != nil {
		log.ErrorWithCtx(ctx, "根据taskId[%d]获取任务数据发生异常: %v", updateReq.TaskId, findTaskErr)
		return nil, findTaskErr
	}
	if task == nil {
		msg := fmt.Sprintf("根据taskId[%d]获取任务数据为空", updateReq.TaskId)
		log.ErrorWithCtx(ctx, msg)
		return nil, errors.New(msg)
	}
	pipelineTaskConfig := make(model.Config)
	var taskConfigExisted bool
	_, taskConfigExisted = pipelineCurrentConfig[updateReq.TaskId]

	var originalConfigJson []byte
	var mergeErr error
	if taskConfigExisted {
		pipelineTaskConfig = pipelineCurrentConfig[updateReq.TaskId]
		pipelineTaskConfigJson, _ := json.Marshal(pipelineTaskConfig)
		originalConfigJson, mergeErr = MergeConfig(ctx, task.Config, pipelineTaskConfigJson)
		if mergeErr != nil {
			log.ErrorWithCtx(ctx, "更新流水线配置合并配置失败 %d %d %v", pipeline.ID, updateReq.TaskId, mergeErr)
			return nil, mergeErr
		}
	} else {
		originalConfigJson = task.Config
	}
	var originalTaskConfig model.Config
	json.Unmarshal(originalConfigJson, &originalTaskConfig)

	diffTaskConfig, _ := DiffTaskConfig(ctx, originalTaskConfig, updateTaskConfig)
	for key, value := range diffTaskConfig {
		pipelineTaskConfig[key] = value
	}
	pipelineCurrentConfig[updateReq.TaskId] = pipelineTaskConfig
	return pipelineCurrentConfig, nil
}

func (p *PipelineSvc) updatePipelineRun(ctx context.Context, id int64, updates map[string]interface{}) error {
	return p.pipelineRunRepo.UpdatePipelineRunFields(ctx, id, updates)
}

func (p *PipelineSvc) UpdatePipelineBasic(ctx context.Context, pl *model.Pipeline) error {
	daoPipeline, err := p.pipelineRepo.GetPipeline(ctx, pl.ID)
	if err != nil {
		log.ErrorWithCtx(ctx, "更新流水线[%d]，查询当前流水线信息，发生异常: %v", pl.ID, err)
		return pipelineErr.ErrUpdatePipelineBasicFailed
	}
	if daoPipeline == nil {
		return pipelineErr.ErrPipelineNotFound
	}
	if daoPipeline.TemplateID != pl.TemplateId {
		err = p.pipelineRepo.UpdatePipelineStateConfig(ctx, pl.ID, datatypes.JSON(`{}`))
		if err != nil {
			// 清空旧配置失败好像也不影响后续的使用？
			log.ErrorWithCtx(ctx, "更新流水线[%d]，切换模板操作，清空旧配置，发生异常: %v", pl.ID, err)
			return pipelineErr.ErrUpdatePipelineBasicFailed
		}
	}

	updatePL := convertToDaoPipeline(pl)
	err = p.pipelineRepo.UpdatePipeline(ctx, updatePL)
	if err != nil {
		log.ErrorWithCtx(ctx, "更新流水线[%d]，更新数据操作，发生异常: %v", pl.ID, err)
		return pipelineErr.ErrUpdatePipelineBasicFailed
	}
	return nil
}

func (p *PipelineSvc) IsPipelineBoundChangeSet(ctx context.Context, pipeline *model.Pipeline) (bool, error) {
	chSet, err := p.changeSetRepo.FindChangeSetPipelineRelated(ctx, model.ChangeSetPipelineObj{PipelineID: pipeline.ID})
	if err != nil {
		log.ErrorWithCtx(ctx, "根据pipelineId[%d]查找更变集失败: %v", pipeline.ID, err)
		return false, err
	}
	if chSet != nil {
		return true, nil
	}
	return false, nil
}

func convertToDaoPipeline(src *model.Pipeline) *dao.Pipeline {
	var dst dao.Pipeline
	_ = mapstructure.Decode(src, &dst)

	if src.Config != nil {
		dst.Config, _ = json.Marshal(src.Config)
	} else {
		dst.Config = datatypes.JSON("{}")
	}
	return &dst
}

// createTektonPR 根据数据库中的 PipelineRun 创建 Tekton PipelineRun
// pr 必须有：pr.Pipeline pr.Pipeline.Template
func (p *PipelineSvc) createTektonPR(ctx context.Context, pr *dao.PipelineRun, appInfo *pbapp.GetAppInfoResp) (*v1beta1.PipelineRun, error) {
	requestDataParam := tekton.WithRequestDate(timex.ToYearMonth(pr.CreatedAt))
	idParam := tekton.WithProjectIdentity(appInfo.GetProject().GetIdentity())
	appParam := tekton.WithAppInfo(appInfo.GetApp().GetName(), appInfo.GetApp().GetCmdbId())

	tektonPipeline, err := p.tektonClient.GetPipeline(ctx, pr.Pipeline.Template.TektonNamespace, pr.Pipeline.Template.TektonName)
	if err != nil {
		log.ErrorWithCtx(ctx, "get tekton Pipeline from template failed: %v", err)
		return nil, err
	}

	tpr, err := p.tektonClient.CreatePipelineRun(ctx, pr, tektonPipeline, requestDataParam, idParam, appParam)
	if err != nil {
		return nil, err
	}
	pr.TektonNamespace = tpr.Namespace
	pr.TektonName = tpr.Name
	pr.Status = constants.PENDING
	updateFields := map[string]interface{}{
		"tekton_namespace": tpr.Namespace,
		"tekton_name":      tpr.Name,
		"status":           constants.PENDING,
	}
	if err = p.pipelineRunRepo.UpdatePipelineRunFields(ctx, pr.ID, updateFields); err != nil {
		log.ErrorWithCtx(ctx, "update pipeline run tekton and status failed: %v", err)
		return nil, err
	}

	return tpr, nil
}

// calculateProjectPipelineRunQuota return {projectID: quota}
func (p *PipelineSvc) calculateProjectPipelineRunQuota(ctx context.Context, projectIDList []int64) (map[int64]int64, error) {
	// 业务规则：5分钟内，同一个项目中最多 10 条流水线
	lastPeriod := time.Duration(-5 * time.Minute)
	lastPeriodPRs, err := p.pipelineRunRepo.FindTknPipelineRunByTime(ctx, lastPeriod, projectIDList)
	if err != nil {
		log.ErrorWithCtx(ctx, "dao find running pipeline runs failed: %v", err)
		return nil, err
	}

	maxQuota := conf.AppConfig.Tekton.ProjectQuota
	pQuota := make(map[int64]int64)
	for _, p := range projectIDList {
		if p == int64(31) || p == int64(2) {
			// CICD 压测和验证 的配额是 10000 不限流
			pQuota[p] = int64(10000)
		} else {
			pQuota[p] = maxQuota
		}
	}
	for _, pr := range lastPeriodPRs {
		q := pQuota[pr.Pipeline.ProjectID]
		pQuota[pr.Pipeline.ProjectID] = q - 1
		log.InfoWithCtx(ctx, "calculate quota project %d pipeline_run %d", pr.Pipeline.ProjectID, pr.ID)
	}

	log.InfoWithCtx(ctx, "param %+v calculateProjectPipelineRunQuota: %+v", projectIDList, pQuota)

	return pQuota, nil
}

// HandlePrepareingPipelineRun 查询数据库中准备中的流水线，如果项目有配额，则创建 Tekton PipelineRun
func (p *PipelineSvc) HandlePrepareingPipelineRun(ctx context.Context) error {

	// 为了避免并发问题，这里使用 redis 锁来控制并发
	lockKey := "handlePrepareingPipelineRunLock"
	locked, err := godis.LockWithExpirationCtx(ctx, lockKey, "1")
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePrepareingPipelineRun get lock %s error: %s", lockKey, err)
		return err
	}
	if !locked {
		log.WarnWithCtx(ctx, "HandlePrepareingPipelineRun can't acquire lock %s, skip", lockKey)
		return nil
	}
	defer godis.Unlock(ctx, lockKey)

	prepareingPRs, err := p.pipelineRunRepo.FindPipelineRunByStatus(ctx, constants.PREPARING)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePrepareingPipelineRun dao find preparing pipeline_run failed: %v", err)
		return err
	}
	projectPrepareingPRs := make(map[int64][]dao.PipelineRun) // {projectID: []dao.PipelineRun}
	for _, pr := range prepareingPRs {
		pID := pr.Pipeline.ProjectID
		_, ok := projectPrepareingPRs[pID]
		if !ok {
			projectPrepareingPRs[pID] = make([]dao.PipelineRun, 0)
		}
		projectPrepareingPRs[pID] = append(projectPrepareingPRs[pID], pr)
	}

	// projectPrepareingPRs 是待创建的资源列表，需要按照 ID 从小到大顺序去创建资源。
	for pID, prs := range projectPrepareingPRs {
		sort.Slice(prs, func(i, j int) bool {
			if prs[i].TektonAt == backdoor { // 留个后门，有些SVIP 给的实在太多了，不限流
				return true
			}
			return prs[i].ID < prs[j].ID
		})

		projectPrepareingPRs[pID] = prs

	}

	log.InfoWithCtx(ctx, "HandlePrepareingPipelineRun projectPrepareingPRs: %+v", projectPrepareingPRs)

	// 这里以 project 为单位控制锁的粒度
	for projectID, prs := range projectPrepareingPRs {
		for i, pr := range prs {
			appInfo, err := p.appClient.GetAppInfo(ctx, &pbapp.GetAppInfoReq{AppId: pr.Pipeline.AppID})
			if err != nil {
				log.ErrorWithCtx(ctx, "HandlePrepareingPipelineRun pipeline_run %d get app info failed: %v, skip", pr.ID, err)
				continue
			}
			// 先抢一下项目的配额锁，再进入 quota 的计算和创建流水线的逻辑
			quotaLockKey := fmt.Sprintf("pipelineQuotaProject%dLock", projectID)
			quotaLock, err := godis.LockWithExpirationCtx(ctx, quotaLockKey, "1")
			if err != nil {
				log.ErrorWithCtx(ctx, "HandlePrepareingPipelineRun pipeline_run %d project %d get %s error: %s, skip", pr.ID, projectID, quotaLockKey, err)
				continue
			}
			if !quotaLock {
				log.WarnWithCtx(ctx, "HandlePrepareingPipelineRun pipeline_run %d project %d can't acquire lock %s, skip", pr.ID, projectID, quotaLock)
				continue
			}
			log.InfoWithCtx(ctx, "HandlePrepareingPipelineRun pipeline_run %d project %d get lock: %s", pr.ID, projectID, quotaLockKey)

			// 以最小粒度开启小事务，一个是并发粒度考虑，一个是事务不要太大。
			// 这里需要处理全部的准备中流水线，只能使用 return error 来控制流程。非预期的错误和业务预期的错误都需要返回 error，以跳出循环。
			err = db.Transaction(func(tx *gorm.DB) error {
				ctx = db.CtxWithTX(ctx, tx)
				log.InfoWithCtx(ctx, "HandlePrepareingPipelineRun pipeline_run %d project %d start transaction", pr.ID, projectID)

				// TODO: lock for calculate?
				pQuota, err := p.calculateProjectPipelineRunQuota(ctx, []int64{projectID})
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePrepareingPipelineRun pipeline_run %d project %d calculate quota failed, skip", pr.ID, projectID, err)
					return errors.Wrapf(err, "project %d calculate quota failed", projectID)
				}
				q := pQuota[projectID]

				if q <= 0 {
					log.InfoWithCtx(ctx, "HandlePrepareingPipelineRun project %d has no quota, skip create pipeline_run: %d", projectID, pr.ID)
					if pr.TektonAt != backdoor { // 留个后门，有些SVIP 给的实在太多了，不限流
						return errors.Wrapf(pipelineErr.ErrTektonResourceQuotaLimit, "project %d quota %d, skip", projectID, q)
					}
				}

				log.InfoWithCtx(ctx, "HandlePrepareingPipelineRun project %d has %d quota, create pipeline_run: %d Tekton PipelineRun", projectID, q, pr.ID)
				tpr, err := p.createTektonPR(ctx, &pr, appInfo)
				if err != nil {
					log.ErrorWithCtx(ctx, "HandlePrepareingPipelineRun pipeline_run %d create tekton pipeline run failed: %v", pr.ID, err)
					return errors.Wrapf(err, "pipeline_run %d create tekton resource failed", pr.ID)
				}
				log.InfoWithCtx(ctx, "HandlePrepareingPipelineRun pipeline_run %d create tekton PipelineRun %s success", pr.ID, tpr.Name)

				if err = p.pipelineRunRepo.UpdatePipelineRunFields(ctx, pr.ID,
					map[string]interface{}{
						"Status":   constants.PENDING,
						"TektonAt": time.Now()}); err != nil {
					log.ErrorWithCtx(ctx, "HandlePrepareingPipelineRun pipeline_run %d update status to pending failed: %s", pr.ID, err)
					return errors.Wrapf(err, "update pipeline_run %d status failed", pr.ID)
				}

				log.InfoWithCtx(ctx, "HandlePrepareingPipelineRun pipeline_run %d create resource success", pr.ID)
				return nil
			})

			godis.Unlock(ctx, quotaLockKey)
			log.InfoWithCtx(ctx, "HandlePrepareingPipelineRun %d project %d release lock: %s", pr.ID, projectID, quotaLockKey)

			if err != nil {
				if errors.Is(err, pipelineErr.ErrTektonResourceQuotaLimit) {
					log.InfoWithCtx(ctx, "HandlePrepareingPipelineRun project %d has no quota, skip pipeline_runs: %+v", projectID, prs[i:])
					break
				} else {
					log.InfoWithCtx(ctx, "HandlePrepareingPipelineRun pipeline_run %d project %d create resource failed, continue", pr.ID, projectID)
					continue
				}
			}

		}
	}
	return nil
}

func (p *PipelineSvc) checkOneRunning(ctx context.Context, pipeline *dao.Pipeline) (canRun bool, err error) {
	canRun = true
	if pipeline.PipelineGroupID == 0 {
		return
	}
	pg, err := p.pipelineGroupRepo.FindPipelineGroup(ctx, pipeline.PipelineGroupID)
	if err != nil {
		return
	}
	if pg == nil {
		return
	}
	if !pg.IsOneRunning {
		return
	}
	ppRun, err := p.pipelineRunRepo.LastRunByPipelineId(ctx, pipeline.ID)
	if err != nil {
		return
	}
	if ppRun == nil {
		return
	}

	if ppRun.Status == constants.CANCEL || ppRun.Status == constants.SUCCESSFUL {
		return
	}

	canRun = false
	return
}

var backdoor = time.Date(1988, 12, 22, 19, 30, 0, 0, time.Local)

const (
	PipelineCacheBuildDir = "/cicd/cache/build/"
	PipelineCachePkgDir   = "/cicd/cache/pkg/"
)

func (p *PipelineSvc) ClearCache(ctx context.Context, pipeline *model.PipelineParam) error {
	pplIdStr := fmt.Sprintf("%d", pipeline.ID)
	if p.delCache.Get(pplIdStr) {
		return pipelineErr.ErrPipelineCacheRunning
	}

	p.delCache.Add(pplIdStr)
	defer p.delCache.Del(pplIdStr)

	pipelineObj, err := p.pipelineRepo.GetPipeline(ctx, pipeline.ID)
	if err != nil {
		return err
	}
	if pipelineObj == nil || pipelineObj.AppName == "" {
		return pipelineErr.ErrPipelineNotFound
	}

	harbor := harbor.NewRepository(conf.AppConfig.HarborCR.Host, conf.AppConfig.HarborCR.AuthName, conf.AppConfig.HarborCR.AuthPsw)

	go func() {
		errDel := harbor.Delete("devops", fmt.Sprintf("%d/%s/cache", pipelineObj.ProjectID, pipelineObj.AppName))
		if errDel != nil {
			log.ErrorWithCtx(ctx, "删除 Harbor 缓存失败: %v", errDel)
		} else {
			log.InfoWithCtx(ctx, "删除 Harbor 缓存成功: %s/%s/cache", pipelineObj.ProjectID, pipelineObj.AppName)
		}
	}()

	err = os.RemoveAll(PipelineCacheBuildDir + pipelineObj.AppName)
	if err != nil {
		return err
	}
	err = os.RemoveAll(PipelineCachePkgDir + pipelineObj.AppName)
	if err != nil {
		return err
	}

	return nil
}

func (ps *PipelineSvc) DelPipelines(ctx context.Context, pipeline model.PipelineDelParam) (err error) {
	if pipeline.AppId == nil || pipeline.PrjId == nil || *pipeline.AppId == 0 {
		return
	}

	var total int64

	pipelines, err := ps.pipelineRepo.PagePipeline(ctx, &model.PipelineQuery{
		PageModel: page.NewUnlimitedSizePageModel(),
		AppId:     *pipeline.AppId,
		ProjectId: *pipeline.PrjId,
	}, &total)

	if err != nil {
		log.ErrorWithCtx(ctx, "查询流水线失败: %v", err)
		return
	}

	for _, p := range pipelines {
		err = ps.DeletePipeline(ctx, &model.PipelineParam{
			ID: p.ID,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "删除流水线[%d]失败: %v", p.ID, err)
			return
		}
	}

	return
}
