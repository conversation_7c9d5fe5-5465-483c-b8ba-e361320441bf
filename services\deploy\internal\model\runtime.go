package model

import "52tt.com/cicd/pkg/constants"

type OperatorUserInfo struct {
	OperatorBy            int64  `json:"operatorBy"`
	OperatorByChineseName string `json:"operatorByChineseName"`
	OperatorByEmployeeNo  string `json:"operatorByEmployeeNo"`
}

type RollbackReq struct {
	OperatorUserInfo
	ID          int64  `json:"releaseID" validate:"required"`
	Description string `json:"description" validate:"max=200"`
}

type RetryReq struct {
	OperatorUserInfo
	ID int64 `json:"releaseId" validate:"required"`
}

type DeployReq struct {
	OperatorUserInfo
	ID          int64  `json:"releaseId" validate:"required"`
	ConfigID    int64  `json:"configId" validate:"required"`
	Description string `json:"description"`
	Timeout     int64  `json:"timeout"`
}

type RollbackListReq struct {
	AppId     int64  `json:"appId" form:"appId" validate:"required"`
	Env       string `json:"env" form:"env" validate:"required"`
	EnvTarget string `json:"envTarget" form:"envTarget" validate:"required"`
	Cluster   string `json:"cluster" form:"cluster" validate:"required"`
	Namespace string `json:"namespace" form:"namespace" validate:"required"`
	Senv      string `json:"senv" form:"senv"`
	Actions   []int  `json:"action" form:"action"`
}

type RollbackListResp struct {
	ID                    int64  `json:"id"`
	OperatorByChineseName string `json:"operatorByChineseName"`
	Branch                string `json:"branch"`
	Description           string `json:"description"`
	OperatorByEmployeeNo  string `json:"operatorByEmployeeNo"`
}

type OfflineReq struct {
	*OperatorUserInfo
	ReleaseID int64 `json:"releaseID" validate:"required"`
}

type RestartReq struct {
	*OperatorUserInfo
	ReleaseID int64 `json:"releaseID" validate:"required"`
}

type CheckReq struct {
	AppId     int64                   `json:"appId" form:"appId"`
	Cluster   string                  `json:"cluster" form:"cluster"`
	Namespace string                  `json:"namespace" form:"namespace"`
	EnvTarget constants.EnvTargetType `json:"envTarget" form:"envTarget"`
	Senv      string                  `json:"senv" form:"senv"`
}

type EventlinkCheckReq struct {
	AppId int64             `json:"appId" form:"appId"`
	Env   constants.EnvType `json:"env" form:"env"`
}

type ReplicasDetailReq struct {
	ReleaseID int64                   `form:"releaseId"`
	AppName   string                  `json:"appName" form:"appName"`
	Cluster   string                  `json:"cluster" form:"cluster"`
	Namespace string                  `json:"namespace" form:"namespace"`
	EnvTarget constants.EnvTargetType `json:"envTarget" form:"envTarget"`
	Senv      string                  `json:"senv" form:"senv"`
	ConfigId  int64                   `json:"configId" form:"configId"`
}

type DetailResp struct {
	Url string `json:"url"`
}

type ImageDeployReq struct {
	ImageUrl    string                  `json:"imageUrl" validate:"required"`
	AppID       int64                   `json:"appId" validate:"required"`
	AppName     string                  `json:"appName" validate:"required"`
	Cluster     string                  `json:"cluster" validate:"required"`
	Namespace   string                  `json:"namespace" validate:"required"`
	Env         constants.EnvType       `json:"env" validate:"required"`
	EnvTarget   constants.EnvTargetType `json:"envTarget" validate:"required"`
	Senv        string                  `json:"senv" validate:"ne=canary"`
	ConfigID    int64                   `json:"configId" validate:"required"`
	Description string                  `json:"description" validate:"max=200"`
	TimeOut     int64                   `json:"timeOut" `
}

type CurrSvcReq struct {
	AppId     int64               `json:"appId" form:"appId"`
	Envs      []constants.EnvType `json:"envs" validate:"required"`
	Cluster   string              `json:"cluster" form:"cluster"`
	Namespace string              `json:"namespace" form:"namespace"`
}

type DeployItem struct {
	Cluster   string                  `json:"cluster" validate:"required"`
	Namespace string                  `json:"namespace" validate:"required"`
	Env       constants.EnvType       `json:"env" validate:"required"`
	EnvTarget constants.EnvTargetType `json:"envTarget" validate:"required"`
	Senv      string                  `json:"senv" validate:"ne=canary"`
}

type SampleDeployReq struct {
	ProjectID int64        `json:"projectId" validate:"required"`
	AppID     int64        `json:"appId" validate:"required"`
	AppName   string       `json:"appName" validate:"required"`
	Envs      []DeployItem `json:"envs" validate:"required"`
}
