package rest

import (
	"context"
	"encoding/json"
	"reflect"

	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/mcp/internal/service/find"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mitchellh/mapstructure"
)

func (ctl *ttCloudMCPController) ToolsFindClusterOrNamespaces() mcp.Tool {
	argsType := reflect.TypeOf(find.FindClusterOrNSArgs{})
	argsSchema := jsonSchemaReflector.ReflectFromType(argsType)
	raw, _ := argsSchema.MarshalJSON()
	return mcp.NewToolWithRawSchema("find_k8s_clusterOrNamespaces", DescpOfFindClusterOrNamespaces, raw)
}

func (ctl *ttCloudMCPController) ToolsFindMultiResource() mcp.Tool {
	argsType := reflect.TypeOf(find.FindK8SResourceArgs{})
	argsSchema := jsonSchemaReflector.ReflectFromType(argsType)
	raw, _ := argsSchema.MarshalJSON()
	return mcp.NewToolWithRawSchema("find_K8S_resources", DescpOfFindK8SResource, raw)
}

func (ctl *ttCloudMCPController) FindClusterOrNamespaces(ctx context.Context, request mcp.CallToolRequest) (res *mcp.CallToolResult, errOut error) {
	var req find.FindClusterOrNSArgs
	err := mapstructure.Decode(request.Params.Arguments, &req)
	if err != nil {
		res = mcp.NewToolResultError("find.FindClusterOrNamespaces Decode Err")
		return
	}
	rst, err := find.FindAgg.FindClusterOrNamespaces(req)
	if err != nil {
		log.Errorf("ttCloudMCPController FindClusterOrNamespaces err: %v", err)
		res = mcp.NewToolResultError(err.Error())
		return
	}
	raw, err := json.Marshal(rst)
	if err != nil {
		res = mcp.NewToolResultError(err.Error())
		return
	}
	res = mcp.NewToolResultText(string(raw))

	return
}

func (ctl *ttCloudMCPController) FindK8SResources(ctx context.Context, request mcp.CallToolRequest) (res *mcp.CallToolResult, errOut error) {
	var req find.FindK8SResourceArgs
	err := mapstructure.Decode(request.Params.Arguments, &req)
	if err != nil {
		res = mcp.NewToolResultError("find.FindK8SResourceArgs Decode Err")
		return
	}

	if req.Search == "*" || req.Search == "" {
		res = mcp.NewToolResultError("search can't be empty or '*'")
		return
	}

	rst, err := find.FindAgg.FindK8SResource(req)
	if err != nil {
		log.Errorf("ttCloudMCPController FindK8SResources err: %v", err)
		res = mcp.NewToolResultError(err.Error())
		return
	}

	raw, err := json.Marshal(rst)
	if err != nil {
		res = mcp.NewToolResultError(err.Error())
		return
	}

	res = mcp.NewToolResultText(string(raw))

	return
}

const (
	DescpOfFindK8SResource = `
功能：
    根据 {要查询资源的名称,必须(模糊匹配,只能查询出资源名称包含该关键字的资源,不支持正则匹配),此处的名称是指资源的名称,而不是资源的类型}、
{集群名称,非必填}{命名空间(NS)名称,非必填}跨集群与地域查询k8s和istio资源的基本信息和当前状态信息(如状态、ip、副本数、namespace、cluster等)。
可以查询的k8s资源有ConfigMap、Secret、Service、Pod、Deployment、StatefulSet、DaemonSet、Job,Namespace;
可以查询的istio资源有sidecar,gateway,virtualservice,telemetry等;
应用(或服务)的名称即为对应deployment和service的名称;

输出：
    返回查询到的资源列表,包含 total:查询到的资源总数;list:资源列表{name:资源名称;
    resource:资源类型,Pod\Deployment\Job\ConfigMap\virtualservice等;
    clusterName:集群名称;namespace:命名空间(NS)名称；
    descriptions:资源当前的状态信息，不同资源类型 当前状态包含的信息不同{
        比如Pod的状态包含:IP、所在节点、重启次数、CPU/内存 使用率、业务等级等;
        Deployment的状态包含:当前副本数/期望副本数、可用副本数、镜像等;
    }
 }`

	DescpOfFindClusterOrNamespaces = `
 功能：
	该工具有两种能力：分别是1.根据{环境}查询所有K8S集群的基本信息；2.根据{集群名称或集群ID}{业务属性}查询该集群的namespaces信息。
    {环境}枚举只能取值[production,preview,testing,dev]之一，分表代表production：生产环境，preview：灰度环境，testing:测试环境，dev：开发环境
    查询namespaces时，{集群名称或集群ID}必填其中一个；
    {业务属性}可选填，留空的话查询该集群所有NS。
输入参数
    环境（非必需）：集群所属的数据环境
    集群名称（非必需）、集群ID（非必需），查询Namespace时必填其中一个
    业务属性（非必需）：NS具体归属的业务部门信息，即NS资源label属性里mesh.quwan.io/business赋予的值。
输出：
	查询集群时返回信息包含 total:查询到的总数;list:集群或命名空间列表{集群名、集群ID、集群描述、集群所属云厂商、云商ID、集群所属地域、集群所属环境、
				集群ApiServer、集群是否就绪(1:可用0:不可用)}
				
	查询命名空间时返回信息包含 total:查询到的总数; clusterInfo:NS归属的集群信息;list:命名空间列表{NS名称、NS所属集群、NS的唯一标识、NS的所属业务线、NS所属开发团队、NS标签}
	}
 `
)
