package run_ser

import (
	"sync"

	"52tt.com/cicd/protocol/app"
	"52tt.com/cicd/protocol/iam"
	"52tt.com/cicd/protocol/pipeline"
)

// 各种支持 运行 的资源的运行 ，流水线
var RunAgg = &runAgg{}
var one = &sync.Once{}

type runAgg struct {
	psCli   pipeline.PipelineServiceClient
	userCli iam.UserServiceClient
	appCli  app.AppServiceClient
}

func InitAgg(psCli pipeline.PipelineServiceClient, userCli iam.UserServiceClient, appCli app.AppServiceClient) {
	one.Do(func() {
		RunAgg = &runAgg{
			psCli:   psCli,
			userCli: userCli,
			appCli:  appCli,
		}
	})
	return
}

func (r *runAgg) RunPipeline(req RunPipelineReq) (err error) {
	en := pipelineEn{
		psCli:   r.psCli,
		userCli: r.userCli,
		appCli:  r.appCli,
	}
	err = en.Run(req)
	return
}
