[default.app]
name = "cicd-bff"
version = 0.1
http.port = 8090
http.timeout = "10s"
rpc.port = 9000
mode = "debug"
description = "cicd Web 应用 BFF"

[default.logger]
logname = "/data/yunwei/bff.log"
loglevel = "debug"
max_size = 50
max_age = 30
max_backups = 10
compress = false

[default.registry]
iam_rpc_url = "127.0.0.1:9003"

[default.redis]
addr = "**************:6379"
db = 0

[routes]
base_path = "/api/v1"
whitelists = [
    { method = "GET", path = "/iam/sso/login$" },
    { method = "GET", path = "/iam/sso/redirect$" },
    { method = "GET", path = "/iam/sso/callback-sync-user$" },
    { method = "GET", path = "/iam/user/set-permanent-token$" },
    { method = "GET", path = "/pipeline/rules$" },
    { method = "GET", path = "/pipeline/commands$" },
    { method = "GET", path = "/pipeline/script-params$" },
    { method = "GET", path = "/pipeline/languages$" },
    { method = "POST", path = "/pipeline/listener/events$" },
    { method = "POST", path = "/pipelines/event/callback$" },
    { method = "POST", path = "/pipeline/runs/tasks/autoDeploy$" },
    { method = "GET", path = "/pipelines/runs/extra/.*" },
    { method = "GET", path = "/pipelines/runs/retry-timeout$" },
    { method = ".*", path = "/approval/.*" },
    { method = ".*", path = "/migrate/.*" },
    { method = ".*", path = "/idehub/user-apps/.*" },
    { method = "POST", path = "/notify/lark-callback/event$" },
    { method = "POST", path = "/pipelines/runs/auto-testing/callback/\\d+$"},
]
access_controls = []
auth_body_apis = [
    { method = "POST",json_path="projectId", path = "/app/apps$" },
    { method = "PUT", json_path="projectId",path = "/app/apps/\\d+$" },
]

[routes.app]
name = "app"
http_url = "http://localhost:8091"
base_path = "/api/v1"
access_controls = [
    { method = "POST", path = "/projects/$", roles = "ADMIN" },
    { method = "GET", path = "/projects/$", roles = "ADMIN,OPENAPI" },
    { method = "PUT", path = "/projects/\\d+$", roles = "ADMIN" },
    { method = "GET", path = "/projects/\\d+$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,QA,OPS" },
    { method = "(POST|PUT)", path = "/projects/\\d+/members$", roles = "ADMIN,DEV_ADMIN" },
    { method = "GET", path = "/projects/\\d+/check-member$", roles = "ADMIN,DEV_ADMIN" },
    { method = "DELETE", path = "/projects/\\d+/members/\\d+$", roles = "ADMIN,DEV_ADMIN" },
    { method = "GET", path = "/apps$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "POST", path = "/apps$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "(GET|PUT)", path = "/apps/\\d+$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "GET", path = "/projects/\\d+/apps/search$", roles = "ADMIN,DEV_ADMIN" },
    { method = "GET", path = "/image-manager/images$", roles = "ADMIN,DEV_ADMIN,DEVELOPER"},
    { method = "PUT", path = "/apps/batch$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "GET", path = "/apps/deploy-plan-apps$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "GET", path = "/projects/openapi/user-projects$", roles="OPENAPI" },
    { method = "(GET|POST)", path = "/config-manager/configs$", roles="ADMIN,DEV_ADMIN,OPS,DEVELOPER" },
    { method = "(GET|PUT|DELETE)", path = "/config-manager/configs/\\d+$", roles="ADMIN,DEV_ADMIN,OPS,DEVELOPER" },
    { method = "GET", path = "/config-manager/configs/\\d+/versions$", roles="ADMIN,DEV_ADMIN,OPS,DEVELOPER" },
    { method = "POST", path = "/config-manager/configs/versions/\\d+/active$", roles="ADMIN,DEV_ADMIN,OPS,DEVELOPER" },
    { method = "POST", path = "/config-manager/configs/versions/\\d+/diff$", roles="ADMIN,DEV_ADMIN,OPS,DEVELOPER" },
    { method = "GET", path = "/config-manager/configs/basic$", roles="ADMIN,DEV_ADMIN,OPS,DEVELOPER" },
    { method = "POST", path = "/config-manager/configs/all$", roles="ADMIN,DEV_ADMIN,OPS,DEVELOPER" },
    { method = "POST", path = "/config-manager/configs/apps/bind$", roles="ADMIN,DEV_ADMIN,OPS,DEVELOPER" },
    { method = "POST", path = "/config-manager/configs/apps/unbind$", roles="ADMIN,DEV_ADMIN,OPS,DEVELOPER" },
    { method = "POST", path = "/config-manager/configs/openapi/revision$", roles="ADMIN,DEV_ADMIN,OPENAPI"},
]

[routes.deploy]
name = "deploy"
http_url = "http://localhost:8092"
base_path = "/api/v1"
access_controls = [
    { method = "GET", path = "/approval-flows/\\d+$", roles = "ADMIN,DEV_ADMIN" },
    { method = "GET", path = "/approval-flows$", roles = "ADMIN,DEV_ADMIN" },
    { method = "POST", path = "/approval-flows$", roles = "ADMIN,DEV_ADMIN" },
    { method = "GET", path = "/approval-flows/projects/all$", roles = "ADMIN,DEV_ADMIN,OPS" },
    { method = "POST", path = "/deploy-configs/\\d+$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS"},
    { method = "POST", path = "/tickets$", roles = "DEV_ADMIN,DEVELOPER"},
    { method = "POST", path = "/tickets/\\d+/deploy-configs$", roles = "DEV_ADMIN,DEVELOPER,OPS" },
    { method = "(GET|POST)", path = "/deploy-configs$", roles = "DEV_ADMIN,DEVELOPER,ADMIN,OPS" },
    { method = "POST", path = "/deploy-configs/config$", roles = "DEV_ADMIN,DEVELOPER,ADMIN,OPS" },
    { method = "POST", path = "/deploy-configs/basic$", roles = "DEV_ADMIN,DEVELOPER,ADMIN,OPS" },
    { method = "(GET|POST)", path = "/deploy-configs/templates/\\d+$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "(GET|POST)", path = "/routes/gateways$", roles = "ADMIN,DEV_ADMIN,OPS" },
    { method = "(GET|PUT|DELETE)", path = "/routes/gateways/\\d+$", roles = "ADMIN,DEV_ADMIN,OPS" },
    { method = "GET", path = "/routes/manage-config$", roles = "ADMIN,DEV_ADMIN,OPS" },
    { method = "POST", path = "/routes/approval-flow/switch$", roles = "ADMIN,DEV_ADMIN,OPS" },
    { method = "GET", path = "/deploy-plans$", roles = "ADMIN,DEV_ADMIN,QA,OPS" },
    { method = "POST", path = "/deploy-plans$", roles = "ADMIN,DEV_ADMIN,OPS" },
    { method = "(DELETE|PUT)", path = "/deploy-plans/\\+d$", roles = "ADMIN,DEV_ADMIN,OPS" },
    { method = "POST", path = "/deploy-plans/\\+d/switch$", roles = "ADMIN,DEV_ADMIN,OPS" },
    { method = "GET", path = "/deploy-plans/\\+d$", roles = "ADMIN,DEV_ADMIN,QA,OPS" },
    { method = "GET", path = "/deploy-plans/\\+d/apps$", roles = "ADMIN,DEV_ADMIN,QA,OPS" },
    { method = "POST", path = "/runtime/image-deploy$", roles = "ADMIN,OPS" },
]

[routes.iam]
name = "iam"
http_url = "http://localhost:8093"
base_path = "/api/v1"
access_controls = [
    { method = "GET", path = "/user/search$", roles = "ADMIN,DEV_ADMIN" },
]

[routes.notify]
name = "notify"
http_url = "http://localhost:8094"
base_path = "/api/v1"
access_controls = []


[routes.migrate]
name = "migrate"
http_url = "http://localhost:8097"
base_path = "/api/v1"
access_controls = []

[routes.pipeline]
name = "pipeline"
http_url = "http://localhost:8095"
base_path = "/api/v1"
access_controls = [
    { method = "GET", path = "/templates$", roles = "ADMIN,DEV_ADMIN" },
    { method = "GET", path = "/templates/languages$", roles = "ADMIN,DEV_ADMIN" },
    { method = "POST", path = "/templates/system$", roles = "ADMIN" },
    { method = "POST", path = "/templates/custom$", roles = "ADMIN,DEV_ADMIN" },
    { method = "(GET|DELETE|PUT)", path = "/templates/\\d+$", roles = "ADMIN,DEV_ADMIN" },
    { method = "GET", path = "/templates/\\d+/basic$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "GET", path = "/templates/\\d+/system$", roles = "ADMIN" },
    { method = "GET", path = "/pipelines/\\d+$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS,QA" },
    { method = "(PUT|DELETE)", path = "/pipelines/\\d+$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "POST", path = "/pipelines$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "POST", path = "/pipelines/\\d+/run$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "PUT", path = "/pipelines/\\d+/config$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "GET", path = "/cloud/clusters$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "GET", path = "/cloud/namespaces$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "GET", path = "/cloud/traffic-mark$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "GET", path = "/cloud/clusters/resources", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "(GET|POST)", path = "/pipeline-groups$", roles = "ADMIN,DEV_ADMIN" },
    { method = "(PUT|DELETE)", path = "/pipeline-groups/\\d+$", roles = "ADMIN,DEV_ADMIN" },
    { method = "GET", path = "/pipelines/runs/tasks/\\d+/deployInfo$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS,QA" },
    { method = "GET", path = "/pipelines/runs/tasks/\\d+/sonar$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS,QA" },
    { method = "POST", path = "/pipelines/runs/tasks/\\d+/retry$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "POST", path = "/pipelines/runs/\\d+/cancel$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "POST", path = "/pipelines/runs/tasks/\\d+/manualDeploy$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "POST", path = "/pipelines/runs/multi-cloud-tasks/\\d+/upgrade$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "POST", path = "/pipelines/runs/multi-cloud-tasks/\\d+/retry$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "POST", path = "/pipelines/runs/multi-cloud-tasks/\\d+/stop-deploy$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "POST", path = "/pipelines/runs/\\d+/tasks/\\d+/stop-task$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "POST", path = "/pipelines/runs/tasks/\\d+/pass-task$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "PUT", path = "/pipelines/runs/tasks/\\d+/subtasks/\\d+/retry-offline$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "POST", path = "/pipelines/runs/tasks/\\d+/percentage$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "GET", path = "/change-sets$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "(POST|PUT)", path = "/change-sets$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
    { method = "GET", path = "/change-sets/\\d+$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "(POST|PUT)", path = "/change-sets/\\d+$", roles = "ADMIN,DEV_ADMIN,DEVELOPER" },
]

[routes.approval]
name = "approval"
http_url = "http://localhost:8080"
base_path = "/api/v1/approval"
access_controls = []

[routes.idehub]
name = "idehub"
http_url = "http://localhost:8100"
base_path = "/api/v1"
access_controls = [
    { method = "(GET|POST)", path = "/user-configs/app$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
    { method = "GET", path = "/user-configs/debug$", roles = "ADMIN,DEV_ADMIN,DEVELOPER,OPS" },
]