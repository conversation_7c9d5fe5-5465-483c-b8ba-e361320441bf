package tekton

import (
	"context"
	"encoding/json"
	"os"
	"strconv"
	"strings"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/services/pipeline/internal/dao"
	"52tt.com/cicd/services/pipeline/internal/model"
	"github.com/samber/lo"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/pod"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func (c *Client) GetTask(ctx context.Context, namespace, name string) (*v1beta1.Task, error) {
	task, err := c.tektonClient.TektonV1beta1().Tasks(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	return task, nil
}

// 生成任务特殊的Pod
func (c *Client) genSpecialTaskPod(taskCfg dao.PipelineRunTask, pr dao.PipelineRun) (tmpt *pod.PodTemplate, annos map[string]string, isSpecialPod bool) {
	tmpt = &pod.PodTemplate{
		NodeSelector: map[string]string{},
	}
	annos = map[string]string{}
	isSpecialPod = c.selectNode(taskCfg, pr, tmpt, annos)

	return
}

// 生成任务资源配置， 会被 task里的 容器平均分配了，不符合预期，只能再Tekton task 模板里指定
func (c *Client) genTaskResource(taskRun *v1beta1.PipelineTaskRunSpec, taskCfg dao.PipelineRunTask) {
	if taskCfg.NeedSuperNode && taskCfg.GetType() == constants.TASK_GENERATE_PUSH_IMAGE {
		// 资源数量会被平均分成3份
		taskRun.ComputeResources = &corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("12"),
				corev1.ResourceMemory: resource.MustParse("30Gi"),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("24"),
				corev1.ResourceMemory: resource.MustParse("240"),
			},
		}
	}
	return
}

// CI Task 选择节点 执行
func (c *Client) selectNode(taskCfg dao.PipelineRunTask, pr dao.PipelineRun, tmpt *pod.PodTemplate, annos map[string]string) (isSpecialPod bool) {
	isSpecialPod = true
	// GUP 限定
	if isUseGpuBuild(taskCfg) {
		tmpt.NodeSelector["cicd"] = "gpu"
		tmpt.Tolerations = append(tmpt.Tolerations, corev1.Toleration{
			Key:    "pool-type",
			Value:  "gpu",
			Effect: "NoSchedule",
		})
		return
	}

	// 超级节点限定
	if taskCfg.GetType() == constants.TASK_GENERATE_PUSH_IMAGE && taskCfg.NeedSuperNode {
		// 超级节点资源限定  https://cloud.tencent.com/document/product/457/44173#.E6.8C.87.E5.AE.9A.E7.B3.BB.E7.BB.9F.E7.9B.98.E5.A4.A7.E5.B0.8F
		annos["eks.tke.cloud.tencent.com/cpu-type"] = "amd,intel"          // cpu类型
		annos["eks.tke.cloud.tencent.com/root-cbs-size"] = "88"            // 磁盘大小
		annos["eks.tke.cloud.tencent.com/reserve-sandbox-duration"] = "1m" // Pod延迟销毁

		tmpt.NodeSelector["billing-type"] = "fixed"
		tmpt.NodeSelector["ignore-resource"] = "true"
		tmpt.Tolerations = append(tmpt.Tolerations, corev1.Toleration{
			Key:    "biz-type",
			Value:  "cicd",
			Effect: "NoSchedule",
		})
		return
	}

	// 特别提速项目ID
	svipPrj := os.Getenv("SpeedUpPrjs")
	svipPrjs := strings.Split(svipPrj, ",")
	if pr.Pipeline != nil && lo.Contains(svipPrjs, strconv.Itoa(int(pr.Pipeline.ProjectID))) {
		tmpt.NodeSelector["cicd"] = "gpu"
		tmpt.Tolerations = append(tmpt.Tolerations, corev1.Toleration{
			Key:    "pool-type",
			Value:  "gpu",
			Effect: "NoSchedule",
		})
		return
	}

	// 随机负载均衡即可
	// if conf.AppConfig.Tekton.NodeNums > 1 {
	// 	tmpt.NodeSelector["tt.com/cicd"] = fmt.Sprintf("node-%d", pr.ID%conf.AppConfig.Tekton.NodeNums)
	// 	return
	// }

	isSpecialPod = false
	return
}

func isUseGpuBuild(t dao.PipelineRunTask) bool {
	if t.GetType() == constants.TASK_AUTOMATION_COMPILE {
		var config model.AutomationCompile
		_ = json.Unmarshal(t.Config, &config)
		return config.GpuBuild
	}

	return false
}
