package new_sentinel

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	cloudagg "52tt.com/cicd/pkg/cloud/aggregate"
	"52tt.com/cicd/pkg/constants"
	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/services/migrate/internal/conf"
	"52tt.com/cicd/services/migrate/internal/dao"
	"52tt.com/cicd/services/migrate/internal/models"
	"52tt.com/cicd/services/migrate/pkg/cim/cicd_ser"
	pkgMd "52tt.com/cicd/services/migrate/pkg/cim/cicd_ser/models"
	"github.com/samber/lo"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/datatypes"
	appv1 "k8s.io/api/apps/v1"
)

type SyncToHSYReq struct {
	AppId         int64                   `json:"appId"`
	ToCluster     string                  `json:"toCluster" `
	ToNamespace   string                  `json:"toNamespace" `
	FromCluster   string                  `json:"fromCluster" `
	FromNamespace string                  `json:"fromNamespace" `
	FromEnvTag    constants.EnumEnvTarget `json:"fromEnvTag" `
	Authorization string                  `json:"authorization "`
}

type SyncPplToHSYReq struct {
	ProjectId  int `json:"projectId"`
	TemplateId int `json:"templateId"`
}

type SyncCdRcdReq struct {
	AppId         int64  `json:"appId"`
	FromCluster   string `json:"fromCluster" `
	FromNamespace string `json:"fromNamespace" `
	Authorization string `json:"authorization "`
}

func SyncCDRcd(ctx context.Context, req SyncCdRcdReq) (err error) {
	// 查询当前运行记录
	isCurrent := true
	cdLog, err := dao.RepoFac.DeployRun.GetChangeLog(ctx, dao.ParamsDeployRun{
		AppID: req.AppId, Cluster: req.FromCluster, Namespace: req.FromNamespace,
		EnvTarget: constants.EnumEnvTargetOrigin, IsCurrent: &isCurrent,
	})
	if err != nil {
		return
	}
	if cdLog != nil {
		err = fmt.Errorf("服务(%d -- %s)在 集群%s,命名空间%s,存在运行中的版本", req.AppId, cdLog.AppName, req.FromCluster, req.FromNamespace)
		return
	}

	// 查询最新部署配置
	mds, err := dao.RepoFac.DeployStaticRepo.FindMetadatas(dao.ParmsCfgMD{
		Cluster: req.FromCluster, Namespace: req.FromNamespace,
		AppID: req.AppId, EnvTarget: constants.EnumEnvTargetOrigin.Value(),
	})
	if err != nil {
		return
	}

	if len(mds) == 0 {
		err = fmt.Errorf("服务在 集群%s,命名空间%s,无部署配置！", req.FromCluster, req.FromNamespace)
		return
	}

	cdCfgs, err := dao.RepoFac.DeployStaticRepo.FindConfigs(dao.ParmsCfg{
		MetadataID: mds[0].ID,
	})
	if err != nil {
		return
	}

	if len(cdCfgs) == 0 {
		err = fmt.Errorf("服务在 集群%s,命名空间%s,无部署配置！", req.FromCluster, req.FromNamespace)
		return
	}

	// 通过牵星平台获取 部署镜像
	cloudCli, err := cloudagg.NewAggClient(&cloudagg.AggCfg{
		HTTPTarget:       conf.GlobalConfig.Cloud.Host,
		GRPCTarget:       conf.GlobalConfig.Cloud.GrpcTarget,
		DeployGRPCTarget: conf.GlobalConfig.Cloud.DeployGrpcTarget,
		Token:            conf.GlobalConfig.Cloud.Token,
	})
	if err != nil {
		return
	}

	imageURL := ""

	resp, err := cloudCli.Get(ctx, &constack.GetRequest{
		Cluster:   req.FromCluster,
		Namespace: req.FromNamespace,
		Name:      mds[0].AppName,
		GroupVersionResource: &constack.GroupVersionResource{
			Group:    "apps",
			Version:  "v1",
			Resource: "deployments",
		},
	})
	if err != nil {
		if status.Code(err) != codes.NotFound {
			err = fmt.Errorf("Call Cloud Get Deployments Resource API Err：%w", err)
			return
		} else {
			resp1, errIn := cloudCli.Get(ctx, &constack.GetRequest{
				Cluster:   req.FromCluster,
				Namespace: req.FromNamespace,
				Name:      mds[0].AppName,
				GroupVersionResource: &constack.GroupVersionResource{
					Group:    "apps",
					Version:  "v1",
					Resource: "statefulsets",
				},
			})
			if errIn != nil {
				err = fmt.Errorf("Call Cloud Get Statefulsets Resource API Err：%w", errIn)
				return
			}
			if len(resp1.Data) == 0 {
				err = fmt.Errorf("%s 服务在 集群%s,命名空间%s,无状态服务！", mds[0].AppName, req.FromCluster, req.FromNamespace)
				return
			}
			sts := appv1.StatefulSet{}
			json.Unmarshal([]byte(resp1.Data), &sts)
			if sts.Spec.Template.Spec.Containers == nil || len(sts.Spec.Template.Spec.Containers) == 0 {
				err = fmt.Errorf("%s 服务在 集群%s,命名空间%s,无镜像！", mds[0].AppName, req.FromCluster, req.FromNamespace)
				return
			}
			imageURL = sts.Spec.Template.Spec.Containers[0].Image
		}
	} else {
		if len(resp.Data) == 0 {
			err = fmt.Errorf("%s 服务在 集群%s,命名空间%s,无服务部署！", mds[0].AppName, req.FromCluster, req.FromNamespace)
			return
		} else {
			deploy := appv1.Deployment{}
			json.Unmarshal([]byte(resp.Data), &deploy)
			if deploy.Spec.Template.Spec.Containers == nil || len(deploy.Spec.Template.Spec.Containers) == 0 {
				err = fmt.Errorf("%s 服务在 集群%s,命名空间%s,无镜像！", mds[0].AppName, req.FromCluster, req.FromNamespace)
				return
			}
			imageURL = deploy.Spec.Template.Spec.Containers[0].Image
		}

	}

	appInfo, has, err := dao.RepoFac.AppPrjRepo.GetApp(ctx, req.AppId)
	if err != nil || !has {
		err = fmt.Errorf("服务(%d) 查询异常 %v", req.AppId, err)
		return
	}

	images := strings.Split(imageURL, "/")
	var artifactVersion string
	artifactVersion = images[len(images)-1]

	operator := cctx.GetUserinfo(ctx)

	deployCL := models.DeployChangeLog{
		Env:                   constants.EnumEnv(mds[0].Env),
		EnvTarget:             constants.EnumEnvTarget(mds[0].EnvTarget),
		Cluster:               mds[0].Cluster,
		Namespace:             mds[0].Namespace,
		AppID:                 req.AppId,
		AppName:               mds[0].AppName,
		ConfigID:              cdCfgs[0].ID,
		ConfigVersion:         cdCfgs[0].Version,
		ConfigType:            constants.DeployConfigType(cdCfgs[0].ConfigType),
		MetadataID:            cdCfgs[0].MetadataID,
		Description:           fmt.Sprintf("批量补充的部署记录  %s ", time.Now().Format("2006-01-02 15:04:05")),
		Status:                constants.DeployStatusSuccessful,
		ArtifactVersion:       artifactVersion,
		ImageUrl:              imageURL,
		OperatorBy:            operator.UserID,
		OperatorByChineseName: operator.ChineseName,
		OperatorByEmployeeNo:  operator.EmployeeNo,
		OperatedAt:            time.Now(),
		Senv:                  mds[0].Senv,
		Action:                constants.DeployActionRollback,
		ProjectID:             appInfo.ProjectID,
		IsCurrent:             true,
	}
	err = dao.RepoFac.DeployRun.CreateChangeLog(ctx, &deployCL)
	if err != nil {
		err = fmt.Errorf("服务(%d -- %s) 创建部署记录异常 %v", req.AppId, mds[0].AppName, err)
		return
	}

	return
}

func SyncPplToHSY(ctx context.Context, req SyncPplToHSYReq) (msg string, err error) {
	ppls, err := dao.RepoFac.PplStaticRepo.FindTemplates(ctx, dao.TmplParm{
		Id:        req.TemplateId,
		ProjectId: req.ProjectId})
	if err != nil {
		return
	}

	println(fmt.Sprintf("待更新流水线模版：%d", len(ppls)))

	for i, ppl := range ppls {
		log.Println(fmt.Sprintf("%d ---- %s", i, ppl.Name))
		for _, stage := range ppl.Stages {
			if stage.Type != constants.STAGE_DEPLOY_TEST_ENV.String() {
				continue
			}
			for _, task := range stage.Tasks {
				addHSY(task)
			}
		}
	}

	return
}

// https://cicd.ttyuyin.com/#/pipeline/single/design/271567?projectId=39
func addHSY(task models.Task) (err error) {

	var nsMap = map[string]struct{}{
		"bigdata-ark":             {},
		"bigdata-bigquery":        {},
		"bigdata-bigquery-web":    {},
		"bigdata-bylink":          {},
		"bigdata-bylink-service":  {},
		"bigdata-dp":              {},
		"bigdata-dxp":             {},
		"bigdata-flink":           {},
		"bigdata-kg":              {},
		"bigdata-platform-common": {},
		"bigdata-python":          {},
		"bigdata-qdos":            {},
		"bigdata-quality":         {},
		"bigdata-spark":           {},
		"bigdata-starrocks":       {},
		"platform-bigdata":        {},
		"argo-workflows":          {},
		"streaming":               {},
	}

	if task.Type == constants.TASK_DEPLOY_ORIGIN.String() || task.Type == constants.TASK_DEPLOY_SUB.String() {
		cfgJson := gjson.ParseBytes(task.Config)
		newEnvs := make([]any, 0)
		if cfgJson.Get("multiEnv").IsArray() {
			multiEnv := cfgJson.Get("multiEnv").Array()
			// 已包含 火山云 跳过
			if lo.ContainsBy(multiEnv, func(item gjson.Result) bool {
				return item.Get("cluster").String() == "k8s-hs-bj-1-test"
			}) {
				return
			}
			for i, item := range multiEnv {
				if !item.IsObject() {
					continue
				}

				newEnvs = append(newEnvs, multiEnv[i].Value())
				if _, ok := nsMap[item.Get("namespace").String()]; ok {
					continue
				}
				newStr, errIn := sjson.Set(item.String(), "cluster", "k8s-hs-bj-1-test")
				if errIn != nil {
					err = errIn
					return
				}
				newEnvs = append(newEnvs, gjson.Parse(newStr).Value())
			}
		}
		if len(newEnvs) > 0 {
			newCfg, err := sjson.Set(string(task.Config), "multiEnv", newEnvs)
			if err != nil {
				return err
			}
			println(newCfg)

			task.Config = datatypes.JSON(newCfg)
			_, err = dao.RepoFac.PplStaticRepo.UpdTask(task, "Config")

		}
	}
	return
}

func SyncToHSY(ctx context.Context, req SyncToHSYReq) (msg string, err error) {
	// 查询当前运行记录
	isCurrent := true
	cdLogs, err := dao.RepoFac.DeployRun.FindChangeLogs(ctx, dao.ParamsDeployRun{
		AppID: req.AppId, Cluster: req.FromCluster, Namespace: req.FromNamespace,
		EnvTarget: req.FromEnvTag, IsCurrent: &isCurrent,
	})
	if err != nil {
		return
	}
	if len(cdLogs) == 0 {
		msg = fmt.Sprintf("服务在 集群%s,命名空间%s,无正在运行的版本", req.FromCluster, req.FromNamespace)
		return
	}

	for _, cdLog := range cdLogs {
		if cdLog.Cluster == req.ToCluster && cdLog.Namespace == req.ToNamespace {
			continue
		}
		msg, err = syncToHSY(ctx, req, cdLog)
		if err != nil {
			return
		}
	}

	return
}

func syncToHSY(ctx context.Context, req SyncToHSYReq, cdLog models.DeployChangeLog) (msg string, err error) {
	// 查询部署配置 集群和NS元数据，不存在则创建
	mds, err := dao.RepoFac.DeployStaticRepo.FindMetadatas(dao.ParmsCfgMD{
		Cluster: req.ToCluster, Namespace: req.ToNamespace,
		AppID: req.AppId, EnvTarget: cdLog.EnvTarget.Value(),
		Senv: cdLog.Senv,
	})
	if err != nil {
		return
	}
	var md *models.DeployMetadata
	// 新增
	if len(mds) == 0 {
		md = &models.DeployMetadata{
			Env:       cdLog.Env.Value(),
			EnvTarget: cdLog.EnvTarget.Value(),
			Cluster:   req.ToCluster,
			Namespace: req.ToNamespace,
			AppID:     req.AppId,
			AppName:   cdLog.AppName,
			Senv:      cdLog.Senv,
		}
		err = dao.RepoFac.DeployStaticRepo.AddMetadata(ctx, md)
		if err != nil {
			return
		}
	} else {
		md = &mds[0]
	}

	// 克隆最新部署配置，已存在则更新
	fromCfgs, err := dao.RepoFac.DeployStaticRepo.FindConfigs(dao.ParmsCfg{
		Id: cdLog.ConfigID,
	})
	if err != nil {
		return
	}
	if len(fromCfgs) == 0 {
		msg = fmt.Sprintf("服务在 集群%s,命名空间%s,无部署配置！", req.FromCluster, req.FromNamespace)
		return
	}

	toCfgs, err := dao.RepoFac.DeployStaticRepo.FindConfigs(dao.ParmsCfg{
		MetadataID: md.ID,
	})
	if err != nil {
		return
	}
	newCfg := fromCfgs[0]
	newCfg.MetadataID = md.ID
	newCfg.ID = 0
	newCfg.Version = 1
	newCfg.CreatedAt = time.Now()
	newCfg.UpdatedAt = time.Now()
	if len(toCfgs) > 0 {
		newCfg.Version = toCfgs[0].Version + 1
	}

	err = dao.RepoFac.DeployStaticRepo.AddConfig(ctx, &newCfg)
	if err != nil {
		return
	}

	md.ConfigID = newCfg.ID
	_, err = dao.RepoFac.DeployStaticRepo.UpdMetadata(ctx, md, "ConfigID")
	if err != nil {
		return
	}

	// 发起一次部署
	cicdSer := cicd_ser.NewAlterSer(conf.GlobalConfig.App.CICDHost)
	cicdSer.SetToken(req.Authorization)
	err = cicdSer.ImageDeploy(pkgMd.ImageDeployReq{
		Env:         cdLog.Env.String(),
		EnvTarget:   cdLog.EnvTarget.String(),
		Cluster:     req.ToCluster,
		Namespace:   req.ToNamespace,
		ConfigID:    newCfg.ID,
		Description: fmt.Sprintf("从集群%s,命名空间%s 部署记录(%d) 迁移 ", req.FromCluster, req.FromNamespace, cdLog.ID),
		ImageURL:    cdLog.ImageUrl,
		AppID:       req.AppId,
		AppName:     cdLog.AppName,
		Senv:        cdLog.Senv,
	})
	if err != nil {
		err = fmt.Errorf("Call CICD ImageDeploy API Err：%w", err)
		return
	}
	return
}
