//go:generate mockgen -destination=event_mock.go -package=service -source=event.go
package service

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/timex"

	"gorm.io/datatypes"

	"52tt.com/cicd/pkg/cloud/aggregate"
	"gorm.io/gorm"

	cloudevents "github.com/cloudevents/sdk-go/v2"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	"github.com/tektoncd/pipeline/pkg/termination"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"knative.dev/pkg/apis"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/event"
	"52tt.com/cicd/pkg/log"
	pbdep "52tt.com/cicd/protocol/deploy"
	pbevent "52tt.com/cicd/protocol/event"
	"52tt.com/cicd/services/pipeline/internal/conf"
	"52tt.com/cicd/services/pipeline/internal/dao"
	"52tt.com/cicd/services/pipeline/internal/model"
	"52tt.com/cicd/services/pipeline/pkg/tekton"
	cdrunner "52tt.com/cicd/services/pipeline/pkg/tekton/cdrunner/v1"
	tektonevent "52tt.com/cicd/services/pipeline/pkg/tekton/event"
)

type eventError struct {
	eventId    string
	tektonName string
	runObjId   int64
	runObjType string
	requestId  string
}

func (e *eventError) Error() string {
	return fmt.Sprintf("event[%s] reqeust[%s] tektonName[%s] runObjId[%d] runObjType[%s]", e.eventId, e.requestId, e.tektonName, e.runObjId, e.runObjType)
}

func (e *eventError) String() string {
	return fmt.Sprintf("event[%s] reqeust[%s] tektonName[%s] runObjId[%d] runObjType[%s]", e.eventId, e.requestId, e.tektonName, e.runObjId, e.runObjType)
}

type EventService interface {
	HandleEvent(ctx context.Context, e *cloudevents.Event) error
}

func NewEventService(prRepo dao.PipelineRunRepository, sender event.Sender, tektonClient tekton.Service,
	deployClient pbdep.DeployServiceClient, redisCli redis.Cmdable, cloudAggCli aggregate.AggClient,
	deployCfgCli pbdep.DeployConfigServiceClient,
) *EventSvc {
	return &EventSvc{
		pipelineRunRepo: prRepo,
		eventSender:     sender,
		tektonClient:    tektonClient,
		deployClient:    deployClient,
		redisCli:        redisCli,
		cloudAggClient:  cloudAggCli,
		deployCfgClient: deployCfgCli,
	}
}

type EventSvc struct {
	pipelineRunRepo dao.PipelineRunRepository
	eventSender     event.Sender
	tektonClient    tekton.Service
	deployClient    pbdep.DeployServiceClient
	redisCli        redis.Cmdable
	cloudAggClient  aggregate.AggClient
	deployCfgClient pbdep.DeployConfigServiceClient
}

func (es *EventSvc) handlePipelineCancel(ctx context.Context, e *cloudevents.Event, runObject tektonevent.ObjectWithCondition, runObjectStatus tektonevent.RunObjectStatus, pipelineRun dao.PipelineRun) error {
	// get run_stage and run_task from db
	if len(runObjectStatus.ChildReferences()) != 1 {
		log.Errorf("处理tekton事件[%s], 解析终止 PipelineRun.ChildReferences 失败 Name[%s] ID[%d] 失败", e.ID(), runObjectStatus.Name(), pipelineRun.ID)
		return fmt.Errorf("处理tekton事件[%s], 解析终止 PipelineRun.ChildReferences 失败 Name[%s] ID[%d] 失败", e.ID(), runObjectStatus.Name(), pipelineRun.ID)
	}
	cancledTask := runObjectStatus.ChildReferences()[0]
	name := cancledTask.PipelineTaskName
	stageId, taskId := parseStageTaskID(name)
	if stageId == nil || taskId == nil {
		log.Errorf("处理tekton事件[%s], 解析终止 PipelineTaskName 失败 Name[%s] ID[%d] 失败", e.ID(), name, pipelineRun.ID)
		return fmt.Errorf("处理tekton事件[%s], 解析终止 PipelineTaskName 失败 Name[%s] ID[%d] 失败", e.ID(), name, pipelineRun.ID)
	}

	// update run_task status to cancel
	trID := *taskId
	tr, err := es.pipelineRunRepo.FindRunTaskById(ctx, trID)
	if err != nil || tr == nil {
		log.Errorf("处理事件[%s(%s)], 获取流水线实例任务[%d]数据信息发生异常: %v", e.ID(), name, trID, err)
		return err
	}
	tr.Status = constants.CANCEL
	err = es.pipelineRunRepo.UpdatePipelineRunTask(ctx, tr)
	if err != nil {
		log.Errorf("处理事件[%s(%s)], 更新流水线实例任务[%d]数据发生异常: %v", e.ID(), name, trID, err)
		return err
	}
	// send event
	tEvent := buildTaskRunEvent(tr, runObjectStatus, runObject)
	result := es.eventSender.Send(context.Background(), event.NewPipelineRelatedEvent(tEvent, event.WithEventType("taskrun"), event.WithID(name)))
	if event.IsUndelivered(result) {
		log.Errorf("处理tekton事件[%s], 投递TaskRun事件 Name[%s] ID[%d] 失败", e.ID(), runObjectStatus.Name(), trID)
		return fmt.Errorf("处理tekton事件[%s], 投递TaskRun事件 Name[%s] ID[%d] 失败", e.ID(), runObjectStatus.Name(), trID)
	}
	if event.IsACK(result) {
		log.InfoWithCtx(ctx, "处理tekton事件[%s], 投递TaskRun事件 Name[%s] ID[%d] 成功", e.ID(), runObjectStatus.Name(), trID)
	}

	// update run_stage status to cancel
	srID := *stageId
	sr, err := es.pipelineRunRepo.GetPipelineRunStageById(ctx, srID)
	if err != nil || sr == nil {
		log.Errorf("处理事件[%s(%s)], 获取流水线实例阶段[%d]数据信息发生异常: %v", e.ID(), name, srID, err)
		return err
	}
	if tr == nil || sr == nil {
		log.Errorf("处理事件[%s(%s)], 获取阶段[%d]任务[%d]数据为空，可能存在脏数据", e.ID(), name, srID, trID)
		return fmt.Errorf("处理事件[%s(%s)], 获取阶段[%d]任务[%d]数据为空，可能存在脏数据", e.ID(), name, srID, trID)
	}
	sr.Status = constants.CANCEL

	err = es.pipelineRunRepo.UpdatePipelineRunStage(ctx, sr)
	if err != nil {
		log.Errorf("处理事件[%s(%s)], 更新流水线实例阶段[%d]数据发生异常: %v", e.ID(), name, srID, err)
		return err
	}

	// send event
	sEvent := buildStageRunEvent(sr, runObjectStatus)
	result = es.eventSender.Send(context.Background(), event.NewPipelineRelatedEvent(sEvent, event.WithEventType("stagerun")))
	if event.IsUndelivered(result) {
		log.Errorf("处理tekton事件[%s], 投递StageRun事件 Name[%s] ID[%d] 失败", e.ID(), runObjectStatus.Name(), srID)
		return fmt.Errorf("处理tekton事件[%s], 投递StageRun事件 Name[%s] ID[%d] 失败", e.ID(), runObjectStatus.Name(), srID)
	}
	if event.IsACK(result) {
		log.InfoWithCtx(ctx, "处理tekton事件[%s], 投递StageRun事件 Name[%s] ID[%d] 成功", e.ID(), runObjectStatus.Name(), srID)
	}
	return nil
}

func (es *EventSvc) handleChangeLogStatus(ctx context.Context, customRun RunObject, taskRun *dao.PipelineRunTask, subtaskRun *dao.PipelineRunSubtask) {
	trType := taskRun.GetType()
	isDeployType := trType.IsDeployTask() || trType.IsImageSyncTask()
	status := customRun.Status()
	log.Infof("处理tekton部署事件, taskrun部署任务的类型和状态[%v] [%s] ID[%d]", isDeployType, status, taskRun.ID)
	if isDeployType && isDeployCompleted(status) {
		var updateStatus constants.DeployStatus
		switch status {
		case constants.SUCCESSFUL:
			updateStatus = constants.DeployStatusSuccessful
		case constants.FAILED:
			updateStatus = constants.DeployStatusFailed
		default:
			updateStatus = constants.DeployStatusFailed
		}
		var subTaskID int64
		if subtaskRun != nil {
			log.Infof("处理tekton部署事件, taskrun部署任务的类型和状态[%v] [%s] ID[%d] subtaskRunId[%d]", isDeployType, status, taskRun.ID, subtaskRun.ID)
			subTaskID = subtaskRun.ID
		}
		//subTaskID, err := customRun.SubTaskId()
		//if err != nil {
		//	log.Errorf("sub task get sub task id failed:%v", err)
		//}
		updateCLReq := &pbdep.UpdateChangeLogReq{
			TaskRunID: taskRun.ID,
			Status:    int32(updateStatus),
			SubtaskId: subTaskID,
		}

		_, err := es.deployClient.UpdateChangeLogStatus(ctx, updateCLReq)
		if err != nil {
			log.Errorf("处理tekton事件[%s], 部署记录变更，[%s] ID[%d] %s", status, taskRun.ID, err.Error())
		}
	}
}

// todo 1107927 remove
// 构建基准环境的 mesh 路由需要大于 趣丸 10 周年方能十全十美
//var _buildPrimaryRouteDeployRunTaskCreateAtAfterAt = time.Date(2023, 9, 23, 07, 55, 0, 0, time.Local)
//
//type handleSuccessDeployEventToBuildRouteOption struct {
//	appName              string
//	automationDeployData datatypes.JSON
//	taskCreateAt         time.Time
//	mainTaskID           int64
//}
//
//
//func (es *EventSvc) handleSuccessDeployEventToBuildRoute(ctx context.Context, opt *handleSuccessDeployEventToBuildRouteOption) {
//	// 回调事件 子环境新增路由
//	var automationDeploy model.AutomationDeploy
//	if err := json.Unmarshal(opt.automationDeployData, &automationDeploy); err != nil {
//		log.ErrorWithCtx(ctx, "[handleSuccessDeployEventToBuildRoute] failed to Unmarshal automationDeploy, err: %v, taskRunID: %d", err, opt.mainTaskID)
//		return
//	}
//
//	buildRouteReq := &routing.BuildRequest{
//		ServiceName:  opt.appName,
//		EnvTarget:    constants.EnvTargetType(automationDeploy.EnvTarget),
//		Cluster:      automationDeploy.Cluster,
//		Namespace:    automationDeploy.Namespace,
//		SubNamespace: automationDeploy.SubNamespace,
//		Senv:         automationDeploy.Senv,
//		TrafficMark:  automationDeploy.TrafficMark,
//	}
//	buildRouteOption := &routing.BuildExtraOption{
//		TaskCreateAt: opt.taskCreateAt,
//		MainTaskID:   opt.mainTaskID,
//		ConfigID:     automationDeploy.ConfigId,
//	}
//
//	switch constants.EnvTargetType(automationDeploy.EnvTarget) {
//	case constants.SUB, constants.SUB_V2:
//		_ = routing.HandleBuildSubenvRouteCompleteProcess(ctx, es.cloudAggClient, es.cloudAggClient, es.deployCfgClient, buildRouteReq, buildRouteOption)
//	case constants.ORIGIN:
//		_ = routing.HandleBuildPrimaryRoute(ctx, es.cloudAggClient, buildRouteReq, buildRouteOption)
//	default:
//		log.WarnWithCtx(ctx, "[handleSuccessDeployEventToBuildRoute] unsupported envTarget: %s, taskRunID: %d", automationDeploy.EnvTarget, opt.mainTaskID)
//		return
//	}
//}

type handleCallDeployChainOption struct {
	appName          string
	autoDeployConfig datatypes.JSON
	mainTaskID       int64
	mainTaskType     constants.Task
	subTaskID        int64
	eventStatus      constants.PipelineStatus
	pipelineRunID    int64
}

func isDeployCompleted(status constants.PipelineStatus) bool {
	return status == constants.SUCCESSFUL || status == constants.FAILED || status == constants.CANCEL
}

// coverTektonResults cover v1beta1.RunResult to v1beta1.TaskRunResult
func coverTektonResults(results []v1beta1.RunResult) []v1beta1.TaskRunResult {
	var taskRunResults []v1beta1.TaskRunResult
	for _, r := range results {
		v := v1beta1.ResultValue{}
		err := v.UnmarshalJSON([]byte(r.Value))
		if err != nil {
			log.Errorf("unmarshal result value failed: %v", err)
			continue
		}
		taskRunResult := v1beta1.TaskRunResult{
			Name:  r.Key,
			Type:  v1beta1.ResultsType(v.Type),
			Value: v,
		}
		taskRunResults = append(taskRunResults, taskRunResult)
	}
	return taskRunResults
}

func getIdFromAnnotation(runObject tektonevent.ObjectWithCondition, key constants.SystemAnnotationKey) (int64, error) {
	annotations := runObject.GetObjectMeta().GetAnnotations()
	val, ok := annotations[string(key)]
	if !ok {
		return 0, fmt.Errorf("annotation info not found for in %v", key)
	}

	valInt64, err := strconv.ParseInt(val, 10, 64)
	if err != nil {
		return 0, err
	}

	return valInt64, nil
}

func buildTaskRunEvent(tr *dao.PipelineRunTask, runObjectStatus tektonevent.RunObjectStatus, runObject tektonevent.ObjectWithCondition) *pbevent.TaskRunEvent {
	results := &structpb.Struct{}
	_ = protojson.Unmarshal(tr.Result, results)
	e := &pbevent.TaskRunEvent{
		Id:                 tr.ID,
		StartedTime:        timestamppb.New(runObjectStatus.StartedTime()),
		Name:               tr.TektonName,
		Namespace:          tr.TektonNamespace,
		Type:               tr.Type,
		Status:             string(tr.Status),
		PipelineRunStageId: tr.PipelineRunStageId,
		Results:            results,
	}

	if runObjectStatus.IsCompleted() {
		e.CompletedTime = timestamppb.New(runObjectStatus.CompletedTime())
		e.ElapsedTime = durationpb.New(runObjectStatus.ElapsedTime())
	}

	prId, _ := getIdFromAnnotation(runObject, constants.PipelineRunIdKey)
	e.PipelineRunId = prId
	return e
}

func buildStageRunEvent(sr *dao.PipelineRunStage, runObjectStatus tektonevent.RunObjectStatus) *pbevent.StageRunEvent {
	e := &pbevent.StageRunEvent{
		Id:            sr.ID,
		StartedTime:   timestamppb.New(runObjectStatus.StartedTime()),
		Type:          sr.Type,
		Status:        string(sr.Status),
		PipelineRunId: sr.PipelineRunId,
	}

	if runObjectStatus.IsCompleted() {
		e.CompletedTime = timestamppb.New(runObjectStatus.CompletedTime())
		e.ElapsedTime = durationpb.New(runObjectStatus.ElapsedTime())
	}

	return e
}

// parseStageTaskID returns stageID and taskID
func parseStageTaskID(name string) (*int64, *int64) {
	strSlice := strings.Split(name, "-")

	// format XX-XX-XX-XX-stage-1678-task-3199
	if len(strSlice) < 3 {
		return nil, nil
	}
	stageIdStr := strSlice[len(strSlice)-3]
	taskIdStr := strSlice[len(strSlice)-1]

	stageID, err := strconv.ParseInt(stageIdStr, 10, 64)
	if err != nil {
		return nil, nil
	}
	taskID, err := strconv.ParseInt(taskIdStr, 10, 64)
	if err != nil {
		return nil, nil
	}

	return &stageID, &taskID
}

// IsLastCITask returns whether the task is the last ci task in the pipeline run
// tr is PipelineRunTask should contain the StageRun
// pr is PipelineRun should contain all the StageRun and TaskRun
// ciStages is the ci stages config from conf.GetCiStagesConfig()
func IsLastCITask(tr dao.PipelineRunTask, pr dao.PipelineRun, ciStages []string) bool {
	ciTypeMap := make(map[string]struct{})
	for _, t := range ciStages {
		ciTypeMap[t] = struct{}{}
	}
	// stage isn't ci stage
	if _, ok := ciTypeMap[tr.StageRun.Type]; !ok {
		return false
	}

	var lastCITask dao.PipelineRunTask
	for i := len(pr.Stages) - 1; i >= 0; i-- {
		stage := pr.Stages[i]
		if _, ok := ciTypeMap[stage.Type]; ok {
			// is the last ci stage
			lastCITask = stage.Tasks[len(stage.Tasks)-1]
			break
		}
	}
	return lastCITask.ID == tr.ID
}

// IsLastCIStage 是否最后一个ci 阶段
func IsLastCIStage(tr dao.PipelineRunTask, pr dao.PipelineRun, ciStages []string) bool {
	ciTypeMap := make(map[string]struct{})
	for _, t := range ciStages {
		ciTypeMap[t] = struct{}{}
	}

	// stage isn't ci stage
	if _, ok := ciTypeMap[tr.StageRun.Type]; !ok {
		return false
	}
	var lastCiStage dao.PipelineRunStage
	for i := len(pr.Stages) - 1; i >= 0; i-- {
		stage := pr.Stages[i]
		if _, ok := ciTypeMap[stage.Type]; ok {
			lastCiStage = stage
			break
		}
	}
	return lastCiStage.ID == tr.StageRun.ID
}

// 是否是最后一个stage
func isEndStageOfPipelineRun(sr *dao.PipelineRunStage, pr *dao.PipelineRun) bool {
	prStageSequence := make([]int64, 0)
	for _, s := range strings.Split(pr.StageSequence, ",") {
		i, _ := strconv.ParseInt(s, 10, 64)
		prStageSequence = append(prStageSequence, i)
	}
	return sr.ID == prStageSequence[len(prStageSequence)-1]
}

// pr must contain all Stages and Stage.Tasks
func isLastTaskOfPipelineRun(tr *dao.PipelineRunTask, pr *dao.PipelineRun) bool {
	lastStage := pr.Stages[len(pr.Stages)-1]
	lastTask := lastStage.Tasks[len(lastStage.Tasks)-1]
	return lastTask.ID == tr.ID
}

func IsOnlyCIPipelinerun(pr *dao.PipelineRun, ciStages []string) bool {
	ciTypeMap := make(map[string]struct{})
	for _, t := range ciStages {
		ciTypeMap[t] = struct{}{}
	}

	// check last stage of pipelinrun is ci stage
	lastStage := pr.Stages[len(pr.Stages)-1]
	if _, ok := ciTypeMap[lastStage.Type]; !ok {
		return false
	} else {
		return true
	}
}

type RunObject interface {
	IsTaskRunEvent() bool
	IsPipelineRunEvent() bool
	IsCustomRunEvent() bool
	IsSuccessEvent() bool
	IsRunningEvent() bool
	IsStartedEvent() bool
	IsFailedEvent() bool
	IsUnknownEvent() bool
	IsSubTaskEvent() bool

	PipelineRunId() (int64, error)
	RunTaskId() (int64, error)
	SubTaskId() (int64, error)
	EventID() string
	RequestID() string
	EventTime() time.Time
	GetAnnotation(key string) string
	GetStatusAnnotation(key string) string
	// Name is Tekton Object Name: TaskRun, PipelineRun, CustomRun Name.
	Name() string
	Namespace() string
	// Status should contain biz logic: such as Unhandle, cancel etc.
	Status() constants.PipelineStatus
	StartedTime() time.Time
	CompletedTime() time.Time
	ElapsedTime() time.Duration
	PodName() string

	// Result will parser TaskRun and CustomRun result to dao.PipelineRunTask.Result
	// return '{"results: [{}, {}]"}'
	Result() ([]byte, error)
	// Workspaces parser PipelineRun workspaces to dao.PipelineRun.Workspaces
	// return '{"workspaces: [{}, {}]"}'
	Workspaces() ([]byte, error)
	// MessageResult parser failed event from TaskRun.Status.Steps.Terminated.Message and
	// covert it to v1beta1.TaskRunResult
	MessageResult() ([]byte, error)
	// FetchLog This method responsibility for fetching log from different tektonObject.
	FetchLog() (string, error)
	// Conditions parser PipelineRun.Status.Conditions
	Conditions() ([]byte, error)
	Reason() string
	// Spec return k8s spec
	Spec() ([]byte, error)
	Type() string
}

func RunObjectFromEvent(e *cloudevents.Event) (RunObject, error) {
	var (
		err       error
		data      tektonevent.CloudEventData
		runObject RunObject
	)
	err = json.Unmarshal(e.Data(), &data)
	if err != nil {
		return nil, err
	}
	switch tektonevent.TektonEventType(e.Type()) {
	case tektonevent.TaskRunStartedEventV1, tektonevent.TaskRunRunningEventV1,
		tektonevent.TaskRunSuccessfulEventV1, tektonevent.TaskRunFailedEventV1,
		tektonevent.TaskRunUnknownEventV1:

		runObject = &taskRunObject{
			baseRunObject: baseRunObject{
				obj:  data.TaskRun,
				data: e,
			},
			taskRun: data.TaskRun,
		}

	case tektonevent.PipelineRunStartedEventV1, tektonevent.PipelineRunRunningEventV1,
		tektonevent.PipelineRunSuccessfulEventV1, tektonevent.PipelineRunFailedEventV1,
		tektonevent.PipelineRunUnknownEventV1:

		runObject = &pipelineRunObject{
			baseRunObject: baseRunObject{
				obj:  data.PipelineRun,
				data: e,
			},
			pipelineRun: data.PipelineRun,
		}

	case tektonevent.CustomRunStartedEventV1, tektonevent.CustomRunRunningEventV1,
		tektonevent.CustomRunSuccessfulEventV1, tektonevent.CustomRunFailedEventV1:

		runObject = &customRunObject{
			baseRunObject: baseRunObject{
				obj:  data.CustomRun,
				data: e,
			},
			customRun: data.CustomRun,
		}

	default:
		return nil, fmt.Errorf("unknown tekton event [%s] type for in %s[%s]", e.ID(), e.Subject(), e.Type())
	}

	return runObject, nil
}

// baseRunObject should implement RunObject common methods. Other RunObject should embed baseRunObject.
type baseRunObject struct {
	obj  tektonevent.ObjectWithCondition
	data *cloudevents.Event
}

func (b *baseRunObject) EventID() string {
	return b.data.ID()
}

func (b *baseRunObject) EventTime() time.Time {
	return b.data.Time()
}

func (b *baseRunObject) IsFailedEvent() bool {
	var result bool
	switch tektonevent.TektonEventType(b.data.Type()) {
	case tektonevent.TaskRunFailedEventV1, tektonevent.PipelineRunFailedEventV1, tektonevent.CustomRunFailedEventV1:
		result = true
	default:
		result = false
	}
	return result
}

func (b *baseRunObject) IsSuccessEvent() bool {
	var result bool
	switch tektonevent.TektonEventType(b.data.Type()) {
	case tektonevent.TaskRunSuccessfulEventV1, tektonevent.PipelineRunSuccessfulEventV1, tektonevent.CustomRunSuccessfulEventV1:
		result = true
	default:
		result = false
	}
	return result
}

func (b *baseRunObject) IsRunningEvent() bool {
	var result bool
	switch tektonevent.TektonEventType(b.data.Type()) {
	case tektonevent.TaskRunRunningEventV1, tektonevent.PipelineRunRunningEventV1, tektonevent.CustomRunRunningEventV1:
		result = true
	default:
		result = false
	}
	return result
}

func (b *baseRunObject) IsStartedEvent() bool {
	var result bool
	switch tektonevent.TektonEventType(b.data.Type()) {
	case tektonevent.TaskRunStartedEventV1, tektonevent.PipelineRunStartedEventV1, tektonevent.CustomRunStartedEventV1:
		result = true
	default:
		result = false
	}
	return result
}

func (b *baseRunObject) IsUnknownEvent() bool {
	var result bool
	switch tektonevent.TektonEventType(b.data.Type()) {
	case tektonevent.TaskRunUnknownEventV1, tektonevent.PipelineRunUnknownEventV1:
		result = true
	default:
		result = false
	}
	return result
}

func (b *baseRunObject) IsSubTaskEvent() bool {
	// TODO: implement it
	return false
}

func (b *baseRunObject) SubTaskId() (int64, error) {
	// TODO: implement it
	return 0, nil
}

func (b *baseRunObject) Name() string {
	return b.obj.GetObjectMeta().GetName()
}

func (b *baseRunObject) Namespace() string {
	return b.obj.GetObjectMeta().GetNamespace()
}

func (b *baseRunObject) PodName() string {
	return ""
}

func (b *baseRunObject) Workspaces() ([]byte, error) {
	return nil, nil
}

func (b *baseRunObject) GetAnnotation(key string) string {
	annotations := b.obj.GetObjectMeta().GetAnnotations()
	return annotations[key]
}

func (b *baseRunObject) PipelineRunId() (int64, error) {
	pipelineRunId := b.GetAnnotation(string(constants.PipelineRunIdKey))
	if pipelineRunId == "" {
		return 0, fmt.Errorf("pipelineRunId annotation is empty")
	}
	return strconv.ParseInt(pipelineRunId, 10, 64)
}

func (b *baseRunObject) RequestID() string {
	return b.GetAnnotation(string(constants.RequestIDKey))
}

func (b *baseRunObject) IsTaskRunEvent() bool {
	_, ok := b.obj.(*v1beta1.TaskRun)
	return ok
}

func (b *baseRunObject) IsPipelineRunEvent() bool {
	_, ok := b.obj.(*v1beta1.PipelineRun)
	return ok
}

func (b *baseRunObject) IsCustomRunEvent() bool {
	_, ok := b.obj.(*v1beta1.CustomRun)
	return ok
}

func (b *baseRunObject) StartedTime() time.Time {
	// NOTICE: consider Status.StartedTime == nil
	return time.Time{}
}

func (b *baseRunObject) MessageResult() ([]byte, error) {
	return nil, nil
}

func (b *baseRunObject) Result() ([]byte, error) {
	return nil, nil
}

func (b *baseRunObject) FetchLog() (string, error) {
	return "", nil
}

func (b *baseRunObject) Conditions() ([]byte, error) {
	return nil, nil
}

func (b *baseRunObject) Reason() string {
	c := b.obj.GetStatusCondition().GetCondition(apis.ConditionSucceeded)
	if c == nil {
		return ""
	}
	return c.Reason
}

func (b *baseRunObject) Spec() ([]byte, error) {
	return nil, nil
}

func (b *baseRunObject) Type() string {
	return b.data.Type()
}

type taskRunObject struct {
	baseRunObject
	taskRun *v1beta1.TaskRun
}

func (o *taskRunObject) PodName() string {
	return o.taskRun.Status.PodName
}

func (o *taskRunObject) StartedTime() time.Time {
	if o.taskRun.Status.StartTime == nil || o.taskRun.Status.StartTime.IsZero() {
		return time.Time{}
	} else {
		return o.taskRun.Status.StartTime.Time
	}
}

func (o *taskRunObject) CompletedTime() time.Time {
	if o.taskRun.Status.CompletionTime == nil || o.taskRun.Status.CompletionTime.IsZero() {
		return time.Time{}
	} else {
		return o.taskRun.Status.CompletionTime.Time
	}
}

func (o *taskRunObject) ElapsedTime() time.Duration {
	if o.CompletedTime().IsZero() {
		return time.Since(o.StartedTime())
	} else {
		return o.CompletedTime().Sub(o.StartedTime())
	}
}

func (o *taskRunObject) GetStatusAnnotation(key string) string {
	if o.taskRun.Status.Annotations == nil {
		return ""
	}
	return o.taskRun.Status.Annotations[key]
}

func (o *taskRunObject) Status() constants.PipelineStatus {
	c := o.taskRun.GetStatusCondition().GetCondition(apis.ConditionSucceeded)
	if c == nil {
		log.Errorf("event [%s] taskrun [%s] condition %s not found, set to %s", o.EventID(), o.Name(), apis.ConditionSucceeded, constants.PENDING)
		return constants.PENDING
	}
	var status constants.PipelineStatus
	switch {
	case c.IsUnknown():
		switch c.Reason {
		case v1beta1.TaskRunReasonStarted.String():
			status = constants.RUNNING
		case v1beta1.TaskRunReasonRunning.String():
			status = constants.RUNNING
		case "Pending":
			if o.baseRunObject.IsUnknownEvent() {
				status = constants.RUNNING
			} else {
				log.Warnf("event [%s] taskrun [%s] Pending is not UnknownEvent, set to %s", o.EventID(), o.Name(), apis.ConditionSucceeded, c.Reason, constants.PENDING)
				status = constants.PENDING
			}
		default:
			log.Warnf("event [%s] taskrun [%s] condition %s reason %s invalid, set to %s", o.EventID(), o.Name(), apis.ConditionSucceeded, c.Reason, constants.PENDING)
			status = constants.PENDING
		}
	case c.IsFalse():
		switch c.Reason {
		case v1beta1.TaskRunReasonCancelled.String():
			status = constants.CANCEL
		default:
			status = constants.FAILED
		}
	case c.IsTrue():
		status = constants.SUCCESSFUL
	default:
		log.Errorf("event [%s] taskrun [%s] condition %s status %s invalid, set to %s", o.EventID(), o.Name(), apis.ConditionSucceeded, c.Status, constants.PENDING)
		status = constants.PENDING
	}
	return status
}

func (o *taskRunObject) RunTaskId() (int64, error) {
	val := o.GetAnnotation(string(constants.PipelineRunTaskIdKey))
	if val != "" {
		// parse taskId from annotation
		valI, err := strconv.ParseInt(val, 10, 64)
		if err != nil {
			return 0, fmt.Errorf("%v: get taskrun failed", err)
		}
		return valI, nil
	} else {
		// parse taskId from task name
		taskName := o.taskRun.Name
		_, taskId := parseStageTaskID(taskName)
		if taskId == nil {
			return 0, fmt.Errorf("parse taskId from task name %s failed", taskName)
		} else {
			return *taskId, nil
		}
	}
}

func (o *taskRunObject) MessageResult() ([]byte, error) {
	var results []v1beta1.TaskRunResult
	if len(o.taskRun.Status.Steps) == 0 {
		log.Errorf("status steps is empty")
		return nil, fmt.Errorf("status steps is empty")
	}
	for i, step := range o.taskRun.Status.Steps {
		if step.Terminated != nil && len(step.Terminated.Message) > 0 {
			msg := step.Terminated.Message
			runResults, err := termination.ParseMessage(log.SugarLogger, msg)
			if err != nil {
				log.Errorf("parse Status.Steps[%d].Terminated.Message failed: %v", i, err)
				continue
			}
			trr := coverTektonResults(runResults)
			if len(trr) == 0 {
				log.Warnf("cover RunResults to TaskRunResults failed")
				continue
			}
			results = append(results, trr...)
		}
	}
	if len(results) == 0 {
		log.Warn("message result is zero")
		return nil, nil
	}
	data, err := json.Marshal(map[string][]v1beta1.TaskRunResult{
		"results": results,
	})
	return data, err
}

func (o *taskRunObject) Result() ([]byte, error) {
	results := o.taskRun.Status.TaskRunResults
	if len(results) == 0 || results == nil {
		return nil, nil
	}
	data, err := json.Marshal(map[string][]v1beta1.TaskRunResult{
		"results": results,
	})
	return data, err
}

func (o *taskRunObject) Conditions() ([]byte, error) {
	c := o.taskRun.Status.GetConditions()
	return json.Marshal(c)
}

func (o *taskRunObject) Spec() ([]byte, error) {
	c := o.taskRun.Spec
	return json.Marshal(c)
}

type pipelineRunObject struct {
	baseRunObject
	pipelineRun *v1beta1.PipelineRun
}

func (o *pipelineRunObject) GetStatusAnnotation(key string) string {
	if o.pipelineRun.Status.Annotations == nil {
		return ""
	}
	return o.pipelineRun.Status.Annotations[key]
}

func (o *pipelineRunObject) Status() constants.PipelineStatus {
	c := o.pipelineRun.GetStatusCondition().GetCondition(apis.ConditionSucceeded)
	if c == nil {
		log.Errorf("event [%s] pipelinerun [%s] condition %s not found, set to %s", o.EventID(), o.Name(), apis.ConditionSucceeded, constants.PENDING)
		return constants.PENDING
	}
	var status constants.PipelineStatus
	switch {
	case c.IsUnknown():
		switch c.Reason {
		case v1beta1.PipelineRunReasonStarted.String():
			status = constants.RUNNING
		case v1beta1.PipelineRunReasonRunning.String():
			status = constants.RUNNING
		case v1beta1.PipelineRunReasonStopping.String():
			status = constants.UNKNOWN
		default:
			log.Warnf("event [%s] pipelinerun [%s] condition %s reason %s invalid, set to %s", o.EventID(), o.Name(), apis.ConditionSucceeded, c.Reason, constants.PENDING)
			status = constants.PENDING
		}
	case c.IsFalse():
		switch c.Reason {
		case v1beta1.PipelineRunReasonCancelled.String():
			status = constants.CANCEL
		default:
			status = constants.FAILED
		}
	case c.IsTrue():
		status = constants.SUCCESSFUL
	default:
		log.Errorf("event [%s] pipelinerun [%s] condition %s status %s invalid, set to %s", o.EventID(), o.Name(), apis.ConditionSucceeded, c.Status, constants.PENDING)
		status = constants.PENDING
	}
	return status
}

func (o *pipelineRunObject) RunTaskId() (int64, error) {
	return 0, nil
}

func (o *pipelineRunObject) Workspaces() ([]byte, error) {
	workspaces := o.pipelineRun.Spec.Workspaces
	if len(workspaces) == 0 {
		return nil, nil
	}
	data, err := json.Marshal(map[string][]v1beta1.WorkspaceBinding{
		"workspaces": workspaces,
	})
	return data, err
}

func (o *pipelineRunObject) StartedTime() time.Time {
	if o.pipelineRun.Status.StartTime == nil || o.pipelineRun.Status.StartTime.IsZero() {
		return time.Time{}
	} else {
		return o.pipelineRun.Status.StartTime.Time
	}
}

func (o *pipelineRunObject) CompletedTime() time.Time {
	if o.pipelineRun.Status.CompletionTime == nil || o.pipelineRun.Status.CompletionTime.IsZero() {
		return time.Time{}
	} else {
		return o.pipelineRun.Status.CompletionTime.Time
	}
}

func (o *pipelineRunObject) ElapsedTime() time.Duration {
	if o.CompletedTime().IsZero() {
		return time.Since(o.StartedTime())
	} else {
		return o.CompletedTime().Sub(o.StartedTime())
	}
}

func (o *pipelineRunObject) Conditions() ([]byte, error) {
	c := o.pipelineRun.Status.GetConditions()
	return json.Marshal(c)
}

func (o *pipelineRunObject) Spec() ([]byte, error) {
	c := o.pipelineRun.Spec
	return json.Marshal(c)
}

type customRunObject struct {
	baseRunObject
	customRun *v1beta1.CustomRun
}

func (o *customRunObject) StartedTime() time.Time {
	if o.customRun.Status.StartTime == nil || o.customRun.Status.StartTime.IsZero() {
		return time.Time{}
	} else {
		return o.customRun.Status.StartTime.Time
	}
}

func (o *customRunObject) CompletedTime() time.Time {
	if o.customRun.Status.CompletionTime == nil || o.customRun.Status.CompletionTime.IsZero() {
		return time.Time{}
	} else {
		return o.customRun.Status.CompletionTime.Time
	}
}

func (o *customRunObject) ElapsedTime() time.Duration {
	if o.CompletedTime().IsZero() {
		return time.Since(o.StartedTime())
	} else {
		return o.CompletedTime().Sub(o.StartedTime())
	}
}

func (o *customRunObject) IsSubTaskEvent() bool {
	subTaskID, err := o.SubTaskId()
	if err != nil {
		return false
	}
	return subTaskID != 0
}

func (o *customRunObject) SubTaskId() (int64, error) {
	var (
		deployContent *cdrunner.DeployContent
		err           error
	)

	for _, param := range o.customRun.Spec.Params {
		if param.Name == "content" {
			content := param.Value.StringVal
			deployContent, err = cdrunner.UnmarshalDeployContent(content)
			if err == nil {
				// task is deploy task
				break
			}
		}
	}
	if deployContent == nil {
		return 0, nil
	}
	if deployContent.ApprovalDeployType != cdrunner.ApprovalDeployTypeMultiCluster {
		return 0, nil
	}
	taskRunContent, err := deployContent.UnmarshalToSubTaskRunContent()
	if err != nil {
		return 0, errors.Wrap(err, "unmarshal to subtaskrun content failed")
	}
	return taskRunContent.SubTaskID, nil
}

func (o *customRunObject) RunTaskId() (int64, error) {
	labels := o.customRun.GetObjectMeta().GetLabels()
	var l string
	for k, v := range labels {
		if k == "tekton.dev/pipelineTask" {
			l = v
		}
	}
	_, taskId := parseStageTaskID(l)
	if taskId == nil {
		return 0, fmt.Errorf("get taskId from customrun labels failed")
	}
	return *taskId, nil
}

func (o *customRunObject) GetStatusAnnotation(key string) string {
	if o.customRun.Status.Annotations == nil {
		return ""
	}
	return o.customRun.Status.Annotations[key]
}

func (o *customRunObject) Status() constants.PipelineStatus {
	c := o.customRun.GetStatusCondition().GetCondition(apis.ConditionSucceeded)
	if c == nil {
		// FYI: When CustomRun started, Status field like this: "status": {"extraFields": null}
		log.Errorf("event [%s] customrun [%s] condition %s not found, set to %s", o.EventID(), o.Name(), apis.ConditionSucceeded, constants.UNHANDLED)
		return constants.UNHANDLED
	}
	var status constants.PipelineStatus
	switch {
	case c.IsUnknown():
		switch c.Reason {
		case "Waiting":
			status = constants.UNHANDLED
		case v1beta1.PipelineRunReasonStarted.String():
			status = constants.UNHANDLED
		case v1beta1.PipelineRunReasonRunning.String():
			status = constants.RUNNING
		default:
			log.Warnf("event [%s] customrun [%s] condition %s reason %s invalid, set to %s", o.EventID(), o.Name(), apis.ConditionSucceeded, c.Reason, constants.UNHANDLED)
			status = constants.UNHANDLED
		}
	case c.IsFalse():
		switch c.Reason {
		case "Cancelled":
			status = constants.CANCEL
		default:
			status = constants.FAILED
		}
	case c.IsTrue():
		switch c.Reason {
		case "Skip":
			status = constants.SKIPPED
		default:
			status = constants.SUCCESSFUL
		}
	default:
		log.Errorf("event [%s] customrun [%s] condition %s status %s invalid, set to %s", o.EventID(), o.Name(), apis.ConditionSucceeded, c.Status, constants.PENDING)
		status = constants.PENDING
	}
	return status
}

func (o *customRunObject) Result() ([]byte, error) {
	results := o.customRun.Status.Results
	if len(results) == 0 || results == nil {
		return nil, nil
	}
	data, err := json.Marshal(map[string][]v1beta1.CustomRunResult{
		"results": results,
	})
	return data, err
}

func (o *customRunObject) Conditions() ([]byte, error) {
	c := o.customRun.Status.GetConditions()
	return json.Marshal(c)
}

func (o *customRunObject) FetchLog() (string, error) {
	conditions := o.customRun.Status.Conditions
	var cond string
	if len(conditions) > 0 {
		for _, c := range conditions {
			cond += fmt.Sprintf("%s\n", c.Message)
		}
	}
	return cond, nil
}

func (o *customRunObject) Spec() ([]byte, error) {
	c := o.customRun.Spec
	return json.Marshal(c)
}

// sendUpdateChangeLogReq 通知 Deploy 服务部署任务对应的 ChangeLog 状态
func (es *EventSvc) sendUpdateChangeLogReq(ctx context.Context, changeLogID int64, r RunObject) {
	status := r.Status()
	if isDeployCompleted(status) {
		var updateStatus constants.DeployStatus
		switch status {
		case constants.SUCCESSFUL:
			updateStatus = constants.DeployStatusSuccessful
		case constants.FAILED:
			updateStatus = constants.DeployStatusFailed
		default:
			updateStatus = constants.DeployStatusFailed
		}

		updateCLReq := &pbdep.UpdateChangeLogReq{
			Id:     changeLogID,
			Status: int32(updateStatus),
		}

		log.InfoWithCtx(ctx, "deploy event [%s] PipelineRun [%s] finalized, update changelog status to %s, clID=%d", r.EventID(), r.Name(), updateStatus, changeLogID)
		_, err := es.deployClient.UpdateChangeLogStatus(ctx, updateCLReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "handle deploy event [%s], update id [%d] status failed %s, clID=%d", r.EventID(), changeLogID, err.Error(), changeLogID)
		}
	} else {
		log.InfoWithCtx(ctx, "deploy event [%s] PipelineRun [%s] not finalized, skip update changelog status`%s`", r.EventID(), r.Name(), status)
	}
}

func (es *EventSvc) handleSimpleDeployEvent(ctx context.Context, r RunObject) {
	changeLogID := r.GetAnnotation(string(constants.ChangeLogIDKey))
	if changeLogID == "" {
		log.ErrorWithCtx(ctx, "get changelog id form simple deploy event %s failed", r.EventID())
		return
	}
	id, _ := strconv.ParseInt(changeLogID, 10, 64)
	es.sendUpdateChangeLogReq(ctx, id, r)
}

func (es *EventSvc) handleRollbackEvent(ctx context.Context, r RunObject) {
	// get changelog id from PipelineRun name "XXX-XXX-{id}"
	s := strings.Split(r.Name(), "-")
	idS := s[len(s)-1]
	id, err := strconv.ParseInt(idS, 10, 64)
	if err != nil {
		log.Errorf("get changelog id from PipelineRun [%s] failed: %s", r.Name(), err)
		return
	}
	es.sendUpdateChangeLogReq(ctx, id, r)
}

func isPipelineTimeout(pr *dao.PipelineRun) bool {
	createAt := pr.CreatedAt
	timeoutAt := createAt.Add(time.Duration(conf.AppConfig.Retry.TimeoutDays) * 24 * time.Hour)
	return time.Now().After(timeoutAt)
}

// HandleEvent handle Tekton Event.
// error is not nil means the event is not process correctly.
func (es *EventSvc) HandleEvent(ctx context.Context, e *cloudevents.Event) error {
	var err error
	var prId int64

	runObject, err := RunObjectFromEvent(e)
	if err != nil {
		log.Errorf("[HandleEvent] handle Tekton Event[%s] failed: %s", e.ID(), err)
		return err
	}

	ctx = cctx.WithRequestInfo(ctx, &cctx.RequestInfo{RequestID: runObject.RequestID()})
	// filter event
	if !es.isEventProcessable(ctx, runObject) {
		log.InfoWithCtx(ctx, "[HandleEvent] event %s is not processable, skip", runObject.EventID())
		return nil
	}

	eventErr := eventError{eventId: runObject.EventID(), requestId: runObject.RequestID(), tektonName: runObject.Name(), runObjType: "Event"}
	// Rollback PipelineRun handler
	rollbackLabel := runObject.GetAnnotation(string(constants.RollbackFlagKey))
	if rollbackLabel == "true" && runObject.IsPipelineRunEvent() {
		log.InfoWithCtx(ctx, "[HandleEvent] handle rollback pipelinerun %s event", runObject.Name())
		es.handleRollbackEvent(ctx, runObject)
		return nil
	}
	tx := db.DB.Begin()
	txCtx := db.CtxWithTX(ctx, tx)
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[HandleEvent] %s: panic %v: stack %s", eventErr.String(), r, debug.Stack())
			tx.Rollback()
		}
		if err != nil {
			log.Errorf("[HandleEvent] %s: error : %v : rollbacked", eventErr.String(), err)
			tx.Rollback()
		} else {
			log.Infof("[HandleEvent] %s: success : commited", eventErr.String())
			tx.Commit()
		}
	}()
	deployType := runObject.GetAnnotation(string(constants.DeployTypeKey))
	if deployType != "" && constants.DeployType(deployType) == constants.DeployTypeSimple {
		if !runObject.IsPipelineRunEvent() {
			log.InfoWithCtx(ctx, "[HandleEvent] deploy event %s not PipelineRun event, skip", runObject.Name())
			return nil
		}

		log.InfoWithCtx(ctx, "[HandleEvent] handle simple deploy %s event", runObject.Name())
		simpleDeployPipelineRunSubTaskId := runObject.GetAnnotation(string(constants.PipelineRunSubtaskIdKey))
		var pipelineRunSubTaskIdFromTekton int64
		if simpleDeployPipelineRunSubTaskId != "" {
			// 处理短流程 流水线里的部署任务
			pipelineRunSubTaskIdFromTekton, _ = strconv.ParseInt(simpleDeployPipelineRunSubTaskId, 10, 64)
			log.InfoWithCtx(ctx, "[HandleEvent] handle simple deploy event`%q`, subtask id=%d", runObject.Name(), pipelineRunSubTaskIdFromTekton)
			_ = es.handlePreSimpleSubTask(txCtx, runObject, pipelineRunSubTaskIdFromTekton)
		}
		//es.handleSimpleDeployEvent(ctx, runObject)
		es.handleSimpleDeployEventV3(ctx, runObject, pipelineRunSubTaskIdFromTekton)
		return nil
	}

	prId, err = runObject.PipelineRunId()
	if err != nil {
		log.Errorf("%s: get pipeline run id failed: %s", eventErr.String(), err)
		return err
	}
	pr, err := es.pipelineRunRepo.TxFindPipelineRunLocked(txCtx, prId)
	if err != nil || pr == nil {
		log.Errorf("%s: lock pipelinerun failed: %s", eventErr.String(), err)
		return err
	}
	if isPipelineTimeout(pr) {
		log.InfoWithCtx(ctx, "pipelinerun %d is timeout, skip", pr.ID)
		return nil
	}

	log.InfoWithCtx(ctx, "[TektonEvent] pipelineRunId:%d,status:%s,runObjType:%s,tektonName:%s,eventId:%s", pr.ID, runObject.Status().String(),
		runObject.Type(), runObject.Name(), runObject.EventID())

	switch {
	case runObject.IsTaskRunEvent():
		err = es.handleTaskRunEvent(txCtx, runObject, pr)
	case runObject.IsPipelineRunEvent():
		err = es.handlePipelineRunEvent(txCtx, runObject, pr)
	case runObject.IsCustomRunEvent():
		err = es.handleCustomRunEvent(txCtx, runObject, pr)
	default:
		err = fmt.Errorf("%v: unknown run object type", eventErr)
	}

	return err
}

// 相比 handleSimpleDeployEvent，这里的逻辑是不用 RunObject，而是查一遍数据库，使用 dao.PipelineRunSubtask 的状态来处理
// str.status 在上层已经拦截处理过
// 临时这样做吧，没其它好方法
func (es *EventSvc) handleSimpleDeployEventV3(ctx context.Context, r RunObject, strID int64) {
	clIDStr := r.GetAnnotation(string(constants.ChangeLogIDKey))
	if clIDStr == "" {
		log.ErrorWithCtx(ctx, "[handleSimpleDeployEventV3] get changelog id form simple deploy event `%s` failed", r.EventID())
		return
	}

	clID, _ := strconv.ParseInt(clIDStr, 10, 64)
	status := r.Status()
	if !isDeployCompleted(status) {
		log.InfoWithCtx(ctx, "[handleSimpleDeployEventV3] deploy event `%s` PipelineRun`%s` not finalized, skip update changelog status `%s`", r.EventID(), r.Name(), status)
		return
	}

	var updateStatus constants.DeployStatus
	switch status {
	case constants.SUCCESSFUL:
		updateStatus = constants.DeployStatusSuccessful
	case constants.FAILED:
		updateStatus = constants.DeployStatusFailed
	default:
		updateStatus = constants.DeployStatusFailed
	}
	log.InfoWithCtx(ctx, "[handleSimpleDeployEventV3] deploy event`%s` PipelineRun `%s` finalized, will update changelog status to %s, clID=%d", r.EventID(), r.Name(), updateStatus, clID)

	eventErr := eventError{eventId: r.EventID(), requestId: r.RequestID(), tektonName: r.Name(), runObjType: "Event"}
	// 拦截状态，chain 已经做了 cl 处理，这里判断 cl 状态是终态，则不再处理
	if updateStatus == constants.DeployStatusSuccessful && strID != 0 {
		log.InfoWithCtx(ctx, "[handleSimpleDeployEventV3] %s deploy event`%s` PipelineRun`%s` finalized, check cl status, clID=%d", eventErr.String(), r.EventID(), r.Name(), clID)
		cl, err := es.deployClient.FindChangeLogBySubTaskRunID(ctx, &pbdep.SubTaskRunId{Id: strID})
		if err != nil {
			log.ErrorWithCtx(ctx, "[handleSimpleDeployEventV3] %s deploy event`%s` PipelineRun`%s` finalized, get cl failed, err: %v, clID=%d", eventErr.String(), r.EventID(), r.Name(), err, clID)
			return
		}
		if cl != nil && (cl.Status == int32(constants.DeployStatusSuccessful) || cl.Status == int32(constants.DeployStatusFailed)) {
			log.InfoWithCtx(ctx, "[handleSimpleDeployEventV3] %s deploy event`%s` PipelineRun`%s` finalized, skip update changelog status, cl status is %s, clID=%d", eventErr.String(), r.EventID(), r.Name(), constants.DeployStatus(cl.Status), clID)
			return
		}
	}
	// 页面修改配置触发的短流程事件
	if updateStatus == constants.DeployStatusSuccessful && strID == 0 {
		chainReq := &pbdep.DeployChainReq{
			ChangelogId: clID,
		}
		if _, err := es.deployClient.DeployChain(ctx, chainReq); err != nil {
			log.ErrorWithCtx(ctx, "[handleSimpleDeployEventV3] %s failed to call DeployChain, err: %v", eventErr.String(), err)
			updateStatus = constants.DeployStatusFailed
		}
	}

	updateCLReq := &pbdep.UpdateChangeLogReq{
		Id:     clID,
		Status: int32(updateStatus),
	}

	log.InfoWithCtx(ctx, "[handleSimpleDeployEventV3] %s deploy event`%s` PipelineRun`%s` finalized, update changelog status to %s, clID=%d", eventErr.String(), r.EventID(), r.Name(), updateStatus, clID)
	_, err := es.deployClient.UpdateChangeLogStatus(ctx, updateCLReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[handleSimpleDeployEventV3] %s handle deploy event `%s`, update id `%d` status failed %s", eventErr.String(), r.EventID(), clID, err.Error())
	}
}

// 相比 handleSimpleDeployEvent，这里的逻辑是在 runObject 终态时，再次处理部分部署逻辑，且可能更改原始传递的 eventStatus
// 新加函数来处理是因为旧的设计，无法适配新逻辑的需求，需要把 pr task 中执行 argocd 的逻辑，重构为服务代码，才能做真正的适配
func (es *EventSvc) handleSimpleDeployEventV2(ctx context.Context, r RunObject, strID int64) {
	clIDStr := r.GetAnnotation(string(constants.ChangeLogIDKey))
	if clIDStr == "" {
		log.ErrorWithCtx(ctx, "[handleSimpleDeployV2] get changelog id form simple deploy event`%s` failed", r.EventID())
		return
	}

	clID, _ := strconv.ParseInt(clIDStr, 10, 64)
	status := r.Status()
	if !isDeployCompleted(status) {
		log.InfoWithCtx(ctx, "[handleSimpleDeployV2] deploy event `%s` PipelineRun`%s` not finalized, skip update changelog status", r.EventID(), r.Name())
		return
	}

	var updateStatus constants.DeployStatus
	switch status {
	case constants.SUCCESSFUL:
		updateStatus = constants.DeployStatusSuccessful
	case constants.FAILED:
		updateStatus = constants.DeployStatusFailed
	default:
		updateStatus = constants.DeployStatusFailed
	}

	// 执行 deployChain，如果失败，更新 changelog 状态为失败
	if updateStatus == constants.DeployStatusSuccessful {
		prSubtask, err := es.pipelineRunRepo.FindSubTaskById(ctx, strID)
		if err != nil {
			log.ErrorWithCtx(ctx, "[handleSimpleDeployV2] find subtask run failed, err: %v", err)
			return
		}
		rt, err := es.pipelineRunRepo.FindTaskRunWithPreload(ctx, prSubtask.PipelineRunTaskId)
		if err != nil {
			log.ErrorWithCtx(ctx, "[handleSimpleDeployV2] find task run failed, err: %v", err)
			return
		}

		opt := &handleCallDeployChainOption{
			appName:          rt.StageRun.PipelineRun.Pipeline.AppName,
			autoDeployConfig: rt.Config,
			mainTaskID:       rt.ID,
			mainTaskType:     rt.GetType(),
			subTaskID:        strID,
			eventStatus:      r.Status(),
			pipelineRunID:    rt.PipelineRunId,
		}
		// 如果当前触发任务的是子 Task, 就用子 Task 的部署配置
		if strID != 0 && prSubtask != nil {
			log.InfoWithCtx(ctx, "[handleSimpleDeployV2] use sub task config to deploy, appName: %s, mainTaskID=%d, subTaskID=%d", opt.appName, opt.mainTaskID, opt.subTaskID)
			opt.autoDeployConfig = prSubtask.Config
		}

		if err = es.handleCallDeployChain(ctx, opt); err != nil {
			updateStatus = constants.DeployStatusFailed
		}
	}

	updateCLReq := &pbdep.UpdateChangeLogReq{
		Id:     clID,
		Status: int32(updateStatus),
	}

	log.InfoWithCtx(ctx, "[handleSimpleDeployV2] deploy event`%s` PipelineRun`%s` finalized, update changelog status to %s", r.EventID(), r.Name(), updateStatus)
	_, err := es.deployClient.UpdateChangeLogStatus(ctx, updateCLReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[handleSimpleDeployV2] handle deploy event`%s`, update id`%d` status failed %s", r.EventID(), clID, err.Error())
	}
}

func (es *EventSvc) handleCustomRunEvent(ctx context.Context, r RunObject, pr *dao.PipelineRun) error {
	if r.StartedTime().IsZero() {
		log.InfoWithCtx(ctx, "[handleCustomRunEvent] custom run event id:%s name:%s not started, skip", r.EventID(), r.Name())
		return nil
	}
	var err error
	if r.IsSubTaskEvent() {
		err = es.handleSubTaskEvent(ctx, r, pr)
	} else {
		err = es.handleNormalTaskEvent(ctx, r, pr)
	}
	return err
}

func (es *EventSvc) handlePreSimpleSubTask(ctx context.Context, r RunObject, prSubTaskId int64) error {
	prSubTask, err := es.pipelineRunRepo.FindSubTaskById(ctx, prSubTaskId)
	if err != nil || prSubTask == nil {
		log.Errorf("%s: find prSubTask failed: %s", err)
		return err
	}
	pr, err := es.pipelineRunRepo.TxFindPipelineRunLocked(ctx, prSubTask.PipelineRunID)
	if err != nil || pr == nil {
		log.Errorf("%s: lock pipelinerun failed: %s", err)
		return err
	}
	return es.handleSimpleSubTaskEvent(ctx, r, pr, prSubTask.PipelineRunTaskId, prSubTask.ID)
}

func (es *EventSvc) handleSimpleSubTaskEvent(ctx context.Context, r RunObject, pr *dao.PipelineRun, prTaskId int64, prSubTaskId int64) error {
	eventErr := eventError{eventId: r.EventID(), requestId: r.RequestID(), tektonName: r.Name(), runObjId: prSubTaskId, runObjType: "SubTask"}
	log.InfoWithCtx(ctx, "[handleSimpleSubTaskEvent] handleSubTaskRunEvent: %s", eventErr.String())
	tx := db.CtxTX(ctx, nil)
	rt, err := es.pipelineRunRepo.TxFindRunTask(ctx, tx, prTaskId)
	if err != nil || rt == nil {
		return errors.Wrapf(err, "%s: find run task failed", eventErr.Error())
	}
	// select rows from database by mainTaskID
	subTaskSearch := model.SubTaskSearch{
		TaskRunId:   prTaskId,
		EnabledList: []int64{1},
	}
	subTasks, err := es.pipelineRunRepo.FindSubTasksByMultiParams(ctx, subTaskSearch)
	if err != nil {
		return errors.Wrapf(err, "%s: find sub tasks by main task id failed", eventErr.Error())
	}
	// construct subtaskstatemachine
	stateMachine := newSubTaskStateMachine(pr, rt, subTasks, prSubTaskId)
	// update sub task status, result, tekname etc.
	eventStatus := r.Status()
	// 手动停止部署后状态变成失败，不要监听事件了
	if stateMachine.trigger.Status == constants.FAILED {
		log.InfoWithCtx(ctx, "[handleSimpleSubTaskEvent] sub task ID[%d] TektonName[%s] is failed, ignore", stateMachine.trigger.ID, stateMachine.trigger.TektonName)
		return nil
	}
	switch eventStatus {
	case constants.UNHANDLED:
		err = stateMachine.subTaskUnhandle(r)
	case constants.RUNNING:
		err = stateMachine.subTaskRun(r)
	case constants.SUCCESSFUL:
		err = stateMachine.subTaskSuccess(r)
	case constants.FAILED:
		err = stateMachine.subTaskFail(r)
	case constants.CANCEL:
		err = stateMachine.subTaskCancel(r)
	default:
		return fmt.Errorf("sub task status %s is not match", eventStatus)
	}
	if err != nil {
		return errors.Wrapf(err, "%s: subtask state machine failed", &eventErr)
	}

	// 拦截部署任务，如果任务成功，再执行 deployChain；如果 deployChain 执行失败，更新子任务和流水线状态为失败
	if eventStatus == constants.SUCCESSFUL {
		opt := &handleCallDeployChainOption{
			appName:          rt.StageRun.PipelineRun.Pipeline.AppName,
			autoDeployConfig: rt.Config,
			mainTaskID:       rt.ID,
			mainTaskType:     rt.GetType(),
			subTaskID:        prSubTaskId,
			eventStatus:      eventStatus,
			pipelineRunID:    rt.PipelineRunId,
		}
		log.InfoWithCtx(ctx, "[handleSimpleSubTaskEvent] %s start to handleCallDeployChain, opt=%+v", eventErr.String(), *opt)
		// 如果当前触发任务的是子 Task, 就用子 Task 的部署配置
		hasSubtask := prSubTaskId != 0 && stateMachine.trigger != nil
		if hasSubtask {
			log.InfoWithCtx(ctx, "[handleSimpleSubTaskEvent] %s use sub task config to deploy, appName: %s, mainTaskID=%d, subTaskID=%d", eventErr.String(), opt.appName, opt.mainTaskID, opt.subTaskID)
			opt.autoDeployConfig = stateMachine.trigger.Config
		}
		if err = es.handleCallDeployChain(ctx, opt); err != nil {
			log.WarnWithCtx(ctx, "[handleSimpleSubTaskEvent] %s handleCallDeployChain failed, update task and pr status to failed", eventErr.String())
			// 拦截强制改状态
			if hasSubtask {
				stateMachine.trigger.Status = constants.FAILED
			}
			rt.Status = constants.FAILED
			pr.Status = constants.FAILED
		}
	}

	// update task
	log.InfoWithCtx(ctx, "[handleSimpleSubTaskEvent] %s handleSimpleSubTaskEvent update run task to %+v", eventErr.String(), *rt)
	updated, err := es.pipelineRunRepo.TxUpdatePipelineRunTask(ctx, tx, rt)
	if err != nil {
		return errors.Wrapf(err, "%v: sub task update run task failed", eventErr)
	}
	if updated == 0 {
		log.Infof("[handleSimpleSubTaskEvent] %s: handleSimpleSubTaskEvent run task is already updated: %#v", eventErr.String(), rt)
	}
	deliverEvent(ctx, es.eventSender, rt, r)

	// update sub task
	subTask := stateMachine.getCallbackTask()
	log.InfoWithCtx(ctx, "[handleSimpleSubTaskEvent] %s handleSimpleSubTaskEvent update sub run task to %+v", eventErr.String(), *subTask)
	updated, err = es.pipelineRunRepo.TxUpdateTaskRunMultiCloud(ctx, tx, subTask)
	if err != nil {
		return errors.Wrapf(err, "%v: sub task update run task failed", eventErr.Error())
	}
	if updated == 0 {
		log.Infof("[handleSimpleSubTaskEvent] %s: sub task is already updated: %#v", eventErr.String(), rt)
	}
	deliverEvent(ctx, es.eventSender, subTask, r)
	// update changelog
	//es.handleChangeLogStatus(r, rt, subTask)

	// update PipelineRunStage and send MQ event
	err = es.processStageRun(ctx, *rt, rt.PipelineRunStageId, r)
	if err != nil {
		log.Errorf("[handleSimpleSubTaskEvent] %s: process stage run failed: %v", eventErr.String(), err)
		return errors.Wrap(err, "sub task process stage run failed")
	}

	log.InfoWithCtx(ctx, "[handleSimpleSubTaskEvent] %s sub task update pipelinerun to %+v", eventErr.String(), *pr)
	updated, err = es.pipelineRunRepo.TxUpdatePipelineRun(ctx, tx, pr)
	if err != nil {
		return errors.Wrapf(err, "%s: update PipelineRun failed", eventErr.Error())
	}
	if updated == 0 {
		log.Infof("[handleSimpleSubTaskEvent] %s: PipelineRun db update is already finished", eventErr.String())
	}
	deliverEvent(ctx, es.eventSender, pr, r)
	return nil
}

func (es *EventSvc) handleSubTaskEvent(ctx context.Context, r RunObject, pr *dao.PipelineRun) error {
	mainTaskID, err := r.RunTaskId()
	if err != nil {
		return errors.Wrap(err, "sub task get main task id failed")
	}
	subTaskID, err := r.SubTaskId()
	if err != nil {
		return errors.Wrap(err, "sub task get sub task id failed")
	}
	return es.handleSimpleSubTaskEvent(ctx, r, pr, mainTaskID, subTaskID)
}

func (es *EventSvc) handleNormalTaskEvent(ctx context.Context, r RunObject, pr *dao.PipelineRun) error {
	rtId, err := r.RunTaskId()
	eventErr := eventError{eventId: r.EventID(), requestId: r.RequestID(), tektonName: r.Name(), runObjId: rtId, runObjType: "CustomRun"}
	if err != nil {
		return fmt.Errorf("%v: get run task id failed: %v", eventErr, err)
	}
	// update PipelineRunTask and send MQ event
	log.InfoWithCtx(ctx, "[handleNormalTaskEvent] handleCustomRunEvent: %v, eventStatus: %v", eventErr, r.Status())
	rt, err := es.processTaskRun(ctx, rtId, r)
	if err != nil {
		log.Errorf("[handleNormalTaskEvent] %s: process task run failed: %v", eventErr.String(), err)
		return err
	}

	// update PipelineRunStage and send MQ event
	err = es.processStageRun(ctx, *rt, rt.PipelineRunStageId, r)
	if err != nil {
		log.Errorf("[handleNormalTaskEvent] %s: process stage run failed: %v", eventErr.String(), err)
		return err
	}

	// only CustomRun have UNHANDLED status event
	if r.Status() == constants.UNHANDLED && rt.GetType() != constants.TASK_API_AUTOMATION_TEST {
		if rt.Status != r.Status() {
			log.WarnWithCtx(ctx, "[handleNormalTaskEvent] %s: pipelinerun status can't transition from %s to %s", eventErr.String(), rt.Status, r.Status())
			return nil
		}
		log.InfoWithCtx(ctx, "[handleNormalTaskEvent] handleCustomRunEvent UNHUNDLER process pipelinerun: %s", eventErr.String())
		err = es.processPipelineRun(ctx, pr, r)
		if err != nil {
			log.ErrorWithCtx(ctx, "[handleNormalTaskEvent] %s: process pipeline run to unhandled failed: %v", eventErr.String(), err)
			return err
		}
	}
	// update PipelineRun status while status is Running
	if r.Status() == constants.RUNNING {
		log.InfoWithCtx(ctx, "[handleNormalTaskEvent] handleCustomRunEvent RUNNING process pipelinerun: %s", eventErr.String())
		err = es.processPipelineRun(ctx, pr, r)
		if err != nil {
			log.Errorf("[handleNormalTaskEvent] %s: process pipeline run to running failed: %v", eventErr.String(), err)
			return err
		}
	}

	tkType := rt.GetType()
	if r.Status() == constants.SUCCESSFUL && (constants.IsAutomationDeployTask(tkType) || constants.IsImageSyncTask(tkType)) {
		//镜像环境同步也需要创建默认路由
		opt := &handleCallDeployChainOption{
			appName:          rt.StageRun.PipelineRun.Pipeline.AppName,
			autoDeployConfig: rt.Config,
			mainTaskID:       rt.ID,
			mainTaskType:     rt.GetType(),
			subTaskID:        0,
			eventStatus:      r.Status(),
			pipelineRunID:    rt.PipelineRunId,
		}
		if err = es.handleCallDeployChain(ctx, opt); err != nil {
			log.ErrorWithCtx(ctx, "[handleNormalTaskEvent] %s failed to handleCallDeployChain, err: %v, taskRunID: %d", eventErr.String(), err, rt.ID)
			// 不返回 err，不然事件存储状态会被回滚，导致状态异常
			// 这里不是短流程，不需要拦截状态，调用失败不管后续；但在 chain 里会更新 changelog 状态，所以要再更新一次，让 changelog 状态为成功
			updateCLReq := &pbdep.UpdateChangeLogReq{
				TaskRunID: rt.ID,
				Status:    int32(constants.DeployStatusSuccessful),
				SubtaskId: 0,
			}
			_, _ = es.deployClient.UpdateChangeLogStatus(ctx, updateCLReq)
		}
	}

	return nil
}

func (es *EventSvc) handleCallDeployChain(ctx context.Context, opt *handleCallDeployChainOption) error {
	if opt.eventStatus == constants.SUCCESSFUL && opt.mainTaskType.IsDeployTask() || constants.IsImageSyncTask(opt.mainTaskType) {
		log.DebugWithCtx(ctx, "[handleCallDeployChain] start to call DeployChain, mainTaskID=%d, subTaskID=%d, eventStatus=%s, taskType=%s", opt.mainTaskID, opt.subTaskID, opt.eventStatus, opt.mainTaskType)
		var autoCfg model.AutomationDeploy
		if err := json.Unmarshal(opt.autoDeployConfig, &autoCfg); err != nil {
			log.ErrorWithCtx(ctx, "[handleCallDeployChain] failed to Unmarshal automationDeploy, err: %v, mainTaskID=%d, subTaskID=%d", err, opt.mainTaskID, opt.subTaskID)
			return errors.WithMessage(err, "failed to Unmarshal automationDeploy")
		}
		if constants.IsImageSyncTask(opt.mainTaskType) {
			// 镜像环境同步也需要创建默认路由
			autoCfg.EnvTarget = "origin"
		}
		chainReq := &pbdep.DeployChainReq{
			AppName:       opt.appName,
			Cluster:       autoCfg.Cluster,
			Namespace:     autoCfg.Namespace,
			SubNamespace:  autoCfg.SubNamespace,
			Senv:          autoCfg.Senv,
			EnvTarget:     autoCfg.EnvTarget,
			TrafficMark:   autoCfg.TrafficMark,
			ConfigId:      autoCfg.ConfigId,
			TaskRunId:     opt.mainTaskID,
			SubtaskRunId:  opt.subTaskID,
			PipelineRunId: opt.pipelineRunID,
		}
		// 执行链路有点长，可能会超时，先直接用新 ctx
		var cancelFnc context.CancelFunc
		ctx, cancelFnc = cctx.CopyCtxWithTimeout(ctx, 5*time.Minute) // 5分钟啥都干完了
		defer cancelFnc()
		if _, err := es.deployClient.DeployChain(ctx, chainReq); err != nil {
			log.ErrorWithCtx(ctx, "[handleCallDeployChain] failed to call DeployChain, err: %v, mainTaskID=%d, subTaskID=%d", err, opt.mainTaskID, opt.subTaskID)
			return errors.WithMessage(err, "failed to call DeployChain")
		}
		return nil
	}
	log.InfoWithCtx(ctx, "[handleCallDeployChain] skip call DeployChain, the event is not target event, prType=%q, eventStatus=%q", opt.mainTaskType, opt.eventStatus)
	return nil
}

func needCallHandleBuildSubenvRouteCompleteProcess(a *model.AutomationDeploy) bool {
	// 非生产环境
	notProdEnv := a.DeployEnv != "" && constants.EnvType(a.DeployEnv) != constants.PRODUCTION
	// 非基准环境
	notOriginEnv := constants.EnvTargetType(a.EnvTarget) != constants.ORIGIN
	// 非生产环境，非基准环境，触发子环境添加服务路由配置
	return notProdEnv && notOriginEnv
}

func (es *EventSvc) handleTaskRunEvent(ctx context.Context, r RunObject, pr *dao.PipelineRun) error {
	rtId, err := r.RunTaskId()
	eventErr := eventError{eventId: r.EventID(), requestId: r.RequestID(), tektonName: r.Name(), runObjId: rtId, runObjType: "TaskRun"}
	if err != nil {
		return fmt.Errorf("%v: get run task id failed: %v", eventErr, err)
	}

	// update PipelineRunTask and send MQ event
	log.InfoWithCtx(ctx, "handleTaskRunEvent: %s, status: %v", eventErr.String(), r.Status())
	rt, err := es.processTaskRun(ctx, rtId, r)
	if err != nil {
		log.Errorf("%s: process task run failed: %v", eventErr.String(), err)
		return err
	}

	// 无奈的补丁，更新内存里的 task rst
	if rt.Type == string(constants.TASK_GENERATE_PUSH_IMAGE) && len(rt.Result) > 0 {
	twoLoop:
		for i := range pr.Stages {
			for j := range pr.Stages[i].Tasks {
				if pr.Stages[i].Tasks[j].ID == rt.ID {
					pr.Stages[i].Tasks[j].Result = rt.Result
					break twoLoop
				}
			}
		}
	}

	// update PipelineRunStage and send MQ event
	err = es.processStageRun(ctx, *rt, rt.PipelineRunStageId, r)
	if err != nil {
		log.Errorf("%s: process stage run failed: %v", eventErr.String(), err)
		return err
	}
	createCDPipelineRun := func(rt *dao.PipelineRunTask) error {
		firstPipelineRun, e := es.pipelineRunRepo.FindFirstPipelineRunBy(ctx, pr.PipelineId, pr.BuildNumber)
		if e != nil {
			log.Errorf("%s: find first pipeline run failed: %v", eventErr.String(), e)
		}

		if pr.IsChangeSetDelegate() {
			log.InfoWithCtx(ctx, "%s: is changeSet delegated pipeline, will not create CD pipelinerun here.", eventErr.String())
			return nil
		}
		runner := cdrunner.New(rt, pr, false, timex.ToYearMonth(firstPipelineRun.CreatedAt))
		tpr, err := es.tektonClient.CreateCDPipelineRun(ctx, runner)
		if err != nil {
			log.Errorf("%s: create CD PipelineRun failed: %v", eventErr.String(), err)
			return err
		} else {
			log.InfoWithCtx(ctx, "create CD PipelineRun[%s] for pipelinerun[%d]", tpr.Name, pr.ID)
		}
		return nil
	}
	// handle CD pipeline
	if r.IsSuccessEvent() {
		if rt.StageRun.IsParallelStage() && IsLastCIStage(*rt, *pr, conf.GetCiStagesConfig()) {
			// 并行流水线ci阶段，需要遍历所有任务成功，才能触发cd阶段
			taskRuns, err := es.pipelineRunRepo.QueryPipelineRunTasksByMultiParams(ctx, &model.PipelineRunTaskSearch{StageRunId: rt.StageRun.ID})
			if err == nil {
				var ignoreTaskRuns []dao.PipelineRunTask
				for _, taskRun := range taskRuns {
					if taskRun.ID == rt.ID {
						continue
					}
					ignoreTaskRuns = append(ignoreTaskRuns, taskRun)
				}
				if isTaskRunAllSuccessOrSkipped(ignoreTaskRuns) && !isEndStageOfPipelineRun(rt.StageRun, pr) {
					tx := db.CtxTX(ctx, nil)
					lastRt, err := es.pipelineRunRepo.TxFindRunTask(ctx, tx, taskRuns[len(taskRuns)-1].ID)
					if err == nil && lastRt != nil {
						return createCDPipelineRun(lastRt)
					} else {
						log.ErrorWithCtx(ctx, "createCDPipelineRun find last taskId %d run failed: %v", taskRuns[len(taskRuns)-1].ID, err)
					}
				}
			}
		} else if IsLastCITask(*rt, *pr, conf.GetCiStagesConfig()) && !isEndStageOfPipelineRun(rt.StageRun, pr) {
			return createCDPipelineRun(rt)
		}
	}
	return nil
}

func (es *EventSvc) handlePipelineRunEvent(ctx context.Context, r RunObject, pr *dao.PipelineRun) error {
	// 忽略其他状态的事件
	if r.Status() == constants.UNKNOWN {
		log.Infof("[handlePipelineRunEvent] event [%s] tektonName [%s]: pass UNKNOWN（PipelineRunStopping） status event", r.EventID(), r.Name())
		return nil
	}
	// 手动停止部署后状态变成失败，不要监听事件了
	eventErr := eventError{eventId: r.EventID(), requestId: r.RequestID(), tektonName: r.Name(), runObjId: pr.ID, runObjType: "PipelineRun"}
	if pr.Status == constants.FAILED {
		log.InfoWithCtx(ctx, "[handlePipelineRunEvent] pipelinerun ID[%d] is failed, ignore", pr.ID)
		return fmt.Errorf("pipelinerun ID[%d] is failed, ignore, eventErr:%v", pr.ID, eventErr)
	}

	// only CI stage pipelinerun success event should be handled
	if strings.Contains(r.Name(), "-ci-") && r.IsSuccessEvent() && !IsOnlyCIPipelinerun(pr, conf.GetCiStagesConfig()) {
		log.Infof("[handlePipelineRunEvent] event [%s] tektonName [%s]: pass CI PipelineRun success event", r.EventID(), r.Name())
		return nil
	}
	if strings.Contains(r.Name(), "-cd-") && r.IsRunningEvent() {
		log.Infof("[handlePipelineRunEvent] event [%s] tektonName [%s]: pass CD PipelineRun running event", r.EventID(), r.Name())
		return nil
	}
	log.InfoWithCtx(ctx, "[handlePipelineRunEvent] handlePipelineRunEvent: %s", eventErr.String())
	return es.processPipelineRun(ctx, pr, r)
}

func (es *EventSvc) processPipelineRun(ctx context.Context, pr *dao.PipelineRun, r RunObject) error {
	eventErr := eventError{eventId: r.EventID(), requestId: r.RequestID(), tektonName: r.Name(), runObjId: pr.ID, runObjType: "PipelineRun"}
	if pr.Status.IsFinished() {
		log.WarnWithCtx(ctx, "[processPipelineRun] %s: pipelinerun status %s is finished, skip", eventErr.String(), pr.Status)
		return fmt.Errorf("%v: pipelinerun finished", eventErr)
	}
	tx := db.CtxTX(ctx, nil)
	originStatus := pr.Status
	if r.IsPipelineRunEvent() {
		pr.TektonName = r.Name()
		pr.TektonNamespace = r.Namespace()
		pr.Status = r.Status()
		pr.StartedTime = r.StartedTime()
		pr.CompletedTime = r.CompletedTime()
		workspaces, err := r.Workspaces()
		if err != nil {
			return fmt.Errorf("%v: parse workspace: %v", eventErr, err)
		}
		pr.Workspaces = workspaces

		// CD 阶段 PipelineRun 的 Started 事件，不能更新 Status 字段 (因为CD 第一个任务可能会将流水线更新为待处理状态)
		if strings.Contains(r.Name(), "-cd-") && r.IsStartedEvent() {
			pr.Status = originStatus
		}
	} else if r.IsCustomRunEvent() {
		pr.Status = r.Status()
	} else {
		return fmt.Errorf("%v: run object type can't process pipelinrun", eventErr)
	}

	log.InfoWithCtx(ctx, "[processPipelineRun] %s update pipelinerun to %v", eventErr.String(), *pr)
	updated, err := es.pipelineRunRepo.TxUpdatePipelineRun(ctx, tx, pr)
	if err != nil {
		return fmt.Errorf("%v: update PipelineRun failed: %v", eventErr, err)
	}
	if updated == 0 {
		log.InfoWithCtx(ctx, "[processPipelineRun] %s: PipelineRun db update is already finished", eventErr.String())
	}
	deliverEvent(ctx, es.eventSender, pr, r)
	return nil
}

func resultWithLogString(result []byte, logs string) ([]byte, error) {
	var tmp map[string]any
	if result != nil {
		if err := json.Unmarshal(result, &tmp); err != nil {
			log.Errorf("unmarshal result to a map failed: %v", err)
			return nil, errors.Wrap(err, "unmarshal result to a map failed")
		}
	} else {
		tmp = make(map[string]any, 0)
	}

	tmp["message"] = logs
	newResult, err := json.Marshal(tmp)
	if err != nil {
		log.Errorf("marshal result with log failed: %v", err)
		return nil, errors.Wrap(err, "marshal result with log failed")
	}
	return newResult, nil
}

func (es *EventSvc) processTaskRun(ctx context.Context, rtId int64, r RunObject) (*dao.PipelineRunTask, error) {
	eventErr := &eventError{eventId: r.EventID(), requestId: r.RequestID(), tektonName: r.Name(), runObjId: rtId, runObjType: "TaskRun"}
	tx := db.CtxTX(ctx, nil)
	rt, err := es.pipelineRunRepo.TxFindRunTask(ctx, tx, rtId)
	if err != nil || rt == nil {
		return nil, fmt.Errorf("%v: find run task failed: %v", eventErr, err)
	}
	// 手动停止部署后状态变成失败，不要监听事件了
	if rt.Status == constants.FAILED && rt.GetType().IsDeployTask() {
		log.InfoWithCtx(ctx, "[processTaskRun] pipelinerun-task ID[%d] TektonName[%s] is failed, ignore", rt.ID, rt.TektonName)
		return nil, fmt.Errorf("pipelinerun-task ID[%d] TektonName[%s] is failed, ignore, eventErr:%v", rt.ID, rt.TektonName, eventErr)
	}
	if rt.Status.IsFinished() && r.Status() != constants.CANCEL {
		log.WarnWithCtx(ctx, "[processTaskRun] %s: run task status %s is finished, skip", eventErr.String(), rt.Status)
		return nil, fmt.Errorf("%v: run task finished", eventErr)
	}
	if r.Status() == constants.SKIPPED {
		log.WarnWithCtx(ctx, "[processTaskRun] %s: run task status %s is skipped, skip", eventErr.String(), rt.Status)
		return nil, fmt.Errorf("%v: run task skipped", eventErr)
	}

	// 状态值不可回退，但是其他字段可以更新。（工单会先更新为 running 状态，回调再发送 waiting 事件）
	eventStatus := r.Status()
	updateStatus := eventStatus
	log.InfoWithCtx(ctx, "[processTaskRun] %s: taskRun status: %s, eventStatus: %s", eventErr.String(), rt.Status, eventStatus)
	if !rt.Status.CanTransition(eventStatus) {
		log.WarnWithCtx(ctx, "[processTaskRun] %s: run task status can't transition from %s to %s , skip", eventErr.String(), rt.Status, eventStatus)
		updateStatus = rt.Status
	}
	rt.TektonName = r.Name()
	rt.TektonNamespace = r.Namespace()
	rt.Status = updateStatus
	rt.CompletedTime = r.CompletedTime()
	rt.PodName = r.PodName()
	if rt.Status == constants.SKIPPED {
		rt.StartedTime = time.Time{}
	} else {
		rt.StartedTime = r.StartedTime()
	}
	var result []byte
	if (rt.GetType() == constants.TASK_SONAR_SCAN || rt.GetType() == constants.TASK_SCA_SCAN) && r.IsFailedEvent() {
		log.InfoWithCtx(ctx, "[processTaskRun] event [%s] tektonName [%s] handle SonarScan task failed event", r.EventID(), r.Name())
		result, err = r.MessageResult()
	} else {
		result, err = r.Result()
	}
	if err != nil {
		return nil, fmt.Errorf("%v: parse result: %v ", eventErr, err)
	}
	if rt.GetType() == constants.TASK_API_AUTOMATION_TEST {
		// 旧自动化测试任务做特殊处理
		isOld, err := isAutoTestingTask(ctx, r)
		if err != nil {
			return nil, errors.WithMessagef(err, "%v: get auto testing task failed", eventErr)
		}
		if isOld {
			if rt.Status == constants.UNHANDLED {
				log.InfoWithCtx(ctx, "[processTaskRun] event [%s] tektonName [%s] ApiAutomationTest task update to Running", r.EventID(), r.Name())
				rt.Status = constants.RUNNING
				if rt.StartedTime.IsZero() {
					rt.StartedTime = time.Now()
				}
			}
			if rt.Status == constants.SUCCESSFUL || rt.Status == constants.FAILED {
				logs, err := r.FetchLog()
				if err != nil {
					return nil, errors.WithMessagef(err, "%v: fetch log failed", eventErr)
				}
				result, err = resultWithLogString(result, logs)
				if err != nil {
					return nil, err
				}
			}
		} else {
			// 新的自动化测试任务直接取数据库的结果
			result = rt.Result
		}
	}

	// 暂停任务的RST ，从数据库捞取
	if rt.GetType() == constants.TASK_PAUSE {
		result = rt.Result
	}

	rt.Result = result
	log.InfoWithCtx(ctx, "[processTaskRun] %s update run task to %v", eventErr, *rt)
	updated, err := es.pipelineRunRepo.TxUpdatePipelineRunTask(ctx, tx, rt)
	if err != nil {
		return nil, fmt.Errorf("%v: update run task failed: %v", eventErr, err)
	}
	if updated == 0 {
		log.InfoWithCtx(ctx, "[processTaskRun] %s: run task is already updated: %#v", eventErr.String(), rt)
	}
	// 终止的customrun 需要更新子任务表状态、时间等
	if r.Status() == constants.CANCEL && rt.GetType().IsDeployTask() && len(rt.SubRunTasks) > 0 {
		_ = es.handleCustomRunSubTask(ctx, tx, r, rt)
	}
	// TODO: use event instead of rpc call to update change log
	// update deploy status to changeLog
	es.handleChangeLogStatus(ctx, r, rt, nil)

	if rt.GetType() == constants.TASK_OFFLINE_CANARY && rt.Status == constants.SKIPPED {
		return rt, nil
	}

	deliverEvent(ctx, es.eventSender, rt, r)
	return rt, nil
}

func (es *EventSvc) handleCustomRunSubTask(ctx context.Context, tx *gorm.DB, r RunObject, pTaskRun *dao.PipelineRunTask) error {
	for _, subTaskRun := range pTaskRun.SubRunTasks {
		// 运行中的任务，终止后需要变成失败
		// 成功的任务，不变化状态
		if subTaskRun.Status == constants.RUNNING {
			subTaskRun.Status = constants.FAILED
		} else if subTaskRun.Status == constants.SUCCESSFUL {
			subTaskRun.Status = constants.SUCCESSFUL
		} else {
			subTaskRun.Status = r.Status()
		}
		subTaskRun.StartedTime, subTaskRun.CompletedTime = r.StartedTime(), r.CompletedTime()
		_, err := es.pipelineRunRepo.TxUpdateTaskRunMultiCloud(ctx, tx, &subTaskRun)
		if err != nil {
			log.ErrorWithCtx(ctx, "update task run status failed withError: %v", err)
			return err
		}
	}
	return nil
}

func (es *EventSvc) processStageRun(ctx context.Context, runTask dao.PipelineRunTask, runStageId int64, r RunObject) error {
	eventErr := &eventError{eventId: r.EventID(), requestId: r.RequestID(), tektonName: r.Name(), runObjId: runStageId, runObjType: "StageRun"}
	tx := db.CtxTX(ctx, nil)
	rs, err := es.pipelineRunRepo.TxFindRunStage(ctx, tx, runStageId)
	if err != nil || rs == nil {
		return fmt.Errorf("%v: find run stage failed: %v", eventErr, err)
	}
	//if rs.Status.IsFinished() && !r.IsSubTaskEvent() {
	if rs.Status.IsFinished() {
		// 子任务有重试，需要在终态后继续更新
		log.Warnf("%s: run stage status %s is finished, skip", eventErr.String(), rs.Status)
		return nil
	}
	if rs.StartedTime.IsZero() && rs.IsFirstTask(runTask.ID) {
		rs.StartedTime = r.StartedTime()
	}
	if r.Status().IsFinished() {
		if rs.IsParallelStage() {
			// 并行阶段
			//所有任务完成后才更新完成时间，只要有一个任务失败或者取消，阶段就失败，更新完成时间
			if r.Status().IsSuccess() {
				taskRuns, err := es.pipelineRunRepo.QueryPipelineRunTasksByMultiParams(ctx, &model.PipelineRunTaskSearch{StageRunId: runStageId, IgnorePipelineRunTaskId: runTask.ID})
				if err == nil && isTaskRunAllFinished(taskRuns) {
					rs.CompletedTime = r.CompletedTime()
				}
			} else {
				rs.CompletedTime = r.CompletedTime()
			}
		} else if rs.IsLastTask(runTask.ID) {
			rs.CompletedTime = r.CompletedTime()
		}
	}
	eventStatus := r.Status()
	currentStatus := rs.Status
	// update stage status by task status
	switch eventStatus {
	case constants.PENDING:
		log.Debugf("%s: ignore pending status", eventErr.String())
	case constants.SUCCESSFUL:
		taskRuns, err := es.pipelineRunRepo.QueryPipelineRunTasksByMultiParams(ctx, &model.PipelineRunTaskSearch{StageRunId: runStageId, IgnorePipelineRunTaskId: runTask.ID})
		if err == nil && isTaskRunAllSuccessOrSkipped(taskRuns) {
			rs.Status = constants.SUCCESSFUL
		}
	case constants.UNHANDLED:
		if runTask.Type == string(constants.TASK_API_AUTOMATION_TEST) {
			// 旧自动化测试任务做特殊处理
			isOld, err := isAutoTestingTask(ctx, r)
			if err != nil {
				return errors.WithMessagef(err, "%v: get auto testing task failed", eventErr)
			}
			if isOld {
				eventStatus = constants.RUNNING
				rs.Status = constants.RUNNING
			} else {
				rs.Status = constants.UNHANDLED
			}
		} else {
			rs.Status = constants.UNHANDLED
		}
	default:
		// RUNNING, FAILED, CANCEL
		rs.Status = eventStatus
	}
	// 失败状态不修改，其余状态值不可回退，但是其他字段可以更新。（工单会先更新为 running 状态，回调再发送 waiting 事件）
	if runTask.Status == constants.FAILED {
		log.WarnWithCtx(ctx, "%s: fail run stage status can't transition from %s to %s , skip", eventErr.String(), runTask.Status, eventStatus)
		rs.Status = constants.FAILED
	} else if runTask.Status != eventStatus {
		log.WarnWithCtx(ctx, "%s: run stage status can't transition from %s to %s , skip", eventErr.String(), runTask.Status, eventStatus)
		rs.Status = currentStatus
	}
	log.InfoWithCtx(ctx, "%s update run stage to %v", eventErr.String(), *rs)
	updated, err := es.pipelineRunRepo.TxUpdatePipelineRunStage(ctx, tx, rs)
	if err != nil {
		return errors.Wrapf(err, "%v: update run stage failed", eventErr)
	}
	if updated == 0 {
		log.Infof("%s: run stage is already updated: %#v", eventErr.String(), rs)
	}
	deliverEvent(ctx, es.eventSender, rs, r)
	return nil
}

const (
	typeKey         = "type"
	typeAutoTesting = "autoTesting" // 自动化测试
	typeFakeDeploy  = "fakeDeploy"  // 带有子任务的短流程部署任务
)

func getCustomRunType(ctx context.Context, r RunObject) (string, error) {
	specBytes, err := r.Spec()
	if err != nil {
		log.ErrorWithCtx(ctx, "Get Spec failed: %v", err)
		return "", err
	}

	var spec v1beta1.CustomRunSpec
	if err := json.Unmarshal(specBytes, &spec); err != nil {
		log.ErrorWithCtx(ctx, "Unmarshal spec failed: %v", err)
		return "", err
	}

	crType := spec.GetParam(typeKey)
	if crType == nil || crType.Value.StringVal == "" {
		msg := fmt.Sprintf("The %s param was not passed", typeKey)
		log.ErrorWithCtx(ctx, msg)
		return "", errors.New(msg)
	}
	return crType.Value.StringVal, nil
}

func isAutoTestingTask(ctx context.Context, r RunObject) (bool, error) {
	// 根据spec.params.type 判断是否 autoTesting 类型任务

	typeString, err := getCustomRunType(ctx, r)
	if err != nil {
		return false, err
	}
	return typeString == typeAutoTesting, nil
}

func isFakedeploy(ctx context.Context, r RunObject) (bool, error) {
	// 根据spec.params.type 判断是否 fakeDeploy 类型任务

	typeString, err := getCustomRunType(ctx, r)
	if err != nil {
		return false, err
	}
	return typeString == typeFakeDeploy, nil
}

func isTaskRunAllSuccessOrSkipped(taskRuns []dao.PipelineRunTask) bool {
	for _, taskRun := range taskRuns {
		if taskRun.Status != constants.SUCCESSFUL && taskRun.Status != constants.SKIPPED {
			return false
		}
	}
	return true
}

func isTaskRunAllFinished(taskRuns []dao.PipelineRunTask) bool {
	if len(taskRuns) == 0 {
		return true
	}
	for _, taskRun := range taskRuns {
		if taskRun.Status != constants.SUCCESSFUL && taskRun.Status != constants.FAILED && taskRun.Status != constants.CANCEL {
			return false
		}
	}
	return true
}
