[default.app]
name = "mcp-service"
version = 0.1
http.port = 8101
rpc.port = 9101
http.timeout = "0s"
mode = "debug"
description = ""

[default.database]
max_life_time = "30m"
idle_connection = 30
open_connection = 20
parse_time = true
host = "************"
port = 3306
username = "rd_dev"
password = "vRcfj3W#2nGdBeu@"
dbname = "notify"


[default.logger]
logname = "/tmp/mcp.log"
loglevel = "debug"
max_size = 50
max_age = 30
max_backups = 10
compress = false

[default.registry]
url = "127.0.0.1:9000"
app_rpc_url = "127.0.0.1:9001"
deploy_rpc_url = "127.0.0.1:9002"
iam_rpc_url = "*************:9000"
notify_rpc_url = "127.0.0.1:9004"
pipeline_rpc_url ="127.0.0.1:9005"
tools_rpc_url = "127.0.0.1:9009"

[default.redis]
addr = "**************:6379"
db = 11

[mcp]
baseURL ="http://127.0.0.1:8101"

[cloud]
host = "https://alpha-cloud.ttyuyin.com/api/v1/openapi"
token = "Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663"
grpc_target = "alpha-cloud.ttyuyin.com:8100"
deploy_grpc_target = "alpha-cloud.ttyuyin.com:8107"

