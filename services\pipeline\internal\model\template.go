package model

import (
	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/page"
)

type TemplateQuery struct {
	page.PageModel
	TemplateType string   `form:"templateType" json:"templateType"`
	ProjectID    int64    `form:"projectId" json:"projectId"`
	Roles        []string `form:"roles" json:"roles"`
	Language     string   `form:"language" json:"language"`
	Types        string   `form:"types" json:"types"`
	Name         string   `form:"name" json:"name"`
}

type Template struct {
	ID        int64                  `json:"id,omitempty" uri:"id"`
	Name      string                 `json:"name" validate:"required"`
	Type      constants.TemplateType `json:"type"`
	Language  string                 `json:"language" validate:"required"`
	ProjectId int64                  `json:"projectId"`
	UserId    int64                  `json:"userId"`
	Stages    []Stage                `json:"stages" validate:"required,gt=1,dive"`
}

type PipelineTemplate struct {
	ID       int64  `json:"id,omitempty"`
	Name     string `json:"name" validate:"required"`
	Language string `json:"language"`
}

type Stage struct {
	ID    int64  `json:"id,omitempty"`
	Name  string `json:"name" validate:"required"`
	Type  string `json:"type" validate:"required,oneof=COMPILE_BUILD AUTOMATION_TEST PARALLEL SECURITY_SCAN STATIC_CODE_SCAN ARTIFACT_MANAGEMENT DEPLOY_DEV_ENV DEPLOY_TEST_ENV BUSINESS_AUTOMATION_TEST TEST_ACCEPTANCE DEPLOY_GRAY_ENV DEPLOY_PROD_ENV ENV_IMAGE_SYNC"`
	Tasks []Task `json:"tasks" validate:"required,gte=1,dive"`
}

type Task struct {
	ID     int64          `json:"id,omitempty"`
	Name   string         `json:"name,omitempty" validate:"required"`
	Type   string         `json:"type" validate:"required,oneof=PULL_CODE AUTOMATION_COMPILE CUSTOM_SHELL UNIT_TEST SCA_SCAN SONAR_SCAN CHECKSTYLE GENERATE_PUSH_IMAGE AUTOMATION_DEPLOY API_AUTOMATION_TEST TEST_ACCEPTANCE TEST_APPROVAL GRAY_UPGRADE_APPROVAL UPGRADE_APPROVAL TEST_ENV_IMAGE_SYNC DEV_ENV_IMAGE_SYNC AUTOMATION_DEPLOY_SENV DEPLOY_CANARY DEPLOY_STAGING DEPLOY_ORIGIN DEPLOY_SUB OFFLINE_CANARY PAUSE_TASK"`
	Config map[string]any `json:"config"`
}

type RelatedPipelines struct {
	Number int      `json:"number"` //流水线条数
	Names  []string `json:"names"`  //流水线名
}

type TestProjectResp struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Data    TestProjectData `json:"data"`
}

type TestProjectData struct {
	Items []TestProjectItem `json:"items"`
}

type TestProjectItem struct {
	ProjectID   string `json:"projectId" mapstructure:"project_id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

type TestPlanResp struct {
	Code    int          `json:"code"`
	Message string       `json:"message"`
	Data    TestPlanData `json:"data"`
}

type TestPlanData struct {
	Items []TestPlanItem `json:"items"`
}

type TestPlanItem struct {
	PlanId      string `json:"planId" mapstructure:"plan_id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

func (t *Task) IsApprovalTask() bool {
	return t.Type == constants.TASK_TEST_APPROVAL.String() || t.Type == constants.TASK_UPGRADE_APPROVAL.String() || t.Type == constants.TASK_GRAY_UPGRADE_APPROVAL.String()
}

type TplLanguageReq struct {
	ProjectID int64 `form:"projectId"`
}
