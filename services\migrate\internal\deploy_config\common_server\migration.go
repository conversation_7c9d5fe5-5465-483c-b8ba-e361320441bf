package common_server

import (
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/protocol/deploy"
	"52tt.com/cicd/services/migrate/internal/app"
	"52tt.com/cicd/services/migrate/internal/deploy_config"
	"52tt.com/cicd/services/migrate/internal/deploy_config/common"
	db "52tt.com/cicd/services/migrate/pkg/database"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
)

// common-server chart包配置导出

func ExportAllConfig(envs []string, services []string, targetProject string, isUpdate bool) error {
	for _, env := range envs {
		err := ExportConfig(env, services, targetProject, isUpdate)
		if err != nil {
			return err
		}
	}
	return nil
}

// common-server name 使用服务名，非service

func ExportConfig(env string, services []string, targetProjectName string, isUpdate bool) error {
	k8sMap := map[string][]int{
		db.Dev:  {34, 36, 53},
		db.Test: {95},
		db.Prod: {130, 191, 192},
	}
	deployConfigs, err := deploy_config.GetProjectDeployConfig(env, k8sMap)
	if err != nil {
		return err
	}
	var configs []deploy_config.UnitDeployConfig
	for _, dc := range deployConfigs {
		if dc.Unit.App.Team.Name != "数据服务组" || dc.Unit.Status != "online" {
			continue
		}
		// exportServices
		if !isContainUnit(dc.Unit.Name, services) && len(services) > 0 {
			continue
		}
		configs = append(configs, deploy_config.UnitDeployConfig{
			Resource:       dc.Resource,
			Values:         dc.Values,
			Default:        dc.Default,
			K8sName:        dc.K8sEnv.AssetsK8sCluster.Name,
			NameSpace:      dc.K8sEnv.NameSpace,
			K8sDescription: dc.K8sEnv.AssetsK8sCluster.Description,
			K8sPrivateId:   dc.K8sEnv.AssetsK8sCluster.PrivateId,
			UnitName:       dc.Unit.Name,
			ObjectId:       dc.Unit.ObjectId,
			TeamName:       dc.Unit.App.Team.Name,
			ChartName:      dc.Chart.Name,
			ChartUrl:       dc.Chart.Url,
			ChartVersion:   dc.Chart.Version,
			ChartType:      dc.Chart.CharType,
			UnitId:         dc.UnitId,
			K8sEnvId:       dc.K8sEnvId,
		})
	}
	if len(configs) == 0 {
		return nil
	}
	//err = deploy_config.CreateConfig(configs)
	err = PullConfig(configs, targetProjectName, isUpdate)
	if err != nil {
		return err
	}

	return nil
}

func PullConfig(configs []deploy_config.UnitDeployConfig, targetProjectName string, isUpdate bool) error {
	dbEnv := db.Prod // 同步环境
	//dbEnv := db.Test

	hpaMap := make(map[string]HpaChart)
	for _, conf := range configs {
		var tempHpa HpaChart
		if conf.Resource == "HPA" {
			json.Unmarshal(conf.Values, &tempHpa)
			log.Debugf("unit name: %v", conf.UnitName)
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			hpaMap[unitKey] = tempHpa
		}
	}
	log.Debugf("hpaMap: %v", hpaMap)

	count := 0
	var successApps []string
	for _, conf := range configs {
		if conf.ChartName == "common-server" {
			var err error
			// 处理数据导入转换逻辑
			log.Debugf("unitId: %v, unitName: %v", conf.UnitId, conf.UnitName)
			var valueConfig WorkloadChart
			_ = json.Unmarshal(conf.Default, &valueConfig)
			_ = json.Unmarshal(conf.Values, &valueConfig)
			if (valueConfig.Service.Enabled && valueConfig.Service.Type == "ClusterIP") || !valueConfig.Service.Enabled {
				continue
			}
			log.Infof("[common-server] unitName: %v, serviceType: %v", conf.UnitName, valueConfig.Service.Type)

			appBaseConfig, err := HandAppBaseConfig(valueConfig)
			if err != nil {
				return err
			}
			appAdvancedConfig, err := HandAppAdvancedConfig(valueConfig)
			if err != nil {
				return err
			}
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			traitConfig, err := HandTraitConfig(valueConfig, unitKey, hpaMap)
			if err != nil {
				return err
			}

			// 数据库操作：查询app、查询/创建deploy_metadata、创建deploy_config、更新app
			// 1.查询app
			newTeamName := conf.TeamName
			if targetProjectName != "" {
				newTeamName = targetProjectName
			}
			appId, err := common.FindAppIdBy(dbEnv, newTeamName, conf.UnitName)
			if err != nil {
				return err
			}
			if appId == 0 {
				log.Debugf("app name: %v is not exist", conf.UnitName)
				continue
			}

			// 2.查询/创建deploy_metadata
			metadata := deploy_config.DeployMetadata{
				Env:       getEnvEnum(conf.K8sName),
				EnvTarget: 1, //默认基准环境
				Cluster:   getClusterName(conf.K8sName),
				Namespace: conf.NameSpace,
				AppID:     appId,
				AppName:   conf.UnitName,
				ConfigID:  0,
			}
			err = deploy_config.GetDeployConfigLastVersion(dbEnv, &metadata)
			if err != nil {
				return err
			}
			if metadata.Config != nil && metadata.Config.CreatedBy != 71 {
				log.Infof("has existed config, unitId: %v, unitName: %v, createdBy: %v", conf.UnitId, conf.UnitName, metadata.Config.CreatedByChineseName)
			}
			// 只验证数据转换是否可行，不更新
			if !isUpdate {
				log.Infof("ignore update, unitId: %v, unitName: %v", conf.UnitId, conf.UnitName)
				continue
			}
			version := 1
			if metadata.ID != 0 {
				if metadata.Config != nil {
					version = metadata.Config.Version + 1
					//continue
				}
				// 只更新指定用户的数据
				if metadata.Config.CreatedBy != 71 {
					log.Infof("ignore update, unitId: %v, unitName: %v, createdBy: %v", conf.UnitId, conf.UnitName, metadata.Config.CreatedByChineseName)
					continue
				}
			} else {
				err = deploy_config.CreateDeployMetadata(dbEnv, &metadata)
				if err != nil {
					return err
				}
			}

			// 3.创建deploy_config
			deployConfig := deploy_config.DeployConfig{
				AppBasicConfig:       appBaseConfig,
				AppAdvancedConfig:    appAdvancedConfig,
				TraitConfig:          traitConfig,
				Version:              version,
				CreatedBy:            71, //创建用户id
				CreatedByChineseName: "陈伟良",
				CreatedByEmployeeNo:  "T2517",
				TemplateID:           getTemplateId(int64(metadata.Env)),
				MetadataID:           metadata.ID,
			}
			deployConfig.ConfigType = 1
			err = deploy_config.CreateDeployConfig(dbEnv, &deployConfig)
			if err != nil {
				return err
			}
			metadata.ConfigID = deployConfig.ID
			err = deploy_config.UpdateDeployMetadata(dbEnv, &metadata)
			if err != nil {
				return err
			}

			// 4.更新matchLabels
			matchLabels := make(map[string]string)
			serviceLabels := make(map[string]string)
			matchLabels["app"] = conf.UnitName
			matchLabels["app.kubernetes.io/instance"] = conf.UnitName
			serviceLabels["app"] = conf.UnitName
			serviceLabels["app.kubernetes.io/instance"] = conf.UnitName
			params := app.UpdateLabelsParameter{
				AppId:         int(appId),
				MatchLabels:   matchLabels,
				ServiceLabels: serviceLabels,
			}
			err = app.UpgradeAppLabels(dbEnv, &params)
			if err != nil {
				return err
			}

			successApps = append(successApps, conf.UnitName)
			count++
		}
	}
	log.Infof("common-server-finish:%d", count)
	log.Infof("common-server-finish-list: %+v", successApps)
	return nil
}

func HandAppBaseConfig(config WorkloadChart) ([]byte, error) {
	res := deploy.AppBasicConfig{
		NetworkType:  "",
		NetworkPorts: []*deploy.AppBasicConfig_Port{},
		Annotations:  []*deploy.Pair{},
		Envs:         []*deploy.Pair{},
		Commands:     []string{},
		Configs:      []*deploy.AppBasicConfig_Config{},
	}

	// 网络配置-服务协议和端口
	if config.Service.Enabled {
		res.NetworkType = config.Service.Type
		serviceName := config.Service.Name
		if len(serviceName) >= 15 || serviceName == "" {
			serviceName = "service-0"
		}
		res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
			Name:         serviceName,
			InternalPort: config.Service.TargetPort,
			ExternalPort: config.Service.Port,
		})
		// 网络配置-多端口
		for _, port := range config.Service.MultiPorts {
			count := 1
			multiSvcName := port.Name
			if len(multiSvcName) >= 15 {
				multiSvcName = fmt.Sprintf("service-%d", count)
				count++
			}
			res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
				Name:         multiSvcName,
				InternalPort: port.TargetPort,
				ExternalPort: port.Port,
			})
		}
	}
	// 环境变量
	res.Envs = append(res.Envs, &deploy.Pair{
		Key:   "TZ",
		Value: "Asia/Shanghai",
	})
	for _, env := range config.Deploy.Env {
		if env.Name != "" && env.Value != "" {
			envVal := env.Value
			res.Envs = append(res.Envs, &deploy.Pair{
				Key:   env.Name,
				Value: envVal,
			})
		}
	}
	// 命令参数-执行命令
	for _, command := range config.Deploy.Command {
		res.Commands = append(res.Commands, command)
	}
	// 配置文件
	for fileName, fileContent := range config.ConfigFiles {
		res.Configs = append(res.Configs, &deploy.AppBasicConfig_Config{DirPath: "/config", FileName: fileName, Content: fileContent})
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func builderHandler() *deploy.HealthCheck_HealthCheckHandler {
	return &deploy.HealthCheck_HealthCheckHandler{
		Type: "",
		Exec: &deploy.ExecAction{
			Command: []string{},
		},
		TcpSocket: &deploy.TCPSocketAction{},
		HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
		Grpc:      &deploy.GRPCAction{},
		Probe: &deploy.HealthCheck_Probe{
			InitialDelaySeconds: 0,
			PeriodSeconds:       10,
			TimeoutSeconds:      1,
			SuccessThreshold:    1,
			FailureThreshold:    3,
		},
	}
}

func HandAppAdvancedConfig(config WorkloadChart) ([]byte, error) {
	res := deploy.AppAdvancedConfig{
		Labels:      []*deploy.Pair{},
		Annotations: []*deploy.Pair{},
		HostAliases: []*deploy.HostAlias{},
		HealthCheck: &deploy.HealthCheck{
			ReadinessProbe: builderHandler(),
			LivenessProbe:  builderHandler(),
			StartupProbe:   builderHandler(),
			Types:          []string{},
		},
		ServiceAccountName: "default",
		Lifecycle: &deploy.Lifecycle{
			PostStart: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			PreStop: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			Types: []string{},
		},
	}

	expectedLabel := map[string]bool{
		"uuid":       true,
		"cluster_id": true,
		"env":        true,
		"lang":       true,
		"type":       true,
	}
	// label 标签
	for key, value := range config.Labels {
		if _, ok := expectedLabel[key]; ok {
			continue
		}
		res.Labels = append(res.Labels, &deploy.Pair{Key: key, Value: value})
	}
	// 注解
	for k, v := range config.Deploy.Annotations {
		// 监控相关
		if k == "telemetry.mesh.quwan.io/customMetricsPath" ||
			k == "telemetry.mesh.quwan.io/customMetricsPort" ||
			k == "telemetry.mesh.quwan.io/customMetricsScrape" ||
			k == "telemetry.mesh.quwan.io/customMetricsContainer" {
			continue
		}
		// sidecar
		if k == "sidecar.istio.io/proxyCPU" ||
			k == "sidecar.istio.io/proxyMemory" ||
			k == "sidecar.istio.io/proxyCPULimit" ||
			k == "sidecar.istio.io/proxyMemoryLimit" {
			continue
		}
		res.Annotations = append(res.Annotations, &deploy.Pair{
			Key:   k,
			Value: v,
		})
	}

	// 主机别名
	if config.TempNamespace.HostAliases.Enabled {
		for _, host := range config.HostAliases {
			for _, name := range host.Hostnames {
				if name != "" && host.IP != "" {
					res.HostAliases = append(res.HostAliases, &deploy.HostAlias{
						Domain: name,
						Ip:     host.IP,
					})
				}
			}
		}
	}

	//健康检查-就绪探针
	if config.TempNamespace.Probe.ReadinessProbe.Enabled {
		res.HealthCheck.Types = append(res.HealthCheck.Types, "readinessProbe")
		if len(config.Probe.ReadinessProbe.Exec.Command) > 0 {
			res.HealthCheck.ReadinessProbe.Type = "exec"
			res.HealthCheck.ReadinessProbe.Exec = &deploy.ExecAction{}
			for _, cmd := range config.Probe.ReadinessProbe.Exec.Command {
				res.HealthCheck.ReadinessProbe.Exec.Command = append(res.HealthCheck.ReadinessProbe.Exec.Command, cmd)
			}
		} else if config.Probe.ReadinessProbe.TCPSocket.Port > 0 {
			res.HealthCheck.ReadinessProbe.Type = "tcpSocket"
			res.HealthCheck.ReadinessProbe.TcpSocket = &deploy.TCPSocketAction{
				Port: config.Probe.ReadinessProbe.TCPSocket.Port,
			}
		} else if config.Probe.ReadinessProbe.HTTPGet.Port > 0 {
			res.HealthCheck.ReadinessProbe.Type = "httpGet"
			res.HealthCheck.ReadinessProbe.HttpGet = &deploy.HTTPGetAction{
				Path: config.Probe.ReadinessProbe.HTTPGet.Path,
				Port: config.Probe.ReadinessProbe.HTTPGet.Port,
			}
			for _, header := range config.Probe.ReadinessProbe.HTTPGet.HTTPHeaders {
				res.HealthCheck.ReadinessProbe.HttpGet.Headers = append(res.HealthCheck.ReadinessProbe.HttpGet.Headers,
					&deploy.Pair{Key: header.Name, Value: header.Value})
			}
		}

		res.HealthCheck.ReadinessProbe.Probe = &deploy.HealthCheck_Probe{}
		// 探测间隔
		if config.Probe.ReadinessProbe.PeriodSeconds > 0 {
			res.HealthCheck.ReadinessProbe.Probe.PeriodSeconds = config.Probe.ReadinessProbe.PeriodSeconds
		} else {
			res.HealthCheck.ReadinessProbe.Probe.PeriodSeconds = 10
		}
		// 启动延时
		if config.Probe.ReadinessProbe.InitialDelaySeconds > 0 {
			res.HealthCheck.ReadinessProbe.Probe.InitialDelaySeconds = config.Probe.ReadinessProbe.InitialDelaySeconds
		} else {
			res.HealthCheck.ReadinessProbe.Probe.InitialDelaySeconds = 0
		}
		// 超时时长
		if config.Probe.ReadinessProbe.TimeoutSeconds > 0 {
			res.HealthCheck.ReadinessProbe.Probe.TimeoutSeconds = config.Probe.ReadinessProbe.TimeoutSeconds
		} else {
			res.HealthCheck.ReadinessProbe.Probe.TimeoutSeconds = 1
		}
		// 成功阈值
		if config.Probe.ReadinessProbe.SuccessThreshold > 0 {
			res.HealthCheck.ReadinessProbe.Probe.SuccessThreshold = config.Probe.ReadinessProbe.SuccessThreshold
		} else {
			res.HealthCheck.ReadinessProbe.Probe.SuccessThreshold = 1
		}
		// 失败阈值
		if config.Probe.ReadinessProbe.FailureThreshold > 0 {
			res.HealthCheck.ReadinessProbe.Probe.FailureThreshold = config.Probe.ReadinessProbe.FailureThreshold
		} else {
			res.HealthCheck.ReadinessProbe.Probe.FailureThreshold = 3
		}
	}

	//健康检查-存活探针
	if config.TempNamespace.Probe.LivenessProbe.Enabled {
		res.HealthCheck.Types = append(res.HealthCheck.Types, "livenessProbe")
		if len(config.Probe.LivenessProbe.Exec.Command) > 0 {
			res.HealthCheck.LivenessProbe.Type = "exec"
			res.HealthCheck.LivenessProbe.Exec = &deploy.ExecAction{}
			for _, cmd := range config.Probe.LivenessProbe.Exec.Command {
				res.HealthCheck.LivenessProbe.Exec.Command = append(res.HealthCheck.LivenessProbe.Exec.Command, cmd)
			}
		} else if config.Probe.LivenessProbe.TCPSocket.Port > 0 {
			res.HealthCheck.LivenessProbe.Type = "tcpSocket"
			res.HealthCheck.LivenessProbe.TcpSocket = &deploy.TCPSocketAction{
				Port: config.Probe.LivenessProbe.TCPSocket.Port,
			}
		} else if config.Probe.LivenessProbe.HTTPGet.Port > 0 {
			res.HealthCheck.LivenessProbe.Type = "httpGet"
			res.HealthCheck.LivenessProbe.HttpGet = &deploy.HTTPGetAction{
				Path: config.Probe.LivenessProbe.HTTPGet.Path,
				Port: config.Probe.LivenessProbe.HTTPGet.Port,
			}
			for _, header := range config.Probe.LivenessProbe.HTTPGet.HTTPHeaders {
				res.HealthCheck.LivenessProbe.HttpGet.Headers = append(res.HealthCheck.LivenessProbe.HttpGet.Headers,
					&deploy.Pair{Key: header.Name, Value: header.Value})
			}
		}

		res.HealthCheck.LivenessProbe.Probe = &deploy.HealthCheck_Probe{}
		// 探测间隔
		if config.Probe.LivenessProbe.PeriodSeconds > 0 {
			res.HealthCheck.LivenessProbe.Probe.PeriodSeconds = config.Probe.LivenessProbe.PeriodSeconds
		} else {
			res.HealthCheck.LivenessProbe.Probe.PeriodSeconds = 10
		}
		// 启动延时
		if config.Probe.LivenessProbe.InitialDelaySeconds > 0 {
			res.HealthCheck.LivenessProbe.Probe.InitialDelaySeconds = config.Probe.LivenessProbe.InitialDelaySeconds
		} else {
			res.HealthCheck.LivenessProbe.Probe.InitialDelaySeconds = 0
		}
		// 超时时长
		if config.Probe.LivenessProbe.TimeoutSeconds > 0 {
			res.HealthCheck.LivenessProbe.Probe.TimeoutSeconds = config.Probe.LivenessProbe.TimeoutSeconds
		} else {
			res.HealthCheck.LivenessProbe.Probe.TimeoutSeconds = 1
		}
		// 成功阈值
		if config.Probe.LivenessProbe.SuccessThreshold > 0 {
			res.HealthCheck.LivenessProbe.Probe.SuccessThreshold = config.Probe.LivenessProbe.SuccessThreshold
		} else {
			res.HealthCheck.LivenessProbe.Probe.SuccessThreshold = 1
		}
		// 失败阈值
		if config.Probe.LivenessProbe.FailureThreshold > 0 {
			res.HealthCheck.LivenessProbe.Probe.FailureThreshold = config.Probe.LivenessProbe.FailureThreshold
		} else {
			res.HealthCheck.LivenessProbe.Probe.FailureThreshold = 3
		}
	}

	// 挂载配置
	res.MountConfig = &deploy.MountConfig{
		Volumes:      []*deploy.Volume{},
		VolumeMounts: []*deploy.VolumeMount{},
	}

	// 默认挂载host localtime
	res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
		Type:       "hostPath",
		VolumeName: "host-localtime",
		ReadOnly:   true,
		RefName:    "/etc/localtime",
	})

	res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
		VolumeName: "host-localtime",
		MountPoint: "/etc/localtime",
	})

	// 挂载pvc
	if config.TempNamespace.Volumes.Enabled {
		for _, volume := range config.Volumes {
			// configMap
			if volume.ConfigMap.Name != "" {
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "configMap",
					VolumeName: volume.Name,
					RefName:    volume.ConfigMap.Name,
				})
				continue
			}
			// hostPath
			if volume.HostPath.Path != "" {
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "hostPath",
					VolumeName: volume.Name,
					ReadOnly:   true,
					RefName:    volume.HostPath.Path,
				})
			}
			if volume.Name != "" {
				res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
					Type:       "PVC",
					ReadOnly:   false,
					VolumeName: volume.Name,
					RefName:    volume.PersistentVolumeClaim.ClaimName,
				})
			}
		}
	}

	if config.TempNamespace.VolumeMounts.Enabled {
		for _, volume := range config.VolumeMounts {
			if volume.Name != "" {
				res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
					VolumeName: volume.Name,
					MountPoint: volume.MountPath,
					SubPath:    volume.SubPath,
				})
			}
		}
	}

	// lifecycle
	if config.TempNamespace.Deploy.Lifecycle.Enabled {
		isPreStop := true
		if len(config.Deploy.Lifecycle.PreStop.Exec.Command) > 0 {
			res.Lifecycle.PreStop.Type = "exec"
			res.Lifecycle.PreStop.Exec = &deploy.ExecAction{}
			for _, cmd := range config.Deploy.Lifecycle.PreStop.Exec.Command {
				res.Lifecycle.PreStop.Exec.Command = append(res.Lifecycle.PreStop.Exec.Command, cmd)
			}
		} else if config.Deploy.Lifecycle.PreStop.TCPSocket.Port > 0 {
			res.Lifecycle.PreStop.Type = "tcpSocket"
			res.Lifecycle.PreStop.TcpSocket = &deploy.TCPSocketAction{
				Port: config.Deploy.Lifecycle.PreStop.TCPSocket.Port,
			}
		} else if config.Deploy.Lifecycle.PreStop.HttpGet.Port > 0 {
			res.Lifecycle.PreStop.Type = "httpGet"
			res.Lifecycle.PreStop.HttpGet = &deploy.HTTPGetAction{
				Path:   config.Deploy.Lifecycle.PreStop.HttpGet.Path,
				Port:   config.Deploy.Lifecycle.PreStop.HttpGet.Port,
				Scheme: "http",
				Host:   "localhost",
			}
			for _, header := range config.Deploy.Lifecycle.PreStop.HttpGet.HTTPHeaders {
				res.Lifecycle.PreStop.HttpGet.Headers = append(res.Lifecycle.PreStop.HttpGet.Headers, &deploy.Pair{
					Key:   header.Name,
					Value: header.Value,
				})
			}
		} else {
			isPreStop = false
		}
		// 使用preStop
		if isPreStop {
			res.Lifecycle.Types = append(res.Lifecycle.Types, "preStop")
		}
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func HandTraitConfig(config WorkloadChart, unitKey string, hpaMap map[string]HpaChart) ([]byte, error) {
	var res deploy.TraitConfig
	// 资源限制
	res.ResourceConstraints = &deploy.ResourceConstraints{Resources: &deploy.Resources{}, Sidecar: &deploy.ResourceConstraints_Sidecar{}}
	cpuReq := config.Resources.Requests.CPU
	if strings.Contains(cpuReq, "m") || strings.Contains(cpuReq, "M") {
		//单位为m
		reqCpu, _ := strconv.ParseFloat(cpuReq[0:len(cpuReq)-1], 32)
		res.ResourceConstraints.Resources.RequestCpu = float32(reqCpu / 1000)
	} else {
		//单位为核
		reqCpu, _ := strconv.ParseFloat(cpuReq, 32)
		res.ResourceConstraints.Resources.RequestCpu = float32(reqCpu)
	}

	memReq, _ := strconv.ParseFloat(config.Resources.Requests.Memory[0:len(config.Resources.Requests.Memory)-2], 32)
	if strings.Contains(config.Resources.Requests.Memory, "Gi") {
		res.ResourceConstraints.Resources.RequestMemory = float32(memReq * 1024)
	} else {
		res.ResourceConstraints.Resources.RequestMemory = float32(memReq)
	}

	cpuLim := config.Resources.Limits.CPU
	if strings.Contains(cpuLim, "m") || strings.Contains(cpuLim, "M") {
		//单位为m
		limCpu, _ := strconv.ParseFloat(cpuLim[0:len(cpuLim)-1], 32)
		res.ResourceConstraints.Resources.LimitCpu = float32(limCpu / 1000)
	} else {
		//单位为核
		limCpu, _ := strconv.ParseFloat(cpuLim, 32)
		res.ResourceConstraints.Resources.LimitCpu = float32(limCpu)
	}

	limMem, _ := strconv.ParseFloat(config.Resources.Limits.Memory[0:len(config.Resources.Limits.Memory)-2], 32)
	if strings.Contains(config.Resources.Limits.Memory, "Gi") {
		res.ResourceConstraints.Resources.LimitMemory = float32(limMem * 1024)
	} else {
		res.ResourceConstraints.Resources.LimitMemory = float32(limMem)
	}

	//sidecar注入
	var reqCpu, reqMem1, limCpu, limMem1 float64
	var has bool
	for key, _ := range config.Deploy.Annotations {
		if key == "sidecar.istio.io/proxyCPU" || key == "sidecar.istio.io/proxyMemory" ||
			key == "sidecar.istio.io/proxyCPULimit" || key == "sidecar.istio.io/proxyMemoryLimit" {
			has = true
		}
	}
	//cpu
	if value, ok := config.Deploy.Annotations["sidecar.istio.io/proxyCPU"]; ok {
		if strings.Contains(value, "m") {
			cpu, _ := strconv.ParseFloat(value[0:len(value)-1], 32)
			reqCpu = cpu / 1000
		} else {
			reqCpu, _ = strconv.ParseFloat(value, 32)
		}
	} else if has {
		reqCpu = 0.1
	}
	//memory
	if value, ok := config.Deploy.Annotations["sidecar.istio.io/proxyMemory"]; ok {
		reqMem1, _ = strconv.ParseFloat(value[0:len(value)-2], 32)
		if strings.Contains(value, "Gi") {
			reqMem1 = reqMem1 * 1024
		}
	} else if has {
		reqMem1 = 400
	}
	//cpuLimit
	if value, ok := config.Deploy.Annotations["sidecar.istio.io/proxyCPULimit"]; ok {
		if strings.Contains(value, "m") {
			cpu, _ := strconv.ParseFloat(value[0:len(value)-1], 32)
			limCpu = cpu / 1000
		} else {
			limCpu, _ = strconv.ParseFloat(value, 32)
		}
	} else if has {
		limCpu = 1
	}
	//memoryLimit
	if value, ok := config.Deploy.Annotations["sidecar.istio.io/proxyMemoryLimit"]; ok {
		limMem1, _ = strconv.ParseFloat(value[0:len(value)-2], 32)
		if strings.Contains(value, "Gi") {
			limMem1 = limMem1 * 1024
		}
	} else if has {
		limMem1 = 1024
	}
	if has {
		res.ResourceConstraints.Sidecar = &deploy.ResourceConstraints_Sidecar{}
		res.ResourceConstraints.Sidecar.Enabled = true
		res.ResourceConstraints.Sidecar.RequestCpu = float32(reqCpu)
		res.ResourceConstraints.Sidecar.RequestMemory = float32(reqMem1)
		res.ResourceConstraints.Sidecar.LimitCpu = float32(limCpu)
		res.ResourceConstraints.Sidecar.LimitMemory = float32(limMem1)
	}

	// 伸缩配置
	res.ScalingConfig = &deploy.ScalingConfig{
		Hpa:      &deploy.ScalingConfig_HPA{Types: []string{}},
		MultiHpa: &deploy.ScalingConfig_MultiHPA{Types: []string{}, Cron: []*deploy.ScalingConfig_MultiHPA_Cron{}},
	}
	replicas, _ := strconv.Atoi(config.Replicas)
	if replicas > 0 {
		res.ScalingConfig.Replicas = int32(replicas)
	} else {
		res.ScalingConfig.Replicas = 1
	}
	res.ScalingConfig.Type = "replicas"
	if value, isExist := hpaMap[unitKey]; isExist && (value.Autoscale.CPU.Enabled || value.Autoscale.Memory.Enabled) {
		res.ScalingConfig.MultiHpa = &deploy.ScalingConfig_MultiHPA{
			Min:   value.Autoscale.Min,
			Max:   value.Autoscale.Max,
			Types: make([]string, 0),
		}
		if value.Autoscale.CPU.Enabled {
			res.ScalingConfig.MultiHpa.CpuUtilization = value.Autoscale.CPU.TargetAverageUtilization
			res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "cpu")
		}
		if value.Autoscale.Memory.Enabled {
			res.ScalingConfig.MultiHpa.MemoryUtilization = value.Autoscale.Memory.TargetAverageUtilization
			res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "memory")
		}
		res.ScalingConfig.Type = "multiHPA"
	}

	// 监控指标-MonitorMetrics
	if value, ok := config.Deploy.Annotations["telemetry.mesh.quwan.io/customMetricsScrape"]; ok && value == "true" {
		res.MonitorMetrics = &deploy.MonitorMetrics{}
		var (
			portStr       string
			metricsPath   string
			containerName string
		)
		for k, v := range config.Deploy.Annotations {
			if k == "telemetry.mesh.quwan.io/customMetricsPath" {
				metricsPath = v
			}
			if k == "telemetry.mesh.quwan.io/customMetricsPort" {
				portStr = v
			}
			if k == "telemetry.mesh.quwan.io/customMetricsContainer" {
				containerName = v
			}
		}
		metricsPort, _ := strconv.Atoi(portStr)
		if metricsPort != 0 && metricsPath != "" {
			res.MonitorMetrics = &deploy.MonitorMetrics{
				Enabled:       true,
				Port:          int32(metricsPort),
				MetricsPath:   metricsPath,
				ContainerName: containerName,
			}
		}
	}

	// 升级策略-upgradeStrategy
	res.UpgradeStrategy = &deploy.UpgradeStrategy{
		Enabled:        false,
		MaxSurge:       25,
		MaxUnavailable: 25,
	}

	// 高级配置-advancedConfig
	res.AdvancedConfig = &deploy.TraitAdvancedConfig{
		SchedulingStrategy: []*deploy.SchedulingStrategy{},
		RateLimiting:       &deploy.RateLimiting{},
		CircuitBreaker:     &deploy.CircuitBreaker{},
		InitContainers:     []*deploy.InitContainer{},
		MultiContainers:    []*deploy.MultiContainer{},
	}

	// 亲和性
	if config.TempNamespace.Affinity.Enabled && len(config.Affinity.PodAntiAffinity.PreferredDuringSchedulingIgnoredDuringExecution) > 0 {
		antiAffinity := config.Affinity.PodAntiAffinity.PreferredDuringSchedulingIgnoredDuringExecution[0]
		rules := []*deploy.SchedulingStrategy_Rule{
			{
				Key:      antiAffinity.PodAffinityTerm.LabelSelector.MatchExpressions[0].Key,
				Operator: "In",
				Value:    antiAffinity.PodAffinityTerm.LabelSelector.MatchExpressions[0].Values[0],
			},
		}
		res.AdvancedConfig.SchedulingStrategy = append(res.AdvancedConfig.SchedulingStrategy, &deploy.SchedulingStrategy{
			Type:         "podAntiAffinity",
			Priority:     "Preferred",
			Topology_Key: antiAffinity.PodAffinityTerm.TopologyKey,
			Weight:       int32(antiAffinity.Weight),
			Rules:        rules,
		})
	}

	// initContainers
	if config.TempNamespace.Deploy.InitContainers.Enabled {
		for _, c := range config.Deploy.InitContainers {
			container := &deploy.InitContainer{
				ImageName:     c.Image,
				ContainerName: c.Name,
				Commands:      []string{},
				InitMountPath: "",
				AppMountPath:  "",
				Envs:          []*deploy.Pair{},
				VolumeMounts:  []*deploy.VolumeMount{},
				MountName:     "",
			}
			// 执行命令
			for _, cmd := range c.Command {
				container.Commands = append(container.Commands, cmd)
			}
			// 命令参数
			for _, arg := range c.Args {
				container.Commands = append(container.Commands, arg)
			}
			for _, mount := range c.VolumeMounts {
				container.VolumeMounts = append(container.VolumeMounts, &deploy.VolumeMount{
					VolumeName: mount.Name,
					MountPoint: mount.MountPath,
					SubPath:    mount.SubPath,
				})
			}
			res.AdvancedConfig.InitContainers = append(res.AdvancedConfig.InitContainers, container)
		}
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func getClusterName(name string) string {
	switch name {
	case "kubeconfig-k8s-hw-bj-1-prod":
		return "k8s-hw-bj-1-prod"
	case "火山云集群k8s-hs-bj-1-test":
		return "k8s-hs-bj-1-test"
	default:
		return name
	}
}

func getEnvEnum(k8sName string) int8 {
	switch k8sName {
	// 开发
	case "k8s-hw-zt-dev",
		"k8s-tc-bj-1-dev":
		return int8(1)
	// 测试
	case "k8s-tc-bj-1-test",
		"k8s-hw-bj-zt-test-turbo",
		"火山云集群k8s-hs-bj-1-test":
		return int8(2)
	case "k8s-tc-bj-1-prod",
		"kubeconfig-k8s-hw-bj-1-prod",
		"k8s-tc-sg-1-prod":
		return int8(4)
	default:
		return 4
	}
}

func getTemplateId(env int64) int64 {
	//switch env {
	//case 2:
	//	return 77
	//case 4:
	//	return 78
	//default:
	//	return 0
	//}

	return 0
}

func isContainUnit(unit string, keys []string) bool {
	var myMap map[string]bool
	myMap = make(map[string]bool)
	for _, key := range keys {
		myMap[key] = true
	}
	if _, ok := myMap[unit]; ok {
		return true
	}
	return false
}
