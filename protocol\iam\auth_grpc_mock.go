// Code generated by MockGen. DO NOT EDIT.
// Source: ./protocol/iam/auth_grpc.pb.go

// Package iam is a generated GoMock package.
package iam

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockAuthServiceClient is a mock of AuthServiceClient interface.
type MockAuthServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockAuthServiceClientMockRecorder
}

// MockAuthServiceClientMockRecorder is the mock recorder for MockAuthServiceClient.
type MockAuthServiceClientMockRecorder struct {
	mock *MockAuthServiceClient
}

// NewMockAuthServiceClient creates a new mock instance.
func NewMockAuthServiceClient(ctrl *gomock.Controller) *MockAuthServiceClient {
	mock := &MockAuthServiceClient{ctrl: ctrl}
	mock.recorder = &MockAuthServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAuthServiceClient) EXPECT() *MockAuthServiceClientMockRecorder {
	return m.recorder
}

// CheckAuthenticate mocks base method.
func (m *MockAuthServiceClient) CheckAuthenticate(ctx context.Context, in *CheckAuthenticateReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckAuthenticate", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAuthenticate indicates an expected call of CheckAuthenticate.
func (mr *MockAuthServiceClientMockRecorder) CheckAuthenticate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAuthenticate", reflect.TypeOf((*MockAuthServiceClient)(nil).CheckAuthenticate), varargs...)
}

// CheckTokenIsBan mocks base method.
func (m *MockAuthServiceClient) CheckTokenIsBan(ctx context.Context, in *CheckTokenIsBanReq, opts ...grpc.CallOption) (*CheckTokenIsBanResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckTokenIsBan", varargs...)
	ret0, _ := ret[0].(*CheckTokenIsBanResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckTokenIsBan indicates an expected call of CheckTokenIsBan.
func (mr *MockAuthServiceClientMockRecorder) CheckTokenIsBan(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckTokenIsBan", reflect.TypeOf((*MockAuthServiceClient)(nil).CheckTokenIsBan), varargs...)
}

// GenUserToken mocks base method.
func (m *MockAuthServiceClient) GenUserToken(ctx context.Context, in *GenUserTokenReq, opts ...grpc.CallOption) (*LoginResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenUserToken", varargs...)
	ret0, _ := ret[0].(*LoginResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenUserToken indicates an expected call of GenUserToken.
func (mr *MockAuthServiceClientMockRecorder) GenUserToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenUserToken", reflect.TypeOf((*MockAuthServiceClient)(nil).GenUserToken), varargs...)
}

// GetOwnUserinfo mocks base method.
func (m *MockAuthServiceClient) GetOwnUserinfo(ctx context.Context, in *GetOwnUserinfoReq, opts ...grpc.CallOption) (*GetOwnUserinfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOwnUserinfo", varargs...)
	ret0, _ := ret[0].(*GetOwnUserinfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOwnUserinfo indicates an expected call of GetOwnUserinfo.
func (mr *MockAuthServiceClientMockRecorder) GetOwnUserinfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOwnUserinfo", reflect.TypeOf((*MockAuthServiceClient)(nil).GetOwnUserinfo), varargs...)
}

// Login mocks base method.
func (m *MockAuthServiceClient) Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Login", varargs...)
	ret0, _ := ret[0].(*LoginResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Login indicates an expected call of Login.
func (mr *MockAuthServiceClientMockRecorder) Login(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Login", reflect.TypeOf((*MockAuthServiceClient)(nil).Login), varargs...)
}

// Logout mocks base method.
func (m *MockAuthServiceClient) Logout(ctx context.Context, in *LogoutReq, opts ...grpc.CallOption) (*LogoutResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Logout", varargs...)
	ret0, _ := ret[0].(*LogoutResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Logout indicates an expected call of Logout.
func (mr *MockAuthServiceClientMockRecorder) Logout(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Logout", reflect.TypeOf((*MockAuthServiceClient)(nil).Logout), varargs...)
}

// MockAuthServiceServer is a mock of AuthServiceServer interface.
type MockAuthServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockAuthServiceServerMockRecorder
}

// MockAuthServiceServerMockRecorder is the mock recorder for MockAuthServiceServer.
type MockAuthServiceServerMockRecorder struct {
	mock *MockAuthServiceServer
}

// NewMockAuthServiceServer creates a new mock instance.
func NewMockAuthServiceServer(ctrl *gomock.Controller) *MockAuthServiceServer {
	mock := &MockAuthServiceServer{ctrl: ctrl}
	mock.recorder = &MockAuthServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAuthServiceServer) EXPECT() *MockAuthServiceServerMockRecorder {
	return m.recorder
}

// CheckAuthenticate mocks base method.
func (m *MockAuthServiceServer) CheckAuthenticate(arg0 context.Context, arg1 *CheckAuthenticateReq) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAuthenticate", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAuthenticate indicates an expected call of CheckAuthenticate.
func (mr *MockAuthServiceServerMockRecorder) CheckAuthenticate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAuthenticate", reflect.TypeOf((*MockAuthServiceServer)(nil).CheckAuthenticate), arg0, arg1)
}

// CheckTokenIsBan mocks base method.
func (m *MockAuthServiceServer) CheckTokenIsBan(arg0 context.Context, arg1 *CheckTokenIsBanReq) (*CheckTokenIsBanResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckTokenIsBan", arg0, arg1)
	ret0, _ := ret[0].(*CheckTokenIsBanResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckTokenIsBan indicates an expected call of CheckTokenIsBan.
func (mr *MockAuthServiceServerMockRecorder) CheckTokenIsBan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckTokenIsBan", reflect.TypeOf((*MockAuthServiceServer)(nil).CheckTokenIsBan), arg0, arg1)
}

// GenUserToken mocks base method.
func (m *MockAuthServiceServer) GenUserToken(arg0 context.Context, arg1 *GenUserTokenReq) (*LoginResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenUserToken", arg0, arg1)
	ret0, _ := ret[0].(*LoginResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenUserToken indicates an expected call of GenUserToken.
func (mr *MockAuthServiceServerMockRecorder) GenUserToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenUserToken", reflect.TypeOf((*MockAuthServiceServer)(nil).GenUserToken), arg0, arg1)
}

// GetOwnUserinfo mocks base method.
func (m *MockAuthServiceServer) GetOwnUserinfo(arg0 context.Context, arg1 *GetOwnUserinfoReq) (*GetOwnUserinfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOwnUserinfo", arg0, arg1)
	ret0, _ := ret[0].(*GetOwnUserinfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOwnUserinfo indicates an expected call of GetOwnUserinfo.
func (mr *MockAuthServiceServerMockRecorder) GetOwnUserinfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOwnUserinfo", reflect.TypeOf((*MockAuthServiceServer)(nil).GetOwnUserinfo), arg0, arg1)
}

// Login mocks base method.
func (m *MockAuthServiceServer) Login(arg0 context.Context, arg1 *LoginReq) (*LoginResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Login", arg0, arg1)
	ret0, _ := ret[0].(*LoginResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Login indicates an expected call of Login.
func (mr *MockAuthServiceServerMockRecorder) Login(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Login", reflect.TypeOf((*MockAuthServiceServer)(nil).Login), arg0, arg1)
}

// Logout mocks base method.
func (m *MockAuthServiceServer) Logout(arg0 context.Context, arg1 *LogoutReq) (*LogoutResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Logout", arg0, arg1)
	ret0, _ := ret[0].(*LogoutResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Logout indicates an expected call of Logout.
func (mr *MockAuthServiceServerMockRecorder) Logout(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Logout", reflect.TypeOf((*MockAuthServiceServer)(nil).Logout), arg0, arg1)
}

// mustEmbedUnimplementedAuthServiceServer mocks base method.
func (m *MockAuthServiceServer) mustEmbedUnimplementedAuthServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAuthServiceServer")
}

// mustEmbedUnimplementedAuthServiceServer indicates an expected call of mustEmbedUnimplementedAuthServiceServer.
func (mr *MockAuthServiceServerMockRecorder) mustEmbedUnimplementedAuthServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAuthServiceServer", reflect.TypeOf((*MockAuthServiceServer)(nil).mustEmbedUnimplementedAuthServiceServer))
}

// MockUnsafeAuthServiceServer is a mock of UnsafeAuthServiceServer interface.
type MockUnsafeAuthServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAuthServiceServerMockRecorder
}

// MockUnsafeAuthServiceServerMockRecorder is the mock recorder for MockUnsafeAuthServiceServer.
type MockUnsafeAuthServiceServerMockRecorder struct {
	mock *MockUnsafeAuthServiceServer
}

// NewMockUnsafeAuthServiceServer creates a new mock instance.
func NewMockUnsafeAuthServiceServer(ctrl *gomock.Controller) *MockUnsafeAuthServiceServer {
	mock := &MockUnsafeAuthServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAuthServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAuthServiceServer) EXPECT() *MockUnsafeAuthServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAuthServiceServer mocks base method.
func (m *MockUnsafeAuthServiceServer) mustEmbedUnimplementedAuthServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAuthServiceServer")
}

// mustEmbedUnimplementedAuthServiceServer indicates an expected call of mustEmbedUnimplementedAuthServiceServer.
func (mr *MockUnsafeAuthServiceServerMockRecorder) mustEmbedUnimplementedAuthServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAuthServiceServer", reflect.TypeOf((*MockUnsafeAuthServiceServer)(nil).mustEmbedUnimplementedAuthServiceServer))
}
