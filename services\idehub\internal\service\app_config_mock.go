// Code generated by MockGen. DO NOT EDIT.
// Source: app_config.go

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	app "52tt.com/cicd/protocol/app"
	model "52tt.com/cicd/services/idehub/internal/model"
	gomock "github.com/golang/mock/gomock"
)

// MockAppConfigService is a mock of AppConfigService interface.
type MockAppConfigService struct {
	ctrl     *gomock.Controller
	recorder *MockAppConfigServiceMockRecorder
}

// MockAppConfigServiceMockRecorder is the mock recorder for MockAppConfigService.
type MockAppConfigServiceMockRecorder struct {
	mock *MockAppConfigService
}

// NewMockAppConfigService creates a new mock instance.
func NewMockAppConfigService(ctrl *gomock.Controller) *MockAppConfigService {
	mock := &MockAppConfigService{ctrl: ctrl}
	mock.recorder = &MockAppConfigServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAppConfigService) EXPECT() *MockAppConfigServiceMockRecorder {
	return m.recorder
}

// BuildSubEnvRoute mocks base method.
func (m *MockAppConfigService) BuildSubEnvRoute(ctx context.Context, in *BuildSubEnvRouteInput) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuildSubEnvRoute", ctx, in)
	ret0, _ := ret[0].(error)
	return ret0
}

// BuildSubEnvRoute indicates an expected call of BuildSubEnvRoute.
func (mr *MockAppConfigServiceMockRecorder) BuildSubEnvRoute(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildSubEnvRoute", reflect.TypeOf((*MockAppConfigService)(nil).BuildSubEnvRoute), ctx, in)
}

// ClearBuildSubEnvRoute mocks base method.
func (m *MockAppConfigService) ClearBuildSubEnvRoute(ctx context.Context, in *ClearSubEnvRouteInput) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearBuildSubEnvRoute", ctx, in)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearBuildSubEnvRoute indicates an expected call of ClearBuildSubEnvRoute.
func (mr *MockAppConfigServiceMockRecorder) ClearBuildSubEnvRoute(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearBuildSubEnvRoute", reflect.TypeOf((*MockAppConfigService)(nil).ClearBuildSubEnvRoute), ctx, in)
}

// GenerateNhctlConfig mocks base method.
func (m *MockAppConfigService) GenerateNhctlConfig(ctx context.Context, userID, appID int64, cluster, namespace, appName string) (*model.ServiceConfigV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateNhctlConfig", ctx, userID, appID, cluster, namespace, appName)
	ret0, _ := ret[0].(*model.ServiceConfigV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateNhctlConfig indicates an expected call of GenerateNhctlConfig.
func (mr *MockAppConfigServiceMockRecorder) GenerateNhctlConfig(ctx, userID, appID, cluster, namespace, appName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateNhctlConfig", reflect.TypeOf((*MockAppConfigService)(nil).GenerateNhctlConfig), ctx, userID, appID, cluster, namespace, appName)
}

// GetAppClusterInfo mocks base method.
func (m *MockAppConfigService) GetAppClusterInfo(ctx context.Context, appId int64, appCluster *string) (string, string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppClusterInfo", ctx, appId, appCluster)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(string)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetAppClusterInfo indicates an expected call of GetAppClusterInfo.
func (mr *MockAppConfigServiceMockRecorder) GetAppClusterInfo(ctx, appId, appCluster interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppClusterInfo", reflect.TypeOf((*MockAppConfigService)(nil).GetAppClusterInfo), ctx, appId, appCluster)
}

// GetAppClusterInfoWithAppIds mocks base method.
func (m *MockAppConfigService) GetAppClusterInfoWithAppIds(ctx context.Context, appCluster *string, appIds ...int64) (map[int64]*model.AppsItem, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, appCluster}
	for _, a := range appIds {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAppClusterInfoWithAppIds", varargs...)
	ret0, _ := ret[0].(map[int64]*model.AppsItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppClusterInfoWithAppIds indicates an expected call of GetAppClusterInfoWithAppIds.
func (mr *MockAppConfigServiceMockRecorder) GetAppClusterInfoWithAppIds(ctx, appCluster interface{}, appIds ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, appCluster}, appIds...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppClusterInfoWithAppIds", reflect.TypeOf((*MockAppConfigService)(nil).GetAppClusterInfoWithAppIds), varargs...)
}

// GetAppClusterInfoWithProjectIds mocks base method.
func (m *MockAppConfigService) GetAppClusterInfoWithProjectIds(ctx context.Context, appCluster *string, prjIds ...int64) (map[int64]*model.AppsItem, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, appCluster}
	for _, a := range prjIds {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAppClusterInfoWithProjectIds", varargs...)
	ret0, _ := ret[0].(map[int64]*model.AppsItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppClusterInfoWithProjectIds indicates an expected call of GetAppClusterInfoWithProjectIds.
func (mr *MockAppConfigServiceMockRecorder) GetAppClusterInfoWithProjectIds(ctx, appCluster interface{}, prjIds ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, appCluster}, prjIds...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppClusterInfoWithProjectIds", reflect.TypeOf((*MockAppConfigService)(nil).GetAppClusterInfoWithProjectIds), varargs...)
}

// GetAppInfo mocks base method.
func (m *MockAppConfigService) GetAppInfo(ctx context.Context, appId int64) (*app.APP, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppInfo", ctx, appId)
	ret0, _ := ret[0].(*app.APP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppInfo indicates an expected call of GetAppInfo.
func (mr *MockAppConfigServiceMockRecorder) GetAppInfo(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppInfo", reflect.TypeOf((*MockAppConfigService)(nil).GetAppInfo), ctx, appId)
}
