// Code generated by MockGen. DO NOT EDIT.
// Source: gitlab.go

// Package gitlab is a generated GoMock package.
package gitlab

import (
	context "context"
	fmt "fmt"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	go_gitlab "github.com/xanzy/go-gitlab"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// AddDiscussion mocks base method.
func (m *MockService) AddDiscussion(param *DiscussionParameter, body fmt.Stringer) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddDiscussion", param, body)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddDiscussion indicates an expected call of AddDiscussion.
func (mr *MockServiceMockRecorder) AddDiscussion(param, body interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddDiscussion", reflect.TypeOf((*MockService)(nil).AddDiscussion), param, body)
}

// AddSysUser mocks base method.
func (m *MockService) AddSysUser(projectId, userId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSysUser", projectId, userId)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddSysUser indicates an expected call of AddSysUser.
func (mr *MockServiceMockRecorder) AddSysUser(projectId, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSysUser", reflect.TypeOf((*MockService)(nil).AddSysUser), projectId, userId)
}

// AddWebhook mocks base method.
func (m *MockService) AddWebhook(projectID int, url string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddWebhook", projectID, url)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddWebhook indicates an expected call of AddWebhook.
func (mr *MockServiceMockRecorder) AddWebhook(projectID, url interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWebhook", reflect.TypeOf((*MockService)(nil).AddWebhook), projectID, url)
}

// AppendDiscussion mocks base method.
func (m *MockService) AppendDiscussion(param *DiscussionParameter, body fmt.Stringer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AppendDiscussion", param, body)
	ret0, _ := ret[0].(error)
	return ret0
}

// AppendDiscussion indicates an expected call of AppendDiscussion.
func (mr *MockServiceMockRecorder) AppendDiscussion(param, body interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AppendDiscussion", reflect.TypeOf((*MockService)(nil).AppendDiscussion), param, body)
}

// Branches mocks base method.
func (m *MockService) Branches(projectId int, search, regex string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Branches", projectId, search, regex)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Branches indicates an expected call of Branches.
func (mr *MockServiceMockRecorder) Branches(projectId, search, regex interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Branches", reflect.TypeOf((*MockService)(nil).Branches), projectId, search, regex)
}

// Changes mocks base method.
func (m *MockService) Changes(addr string, iid int64) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Changes", addr, iid)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Changes indicates an expected call of Changes.
func (mr *MockServiceMockRecorder) Changes(addr, iid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Changes", reflect.TypeOf((*MockService)(nil).Changes), addr, iid)
}

// GetBranch mocks base method.
func (m *MockService) GetBranch(repoAddr, branch string) (*go_gitlab.Branch, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBranch", repoAddr, branch)
	ret0, _ := ret[0].(*go_gitlab.Branch)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBranch indicates an expected call of GetBranch.
func (mr *MockServiceMockRecorder) GetBranch(repoAddr, branch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBranch", reflect.TypeOf((*MockService)(nil).GetBranch), repoAddr, branch)
}

// GetBranches mocks base method.
func (m *MockService) GetBranches(repoAddr, search, regex string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBranches", repoAddr, search, regex)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBranches indicates an expected call of GetBranches.
func (mr *MockServiceMockRecorder) GetBranches(repoAddr, search, regex interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBranches", reflect.TypeOf((*MockService)(nil).GetBranches), repoAddr, search, regex)
}

// GetCommits mocks base method.
func (m *MockService) GetCommits(ctx context.Context, projectId int, params map[string]string) ([]go_gitlab.Commit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommits", ctx, projectId, params)
	ret0, _ := ret[0].([]go_gitlab.Commit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommits indicates an expected call of GetCommits.
func (mr *MockServiceMockRecorder) GetCommits(ctx, projectId, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommits", reflect.TypeOf((*MockService)(nil).GetCommits), ctx, projectId, params)
}

// GetDiscussion mocks base method.
func (m *MockService) GetDiscussion(param *DiscussionParameter) (*DiscussionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDiscussion", param)
	ret0, _ := ret[0].(*DiscussionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDiscussion indicates an expected call of GetDiscussion.
func (mr *MockServiceMockRecorder) GetDiscussion(param interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiscussion", reflect.TypeOf((*MockService)(nil).GetDiscussion), param)
}

// GetGitlabEventMsg mocks base method.
func (m *MockService) GetGitlabEventMsg(gitEvents map[string]interface{}) (*GitEventResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGitlabEventMsg", gitEvents)
	ret0, _ := ret[0].(*GitEventResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGitlabEventMsg indicates an expected call of GetGitlabEventMsg.
func (mr *MockServiceMockRecorder) GetGitlabEventMsg(gitEvents interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGitlabEventMsg", reflect.TypeOf((*MockService)(nil).GetGitlabEventMsg), gitEvents)
}

// GetProject mocks base method.
func (m *MockService) GetProject(repoAddr string) (*go_gitlab.Project, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProject", repoAddr)
	ret0, _ := ret[0].(*go_gitlab.Project)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProject indicates an expected call of GetProject.
func (mr *MockServiceMockRecorder) GetProject(repoAddr interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProject", reflect.TypeOf((*MockService)(nil).GetProject), repoAddr)
}

// GetUserIdByEmail mocks base method.
func (m *MockService) GetUserIdByEmail(email string) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserIdByEmail", email)
	ret0, _ := ret[0].(int)
	return ret0
}

// GetUserIdByEmail indicates an expected call of GetUserIdByEmail.
func (mr *MockServiceMockRecorder) GetUserIdByEmail(email interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserIdByEmail", reflect.TypeOf((*MockService)(nil).GetUserIdByEmail), email)
}

// GetUserInfoByEmail mocks base method.
func (m *MockService) GetUserInfoByEmail(email string) []interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInfoByEmail", email)
	ret0, _ := ret[0].([]interface{})
	return ret0
}

// GetUserInfoByEmail indicates an expected call of GetUserInfoByEmail.
func (mr *MockServiceMockRecorder) GetUserInfoByEmail(email interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfoByEmail", reflect.TypeOf((*MockService)(nil).GetUserInfoByEmail), email)
}

// GetUserInfoById mocks base method.
func (m *MockService) GetUserInfoById(usrId int64) map[string]interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInfoById", usrId)
	ret0, _ := ret[0].(map[string]interface{})
	return ret0
}

// GetUserInfoById indicates an expected call of GetUserInfoById.
func (mr *MockServiceMockRecorder) GetUserInfoById(usrId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfoById", reflect.TypeOf((*MockService)(nil).GetUserInfoById), usrId)
}

// GitMergeChanges mocks base method.
func (m *MockService) GitMergeChanges(addr string, iid int64) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GitMergeChanges", addr, iid)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GitMergeChanges indicates an expected call of GitMergeChanges.
func (mr *MockServiceMockRecorder) GitMergeChanges(addr, iid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GitMergeChanges", reflect.TypeOf((*MockService)(nil).GitMergeChanges), addr, iid)
}

// IsCanBeMerged mocks base method.
func (m *MockService) IsCanBeMerged(projectId int, iid int64) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsCanBeMerged", projectId, iid)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsCanBeMerged indicates an expected call of IsCanBeMerged.
func (mr *MockServiceMockRecorder) IsCanBeMerged(projectId, iid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsCanBeMerged", reflect.TypeOf((*MockService)(nil).IsCanBeMerged), projectId, iid)
}

// ResolveDiscussion mocks base method.
func (m *MockService) ResolveDiscussion(param *DiscussionParameter) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResolveDiscussion", param)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResolveDiscussion indicates an expected call of ResolveDiscussion.
func (mr *MockServiceMockRecorder) ResolveDiscussion(param interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveDiscussion", reflect.TypeOf((*MockService)(nil).ResolveDiscussion), param)
}

// SetPipelineOfCommit mocks base method.
func (m *MockService) SetPipelineOfCommit(params *SetPipelineOfCommitParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPipelineOfCommit", params)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPipelineOfCommit indicates an expected call of SetPipelineOfCommit.
func (mr *MockServiceMockRecorder) SetPipelineOfCommit(params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPipelineOfCommit", reflect.TypeOf((*MockService)(nil).SetPipelineOfCommit), params)
}
