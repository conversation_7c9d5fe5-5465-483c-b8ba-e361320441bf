//go:generate mockgen -destination=change_log_mock.go -package=service -source=change_log.go
package service

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"52tt.com/cicd/services/deploy/internal/chain"
	"52tt.com/cicd/services/deploy/internal/chain/routing"

	apierrors "52tt.com/cicd/pkg/apierror/errors"
	"52tt.com/cicd/pkg/argo"
	"52tt.com/cicd/pkg/cloud/aggregate"
	"52tt.com/cicd/pkg/constants"
	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/event"
	"52tt.com/cicd/pkg/eventcenter"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/page"
	"52tt.com/cicd/pkg/safego"
	"52tt.com/cicd/pkg/tools"
	"52tt.com/cicd/pkg/tools/set"
	pbapp "52tt.com/cicd/protocol/app"
	pbdep "52tt.com/cicd/protocol/deploy"
	pbevent "52tt.com/cicd/protocol/event"
	pipelinepb "52tt.com/cicd/protocol/pipeline"
	"52tt.com/cicd/services/deploy/internal/conf"
	"52tt.com/cicd/services/deploy/internal/dao"
	"52tt.com/cicd/services/deploy/internal/envtarget"
	"52tt.com/cicd/services/deploy/internal/events/dispatcher"
	"52tt.com/cicd/services/deploy/internal/model"
	deperr "52tt.com/cicd/services/deploy/internal/pkg/error"
	"52tt.com/cicd/services/deploy/internal/pkg/tpl"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
)

type DeployChangeLogService interface {
	CreateChangeLog(ctx context.Context, req *model.CreateChangeLogReq) (int64, error)
	UpdateChangeLogStatus(ctx context.Context, req *model.UpdateChangeLogStatusReq) error
	ListChangeLog(ctx context.Context, query *model.ListChangeLogReq) (*page.Page, error)
	RunningChangeLogs(ctx context.Context, query model.RunningChangeLogsReq) ([]model.ChangeLog, error)
	// RollbackChangeLog 回滚
	RollbackChangeLog(ctx context.Context, req *model.RollbackReq) error
	// Restart 重启服务
	Restart(context.Context, *model.RestartReq) error
	// Retry 部署/下线重试
	Retry(ctx context.Context, ID int64) error
	// DeployWithConfig 根据变更记录 ID 与新部署配置执行部署
	DeployWithConfig(ctx context.Context, req model.DeployReq) error
	ListDeployRecord(ctx context.Context, changeLogID int64) ([]model.DeployRecord, error)
	SimpleDeploy(ctx context.Context, changeLog *dao.DeployChangeLog, appConfig *pbdep.GetAppConfigResp, deployTimeout int64) error
	OfflineDeploy(ctx context.Context, changeLog *dao.DeployChangeLog, appConfig *pbdep.GetAppConfigResp, runningChangeLogId int64, needUpdatePipeline bool) error
	CheckIsRunning(ctx context.Context, req *model.CheckReq) (bool, error)
	CheckEventlinkIsRunning(ctx context.Context, req *model.EventlinkCheckReq) (bool, error)
	Detail(ctx context.Context, req *model.ReplicasDetailReq) (*model.DetailResp, error)
	// ListStepLog 查询部署步骤的日志（record.result）
	ListStepLog(ctx context.Context, req *model.ListStepLogReq) (*model.ListStepLogResp, error)
	SendEvent(ctx context.Context, cl *dao.DeployChangeLog, method pbevent.DeployChangeLogEvent_Method) error
	GetChangeLogFailReason(ctx context.Context, clId int64, clStatus constants.DeployStatus, clAction constants.DeployAction) string
	AutoHandleDeployFailed(ctx context.Context, params *pbdep.AutoHandleDeployFailedReq) error
	ImageDeploy(ctx context.Context, req *model.ImageDeployReq) (int64, error)
	SampleDeploy(ctx context.Context, req *model.SampleDeployReq) error
	GetCurrentChangeLog(ctx context.Context, req *model.RunningChangeLogParams) (*model.ChangeLog, error)
}

func NewDeployChangeLogService(repo dao.DeployChangeLogRepository, configRepo dao.DeployConfigRepository,
	ticketRepo dao.TicketRepository, deployRecordRepo dao.DeployRecordRepository,
	appClient pbapp.AppServiceClient, sender event.Sender,
	cloudAggCli aggregate.AggClient, argo argo.ArgoClient, redisCli *redis.Client, ecClient eventcenter.Service,
	configService DeployConfigService, deployEventDispatcher dispatcher.AppDeployEventDispatcher,
	tm envtarget.TrafficMarker, pipelineRunCli pipelinepb.PipelineRunServiceClient,
	planSvc DeployPlanService) *DeployChangeLogSvc {
	return &DeployChangeLogSvc{
		repo:                  repo,
		configRepo:            configRepo,
		ticketRepo:            ticketRepo,
		deployRecordRepo:      deployRecordRepo,
		appClient:             appClient,
		eventSender:           sender,
		cloudAggClient:        cloudAggCli,
		argo:                  argo,
		redisCli:              redisCli,
		eventCenterClient:     ecClient,
		configSvc:             configService,
		deployEventDispatcher: deployEventDispatcher,
		tm:                    tm,
		pipelineRunCli:        pipelineRunCli,
		planSvc:               planSvc,
	}
}

type DeployChangeLogSvc struct {
	repo                  dao.DeployChangeLogRepository
	configRepo            dao.DeployConfigRepository
	ticketRepo            dao.TicketRepository
	deployRecordRepo      dao.DeployRecordRepository
	appClient             pbapp.AppServiceClient
	eventSender           event.Sender
	argo                  argo.ArgoClient
	redisCli              *redis.Client
	eventCenterClient     eventcenter.Service
	configSvc             DeployConfigService
	cloudAggClient        aggregate.AggClient
	deployEventDispatcher dispatcher.AppDeployEventDispatcher
	tm                    envtarget.TrafficMarker
	pipelineRunCli        pipelinepb.PipelineRunServiceClient
	planSvc               DeployPlanService
}

func (s *DeployChangeLogSvc) CreateChangeLog(ctx context.Context, req *model.CreateChangeLogReq) (int64, error) {
	if req.OperatedAt.IsZero() {
		req.OperatedAt = time.Now()
	}

	c, err := s.configRepo.GetConfig(ctx, req.ConfigID)
	if mustNotNil(c, err) != nil {
		log.ErrorWithCtx(ctx, "创建变更记录，获取config[%d]，发生异常: %v", req.ConfigID, err)
		return 0, err
	}
	m, err := s.configRepo.GetMetadata(ctx, c.MetadataID)
	if mustNotNil(m, err) != nil {
		log.ErrorWithCtx(ctx, "创建变更记录，获取metadata[%d]，发生异常: %v", c.MetadataID, err)
		return 0, err
	}

	// 每个环境的cluster必不一样，cluster对应env，env与ticketType也可一一对应，因此可确定某个环境的工单
	// 但同一时间同个环境服务是否会有多张工单？
	t, err := s.ticketRepo.GetTicketBy(&model.AppTicketQuery{
		TaskRunId: req.TicketRelatedTaskRunID,
		Status:    constants.TicketApproved.String(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "创建变更记录，查找相关工单，发生异常: %v", err)
		return 0, err
	}
	var description string
	switch req.TaskRunType {
	case constants.TASK_DEV_ENV_IMAGE_SYNC.String(), constants.TASK_TEST_ENV_IMAGE_SYNC.String():
		description = "生产环境部署后，基准环境同步更新"
		break
	default:
		if t == nil {
			description = req.Description
		} else {
			description = t.Reason
		}
	}

	appInfo, err := s.appClient.GetAppInfo(ctx, &pbapp.GetAppInfoReq{AppId: m.AppID})
	if err != nil {
		log.ErrorWithCtx(ctx, "创建变更记录 appClient.GetAppInfo Err: %v", err)
		return 0, err
	}

	cl := buildChangeLog(m, c, req)
	cl.Description = description
	cl.ProjectID = appInfo.GetProject().GetId()
	if req.Senv != "" {
		cl.Senv = req.Senv
	}
	if req.EnvTag != "" {
		cl.EnvTarget = constants.EnvTargetType(req.EnvTag).ToEnumEnvTarget()
	}
	// 现在创建只有部署用着，回滚和下线都是单独的接口
	cl.Action = constants.DeployActionDeploy
	if err = s.repo.CreateChangeLog(ctx, cl); err != nil {
		return 0, err
	}

	// 通过redis key判断当前部署记录是否存在已经运行中的版本，第一次为部署，后续都为升级
	if err := s.setRunningChangeLogKey(ctx, cl); err != nil {
		return 0, err
	}
	if err := s.SendEvent(ctx, cl, pbevent.DeployChangeLogEvent_CREATE); err != nil {
		log.ErrorWithCtx(ctx, "发送变更记录[%d]事件，发生异常: %v", cl.ID, err)
		return 0, err
	}
	return cl.ID, nil
}

func (s *DeployChangeLogSvc) setRunningChangeLogKey(ctx context.Context, cl *dao.DeployChangeLog) error {
	rcl, err := s.repo.FindRunningChangeLog(ctx, &model.RunningChangeLogParams{
		AppID:     cl.AppID,
		Cluster:   cl.Cluster,
		Namespace: cl.Namespace,
		EnvTarget: cl.EnvTarget,
	})
	if err != nil {
		return err
	}

	existedVal := 1
	if rcl == nil {
		existedVal = 0
	}
	key := fmt.Sprintf("deploy:id-%d", cl.ID)
	if redisErr := s.redisCli.Set(ctx, key, existedVal, 7*24*time.Hour).Err(); redisErr != nil {
		log.ErrorWithCtx(ctx, "记录变更记录[%d]值[%d]，发生异常: %v", cl.ID, existedVal, redisErr)
		return redisErr
	}

	return nil
}

func (s *DeployChangeLogSvc) UpdateChangeLogStatus(ctx context.Context, req *model.UpdateChangeLogStatusReq) error {
	var cl *dao.DeployChangeLog
	var err error
	if req.TaskRunID != 0 {
		log.InfoWithCtx(ctx, "[UpdateChangeLogStatus] 更新变更记录状态为`%v`, taskRunID=%d", req.Status, req.TaskRunID)
		cl, err = s.repo.UpdateChangeLogStatusByTrIDAndSrID(ctx, req.TaskRunID, req.SubtaskId, req.Status)
	} else {
		log.InfoWithCtx(ctx, "[UpdateChangeLogStatus] 更新变更记录状态为`%v`, id=%d", req.Status, req.ID)
		cl, err = s.repo.UpdateChangeLogStatusByID(ctx, req.ID, req.Status)
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "更新变更记录[%d]状态为[%v], 发生异常", req.TaskRunID, req.Status)
		return err
	}
	log.DebugWithCtx(ctx, "[UpdateChangeLogStatus] changelog %d updated %+v", cl.ID, *cl)

	// 部署/更新/回滚发送事件至事件中心
	s.sendEventToEventCenter(context.Background(), restartReq{clID: cl.ID, isRestart: false})

	return s.SendEvent(ctx, cl, pbevent.DeployChangeLogEvent_UPDATE)
}

func (s *DeployChangeLogSvc) SendEvent(ctx context.Context, cl *dao.DeployChangeLog, method pbevent.DeployChangeLogEvent_Method) error {
	eData := buildChangeLogEvent(cl, method)
	log.DebugWithCtx(ctx, "[SendEvent] build changelog %d event %+v", cl.ID, eData)
	result := s.eventSender.Send(ctx, event.NewDeployRelatedEvent(eData, event.WithEventType("changelog")))
	if event.IsUndelivered(result) {
		log.ErrorWithCtx(ctx, "[SendEvent] 发送变更记录[%d]事件，发生异常: %v", cl.ID, result.Error())
		return fmt.Errorf("发送变更记录[%d]事件，发生异常: %v", cl.ID, result.Error())
	}
	if event.IsACK(result) {
		log.InfoWithCtx(ctx, "[SendEvent] 发送变更记录[%d]事件成功", cl.ID)
	}

	return nil
}

type restartReq struct {
	isRestart bool
	startTime time.Time
	clID      int64
}

// sendEventToEventCenter 推送数据至事件中心
func (s *DeployChangeLogSvc) sendEventToEventCenter(ctx context.Context, req restartReq) {
	safego.Go(func() {
		_ = s.pushToEventCenter(ctx, req)
	})
}

func (s *DeployChangeLogSvc) pushToEventCenter(ctx context.Context, req restartReq) error {
	cl, err := s.repo.FindChangeLogByID(ctx, req.clID)
	if err != nil || cl == nil {
		log.ErrorWithCtx(ctx, "根据ID[%d]查询变更记录，发生异常: %v, %v", req.clID, err, cl)
		return err
	}

	isRunningLog, err := s.getRunningChangeLogKey(ctx, cl)
	if err != nil {
		return err
	}

	// 变更记录状态为SUCCESSFUL才推送事件
	if cl.Status != constants.DeployStatusSuccessful {
		log.DebugWithCtx(ctx, "变更记录[%d]状态为: %v", cl.ID, cl.Status)
		return nil
	}

	log.DebugWithCtx(ctx, "开始发送部署事件[%d]至事件中心", cl.ID)
	var action eventcenter.DeployAction
	operatedAt := cl.OperatedAt
	if req.isRestart {
		action = eventcenter.DeployActionRestart
		operatedAt = req.startTime
	} else {
		switch cl.Action {
		case constants.DeployActionDeploy:
			if !isRunningLog {
				action = eventcenter.DeployActionDeploy
			} else {
				action = eventcenter.DeployActionUpgrade
			}
		case constants.DeployActionRollback:
			action = eventcenter.DeployActionRollback
		case constants.DeployActionOffline:
			action = eventcenter.DeployActionOffline
		}
	}

	eventSource := eventcenter.EventSource{
		EventID:     cl.ID,
		AppId:       cl.AppID,
		Action:      action,
		StartTime:   operatedAt,
		EndTime:     time.Now(),
		Description: cl.Description,
		Cluster:     cl.Cluster,
		Namespace:   cl.Namespace,
	}

	return s.eventCenterClient.SendEvent(ctx, eventSource)
}

func (s *DeployChangeLogSvc) getRunningChangeLogKey(ctx context.Context, cl *dao.DeployChangeLog) (bool, error) {
	// 通过redis key值判断当前变更记录在部署前是否已经有运行中的记录
	key := fmt.Sprintf("deploy:id-%d", cl.ID)
	result := s.redisCli.Get(ctx, key)
	if result.Err() != redis.Nil {
		isExistedNum, err := result.Int64()
		if err != nil {
			log.ErrorWithCtx(ctx, "数据转换错误, val: %v, err: %v", result, err)
			return false, err
		}
		if err := s.redisCli.Del(ctx, key).Err(); err != nil {
			log.ErrorWithCtx(ctx, "删除key[%v]发生异常: %v", key, err)
			return false, err
		}

		if isExistedNum == 1 {
			return true, nil
		}
	}

	return false, nil
}

func (s *DeployChangeLogSvc) ListChangeLog(ctx context.Context, query *model.ListChangeLogReq) (*page.Page, error) {
	log.DebugWithCtx(ctx, "获取变更记录列表请求参数: %+v", query)
	var total int64
	if query.OperatedStart != "" && query.OperatedEnd != "" {
		query.OperatedAt = []string{query.OperatedStart, query.OperatedEnd}
	}

	data, err := s.repo.ListChangeLog(ctx, query, &total)
	if err != nil {
		log.ErrorWithCtx(ctx, "获取变更记录列表数据，发生异常: %v", err)
		return nil, err
	}

	results := make([]model.ChangeLog, len(data))
	for i, v := range data {
		result := s.buildChangeLogResp(ctx, v)
		results[i] = result
	}

	resp := &page.Page{
		PageNum:     query.PageNum(),
		PageSize:    query.PageSize(),
		List:        &results,
		TotalRecord: total,
	}
	return resp, nil
}

func (s *DeployChangeLogSvc) RunningChangeLogs(ctx context.Context, query model.RunningChangeLogsReq) (objs []model.ChangeLog, err error) {
	// 这里比较恶心
	// 预发布的preview环境，需要拿出原先灰度的环境+生产环境staging标签的部署记录出来
	// 生产环境，不要拿生产环境staging标签的部署记录出来
	list, err := s.repo.ListCurrentlyRunningVersion(context.Background(), &model.ListCurrentlyRunningVersionReq{
		AppsId:    []int64{query.AppId},
		Env:       query.DeployEnv.Value(),
		EnvTarget: query.EnvTarget.ToEnumEnvTarget(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "DeployChangeLogSvc RunningChangeLogs Err %v", err)
		return nil, err
	}
	for _, changeLog := range list {
		objs = append(objs, s.buildChangeLogResp(ctx, changeLog))
	}
	return
}

func generateAppPriority(level string) string {
	if level == "" {
		return "prod-p4"
	}
	return fmt.Sprintf("prod-%v", level)
}

func getContainerName(ctx context.Context, projectId int64, appName string) string {
	exists := tools.Any(conf.DeployConfig.Deploy.Projects, func(DefaultProjectId int64) bool {
		return DefaultProjectId == projectId
	})
	if exists {
		return appName
	}
	return conf.DeployConfig.Deploy.DefaultContainerName
}

type LogResults struct {
	Results []Result `json:"results"`
}

type Logs struct {
	LogKey []PodContainers `json:"logkey"`
}

type Result struct {
	// Name the given name
	Name string `json:"name"`
	// Value the given value of the result
	Value string `json:"value"`
}

type PodContainers struct {
	PodName        string   `json:"-"`
	ContainerNames []string `json:""`
}

func getLogKey(pipelineRunName string) []byte {
	var results []Result
	data := Logs{
		LogKey: []PodContainers{
			{
				PodName:        fmt.Sprintf("%s-%s-pod", pipelineRunName, "render"),
				ContainerNames: []string{"step-render-manifest", "step-packege-push"},
			},
			{
				PodName:        fmt.Sprintf("%s-%s-pod", pipelineRunName, "argo-deploy"),
				ContainerNames: []string{"step-login"},
			},
		},
	}
	result := make(map[string][]map[string][]string)

	result["logkey"] = make([]map[string][]string, len(data.LogKey))

	for i, v := range data.LogKey {
		result["logkey"][i] = map[string][]string{v.PodName: v.ContainerNames}
	}

	jsonRes, _ := json.Marshal(result)
	results = append(results, Result{
		Name:  "logkey",
		Value: string(jsonRes),
	})
	logResult := LogResults{Results: results}
	resultJson, _ := json.Marshal(logResult)

	return resultJson
}

func (s *DeployChangeLogSvc) RollbackChangeLog(ctx context.Context, req *model.RollbackReq) error {
	targetCL, err := s.repo.FindChangeLogByID(ctx, req.ID)
	if err != nil || targetCL == nil {
		return fmt.Errorf("%s: ChangeLog not found", err)
	}
	if !targetCL.CanRollback() {
		return deperr.ErrChangeLogCanNotBeRollback
	}
	operator := cctx.GetUserinfo(ctx)
	rollbackCL := targetCL.CopyNew(constants.DeployActionRollback, constants.DeployStatusRollbacking, req.Description).
		SetOperator(operator.UserID, operator.ChineseName, operator.EmployeeNo)
	err = s.repo.CreateChangeLog(ctx, rollbackCL)
	if err != nil {
		return fmt.Errorf("%s: CreateChangeLog failed", err)
	}

	appConfig, err := s.configSvc.GetDeployConfigById(ctx, rollbackCL.ConfigID)
	if err != nil {
		return errors.Wrapf(err, "get deploy config %d failed", rollbackCL.ConfigID)
	}

	deployTimeout, err := s.getTaskRunTimeout(ctx, rollbackCL.TaskRunID)
	if err != nil {
		return err
	}
	err = s.simpleDeploy(ctx, rollbackCL, appConfig, deployTimeout)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeployChangeLogSvc RollbackChangeLog Err %v", err)
		return errors.Wrapf(err, "rollback deploy failed")
	}

	log.DebugWithCtx(ctx, "rollback to changelog %d %+v", rollbackCL.ID, *rollbackCL)

	return nil
}

func mustNotNil(data any, err error) error {
	if data == nil {
		return errors.New("查询数据为nil")
	}

	return err
}

func (s *DeployChangeLogSvc) buildChangeLogResp(ctx context.Context, changeLog dao.DeployChangeLog) model.ChangeLog {
	pipelineId, buildNumber := int64(0), int64(0)
	failReason := s.GetChangeLogFailReason(ctx, changeLog.ID, changeLog.Status, changeLog.Action)
	if changeLog.TaskRunID != 0 {
		pipelineObj, _ := s.pipelineRunCli.GetPipelineRunTask(ctx, &pipelinepb.GetPRTaskReq{Tid: changeLog.TaskRunID})
		if pipelineObj.Id != 0 {
			pipelineId = pipelineObj.PipelineId
			buildNumber = pipelineObj.PipelineBuildNumber
		}
	}
	return model.ChangeLog{
		ID:                    changeLog.ID,
		Env:                   changeLog.GetEnvType(),
		EnvTarget:             changeLog.GetEnvTarget(),
		Cluster:               changeLog.Cluster,
		Namespace:             changeLog.Namespace,
		AppID:                 changeLog.AppID,
		AppName:               changeLog.AppName,
		ConfigID:              changeLog.ConfigID,
		ConfigVersion:         changeLog.ConfigVersion,
		TaskRunId:             changeLog.TaskRunID,
		PipelineId:            pipelineId,
		BuildNumber:           buildNumber,
		MetadataID:            changeLog.MetadataID,
		IsCurrent:             changeLog.IsCurrent,
		Description:           changeLog.Description,
		Status:                changeLog.Status.String(),
		ArtifactVersion:       changeLog.ArtifactVersion,
		OperatorBy:            changeLog.OperatorBy,
		OperatorByChineseName: changeLog.OperatorByChineseName,
		OperatorByEmployeeNo:  changeLog.OperatorByEmployeeNo,
		OperatedAt:            changeLog.OperatedAt,
		Senv:                  changeLog.Senv,
		Branch:                changeLog.Branch,
		Action:                changeLog.Action,
		FailReason:            failReason,
		ProjectID:             changeLog.ProjectID,
	}
}

func buildChangeLog(m *dao.DeployMetadata, c *dao.DeployConfig, req *model.CreateChangeLogReq) *dao.DeployChangeLog {
	return &dao.DeployChangeLog{
		Env:                   constants.EnumEnv(m.Env),
		EnvTarget:             constants.EnumEnvTarget(m.EnvTarget),
		Cluster:               m.Cluster,
		Namespace:             m.Namespace,
		AppID:                 m.AppID,
		AppName:               m.AppName,
		ConfigID:              c.ID,
		ConfigVersion:         c.Version,
		ConfigType:            constants.DeployConfigType(c.ConfigType),
		MetadataID:            m.ID,
		OperatorBy:            req.OperatedBy,
		OperatorByChineseName: req.ChineseName,
		OperatorByEmployeeNo:  req.EmployeeNo,
		ArtifactVersion:       req.ArtifactVersion,
		TaskRunID:             req.TaskRunID,
		ImageUrl:              req.ImageUrl,
		OperatedAt:            req.OperatedAt,
		SubtaskRunId:          req.SubtaskRunId,
		Senv:                  m.Senv,
		Branch:                req.Branch,
	}
}

func buildChangeLogEvent(cl *dao.DeployChangeLog, method pbevent.DeployChangeLogEvent_Method) *pbevent.DeployChangeLogEvent {
	e := &pbevent.DeployChangeLogEvent{
		Id:                    cl.ID,
		Env:                   int32(cl.Env),
		EnvTarget:             int32(cl.EnvTarget),
		Cluster:               cl.Cluster,
		Namespace:             cl.Namespace,
		AppId:                 cl.AppID,
		AppName:               cl.AppName,
		ConfigId:              cl.ConfigID,
		TaskRunId:             cl.TaskRunID,
		Status:                int32(cl.Status),
		IsCurrent:             cl.IsCurrent,
		Description:           cl.Description,
		OperatorBy:            cl.OperatorBy,
		OperatorByChineseName: cl.OperatorByChineseName,
		OperatorByEmployeeNo:  cl.OperatorByEmployeeNo,
		OperatedAt:            timestamppb.New(cl.OperatedAt),
		Action:                int64(cl.Action),
		MetadataId:            cl.MetadataID,
		ConfigVersion:         int64(cl.ConfigVersion),
		ArtifactVersion:       cl.ArtifactVersion,
		Branch:                cl.Branch,
		Method:                method,
		Senv:                  cl.Senv,
		ProjectId:             cl.ProjectID,
	}
	return e
}

// Restart 重启服务
func (s *DeployChangeLogSvc) Restart(ctx context.Context, req *model.RestartReq) error {
	changeLog, err := s.repo.FindChangeLogByID(ctx, req.ReleaseID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[Restart] failed to find changeLog, err: %v, id: %d", err, req.ReleaseID)
		return err
	}
	if changeLog == nil {
		log.ErrorWithCtx(ctx, "[Restart] changeLog not found, err: %v, id: %d", err, req.ReleaseID)
		return err
	}
	resourceName := constants.GenerateGeneralComponentName(changeLog.AppName, changeLog.EnvTarget.ToEnvTargetType(), changeLog.Senv)
	//call cloud api to restart
	reReq := &constack.RestartRequest{
		Cluster:   changeLog.Cluster,
		Namespace: changeLog.Namespace,
		Name:      resourceName,
		Kind:      changeLog.GetConfigTypeWorkloadKind(),
	}
	startTime := time.Now()
	if _, err = s.cloudAggClient.Restart(ctx, reReq); err != nil {
		log.ErrorWithCtx(ctx, "[Restart] failed to restart, err: %v, id: %d", err, req.ReleaseID)
		return err
	}

	// 服务重启推送数据至事件中心
	s.sendEventToEventCenter(context.Background(), restartReq{clID: changeLog.ID, isRestart: true, startTime: startTime})
	return nil
}

func (s *DeployChangeLogSvc) Retry(ctx context.Context, ID int64) error {
	targetCL, err := s.repo.FindChangeLogByID(ctx, ID)
	if err != nil || targetCL == nil {
		return errors.Wrapf(err, ": retry ChangeLog not found")
	}
	if !targetCL.CanRetry() {
		return errors.Errorf("changeLog %d can not be retry", ID)
	}

	appConfig, err := s.configSvc.GetDeployConfigById(ctx, targetCL.ConfigID)
	if err != nil {
		return errors.Wrapf(err, "get deploy config %d failed", targetCL.ConfigID)
	}

	deployTimeout, err := s.getTaskRunTimeout(ctx, targetCL.TaskRunID)
	if err != nil {
		return err
	}
	operator := cctx.GetUserinfo(ctx)
	targetCL.Status = constants.DeployStatusDeploying
	targetCL.OperatorBy = operator.UserID
	targetCL.OperatorByChineseName = operator.ChineseName
	targetCL.OperatorByEmployeeNo = operator.EmployeeNo
	targetCL.OperatedAt = time.Now()
	updateErr := s.repo.UpdateChangeLogByID(ctx, targetCL.ID, targetCL)
	if updateErr != nil {
		return errors.Wrapf(updateErr, "update changeLog %d failed", targetCL.ID)
	}

	if targetCL.Action == constants.DeployActionOffline {
		if err := s.offlineDeploy(ctx, targetCL, appConfig, 0, false); err != nil {
			return errors.Wrapf(err, "Retry offline failed")
		}
	} else {
		if err := s.simpleDeploy(ctx, targetCL, appConfig, deployTimeout); err != nil {
			return errors.Wrapf(err, "Retry deploy failed")
		}
	}
	// 发送事件
	_ = s.SendEvent(ctx, targetCL, pbevent.DeployChangeLogEvent_UPDATE)

	log.DebugWithCtx(ctx, "Retry changelog %d %+v", targetCL.ID, *targetCL)
	return nil
}

func (s *DeployChangeLogSvc) DeployWithConfig(ctx context.Context, req model.DeployReq) error {
	baseCL, err := s.repo.FindChangeLogByID(ctx, req.ID)
	if err != nil || baseCL == nil {
		return errors.Wrapf(err, ": DeployWithConfig ChangeLog not found")
	}

	config, err := s.configRepo.GetConfig(ctx, req.ConfigID)
	if mustNotNil(config, err) != nil {
		return errors.Wrapf(err, ": DeployWithConfig GetConfig failed")
	}

	appConfig, err := s.configSvc.GetDeployConfigById(ctx, req.ConfigID)
	if err != nil {
		return errors.Wrapf(err, ": get deploy config %d failed", baseCL.ConfigID)
	}
	operator := cctx.GetUserinfo(ctx)
	deployCL := baseCL.CopyNew(constants.DeployActionDeploy, constants.DeployStatusDeploying, req.Description).
		SetOperator(operator.UserID, operator.ChineseName, operator.EmployeeNo).
		SetConfigInfo(config.ID, config.Version, config.GetConfigType())
	err = s.repo.CreateChangeLog(ctx, deployCL)
	if err != nil {
		return errors.Wrapf(err, "DeployWithConfig CreateChangeLog failed")
	}

	// 仅修改配置升级不关联流水线，使用默认值
	timeOut := constants.DefaultDeployTimeout
	if req.Timeout > 0 {
		timeOut = req.Timeout
	}
	if err = s.simpleDeploy(ctx, deployCL, appConfig, timeOut); err != nil {
		return errors.Wrapf(err, "DeployWithConfig deploy failed")
	}
	return nil
}

func (s *DeployChangeLogSvc) getEnvNameAndTrafficMark(ctx context.Context, projectId int64, cluster, namespace string, appConfig *pbdep.GetAppConfigResp) (string, string, error) {
	var environment string
	var environmentMark string
	if set.With(constants.SUB_V2, constants.SUB).Contains(constants.EnvTargetType(appConfig.EnvTarget)) {
		tmOpts := envtarget.GetGetTrafficMarkOptions{
			Cluster:   cluster,
			Namespace: namespace,
			ProjectID: projectId,
			Env:       constants.EnvType(appConfig.Env).ToEnumEnv(),
			Senv:      appConfig.Senv,
			Belong:    constants.EnumSubEnvGroupAll,
		}
		log.DebugWithCtx(ctx, "[getEnvNameAndTrafficMark] get traffic mark params: %+v", tmOpts)
		markName, err := s.tm.GetTrafficMark(ctx, &tmOpts)
		if err != nil {
			log.ErrorWithCtx(ctx, "[getEnvNameAndTrafficMark] get traffic mark failed, err: %v", err)
			return "", "", err
		}
		if markName != nil {
			environmentMark = *markName
		}

		switch constants.EnvTargetType(appConfig.EnvTarget) {
		case constants.SUB_V2:
			environment = appConfig.Senv
		case constants.SUB:
			environment = namespace
		}
	}
	return environment, environmentMark, nil
}

// InitMakeUpDeploy 初始化补偿部署
func (s *DeployChangeLogSvc) InitMakeUpDeploy() {
	deploySetName := fmt.Sprintf("%v-change-log-deploy", conf.DeployConfig.Tekton.Env)
	items, err := s.redisCli.SMembers(context.Background(), deploySetName).Result()
	if err != nil {
		log.ErrorWithCtx(context.Background(), "[InitMakeUpDeploy] redis get deploy change log failed: %v", err)
		return
	}
	log.InfoWithCtx(context.Background(), "[InitMakeUpDeploy] make up deploy items: %v", items)
	safego.Go(func() {
		for _, item := range items {
			clId, _ := strconv.ParseInt(item, 10, 64)
			if clId == 0 {
				continue
			}
			s.clMakeUpDeploy(context.Background(), clId, deploySetName)
		}
	})
}

func (s *DeployChangeLogSvc) clMakeUpDeploy(ctx context.Context, clId int64, deploySetName string) {
	log.InfoWithCtx(ctx, "[clMakeUpDeploy] start make up deploy, clID=%d", clId)
	// 删除 redis 部署记录
	if err := s.redisCli.SRem(context.Background(), deploySetName, clId).Err(); err != nil {
		log.ErrorWithCtx(ctx, "[simpleDeploy] redis delete deploy change log record failed: %v, clID=%d", err, clId)
	}

	cl, err := s.repo.FindChangeLogByID(ctx, clId)
	if err != nil || cl == nil {
		log.ErrorWithCtx(ctx, "[clMakeUpDeploy] find change log failed: %v, clID=%d", err, clId)
		return
	}
	// 操作时间超过10分钟的不再补偿
	if cl.OperatedAt.Add(10 * time.Minute).Before(time.Now()) {
		log.ErrorWithCtx(ctx, "[clMakeUpDeploy] change log operated time expired, clID=%d", clId)
		return
	}
	// cicd-deploy服务生产环境直接更新状态即可
	if isSelfDeploy(cl) {
		log.InfoWithCtx(ctx, "[clMakeUpDeploy] skip redeploy cicd-deploy, start update status cl=%v", cl)
		if err = s.updateSubTaskStatus(ctx, cl.TaskRunID, cl.SubtaskRunId, constants.SUCCESSFUL); err != nil {
			log.ErrorWithCtx(ctx, "[clMakeUpDeploy] update subtask status failed: %v, cl=%v", err, cl)
			return
		}
		req := &model.UpdateChangeLogStatusReq{
			ID:     cl.ID,
			Status: constants.DeployStatusSuccessful,
		}
		if err = s.UpdateChangeLogStatus(ctx, req); err != nil {
			log.ErrorWithCtx(ctx, "[clMakeUpDeploy] update change log status failed: %v, cl=%v", err, cl)
			return
		}
	} else {
		appConfig, err := s.configSvc.GetDeployConfigById(ctx, cl.ConfigID)
		if err != nil {
			log.ErrorWithCtx(ctx, "[clMakeUpDeploy] get deploy config failed: %v, clID=%d, configID=%d", err, clId, cl.ConfigID)
			return
		}
		deployTimeout, err := s.getTaskRunTimeout(ctx, cl.TaskRunID)
		if err != nil {
			log.ErrorWithCtx(ctx, "[clMakeUpDeploy] get task run timeout failed: %v, clID=%d, taskRunID=%d", err, clId, cl.TaskRunID)
			return
		}
		if err = s.simpleDeploy(ctx, cl, appConfig, deployTimeout); err != nil {
			log.ErrorWithCtx(ctx, "[clMakeUpDeploy] simple deploy failed: %v, cl=%v", err, cl)
			return
		}
	}
	log.InfoWithCtx(ctx, "[clMakeUpDeploy] make up deploy success, clID=%d", clId)
}

func isSelfDeploy(cl *dao.DeployChangeLog) bool {
	return (cl.Cluster == conf.DeployConfig.SelfDeploy.Cluster &&
		cl.Namespace == conf.DeployConfig.SelfDeploy.Namespace &&
		cl.AppName == conf.DeployConfig.SelfDeploy.Name &&
		cl.Senv == conf.DeployConfig.SelfDeploy.Senv) ||
		(cl.Cluster == "k8s-tc-bj-cicd-prod" && cl.Namespace == "cicd" && cl.AppName == "cicd-deploy" && cl.Senv == "")
}

// simpleDeploy 是短路径部署的实现
// cl 是变更记录，已经持久化的数据
func (s *DeployChangeLogSvc) simpleDeploy(ctx context.Context, cl *dao.DeployChangeLog, appConfig *pbdep.GetAppConfigResp, deployTimeout int64) error {
	manifest, envTrafficMark, err := s.getDeployConfig(ctx, cl, appConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "[simpleDeploy] %d get deploy config failed: %v", cl.ID, err)
		s.repo.UpdateChangeLogStatusByID(ctx, cl.ID, constants.DeployStatusFailed) // 更新变更记录状态为失败
		return err
	}
	// 协程处理部署流
	safego.Go(func() {
		var deploySetName = fmt.Sprintf("%v-change-log-deploy", conf.DeployConfig.Tekton.Env)
		defer func() {
			// 部署完成删除redis部署记录
			if err = s.redisCli.SRem(context.Background(), deploySetName, cl.ID).Err(); err != nil {
				log.ErrorWithCtx(ctx, "[simpleDeploy] redis delete deploy change log failed: %v, clID=%d", err, cl.ID)
			}
			if r := recover(); r != nil {
				log.ErrorWithCtx(ctx, "[simpleDeploy] simpleDeploy panic: %v", r)
			}
		}()
		// 往redis记录部署记录
		if err = s.redisCli.SAdd(context.Background(), deploySetName, cl.ID).Err(); err != nil {
			log.ErrorWithCtx(ctx, "[simpleDeploy] redis add deploy change log failed: %v, clID=%d", err, cl.ID)
		}

		// deployTimeout 部署rs超时时间
		// 240s 为部署hpa和路由以及处理部署结果的时间
		newCtx, cancelFnc := cctx.CopyCtxWithTimeout(ctx, time.Duration(deployTimeout+240)*time.Second)
		defer cancelFnc()
		// 替换成牵星OAM部署接口
		req := &model.CloudDeployChainReq{
			Manifest:            manifest,
			DeployTimeout:       uint32(deployTimeout),
			TrafficMark:         envTrafficMark,
			NeedOperateDatabase: true,
		}
		_ = s.handleCallCloudDeployChain(newCtx, cl, req)
	})

	return nil
}

func (s *DeployChangeLogSvc) getDeployConfig(ctx context.Context, cl *dao.DeployChangeLog, appConfig *pbdep.GetAppConfigResp) (string, string, error) {
	req := &pbapp.GetDeployMsgReq{Id: cl.AppID, EnvType: cl.Env.String()}
	// 生产预发布，当灰度环境
	if cl.EnvTarget == constants.EnumEnvTargetSubV2 && cl.Senv == constants.STAGING {
		req.EnvType = constants.EnumEnvPreview.String()
	}
	ctx, _ = context.WithTimeout(context.Background(), 5*time.Minute)
	appInfo, err := s.appClient.GetAppDeployMsg(ctx, req)
	if err != nil {
		return "", "", errors.WithMessage(err, "SimpleDeploy get app info failed")
	}
	deployAdvanceConfig := model.DeployAdvanceConfig{
		ProjectId:     appInfo.ProjectId,
		Priority:      generateAppPriority(appInfo.Level),
		AppName:       cl.AppName,
		Namespace:     cl.Namespace,
		ImageName:     cl.ImageUrl,
		ContainerName: getContainerName(ctx, appInfo.ProjectId, cl.AppName),
		UUID:          appInfo.CmdbId,
		LangName:      appInfo.LangName,
		//Env:           constants.EnvType(appConfig.Env),
		Env:     cl.Env.ToEnvType(),
		Cluster: cl.Cluster,
	}
	deployAdvanceConfig.MatchLabels, deployAdvanceConfig.SerSltLabels = GetAppLabels(appInfo, deployAdvanceConfig, appConfig)
	if cl.Senv != "" {
		appConfig.EnvTarget = constants.SUB_V2.String()
		appConfig.Senv = cl.Senv
	}
	envName, envTrafficMark, err := s.getEnvNameAndTrafficMark(ctx, appInfo.ProjectId, cl.Cluster, cl.Namespace, appConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "[simpleDeploy] get env name and traffic mark failed: %v", err)
		return "", "", err
	}
	for _, dyCfg := range appInfo.DynamicConfigs {
		deployAdvanceConfig.DyCfgs = append(deployAdvanceConfig.DyCfgs, model.DyCfg{
			CfgFile:  dyCfg.FileName,
			ApolloNS: dyCfg.ApolloNs,
			IsGlobal: dyCfg.IsGlobal,
		})
	}

	variables := tpl.DynamicVariable{
		ServiceName:           cl.AppName,
		ImageName:             cl.ImageUrl,
		DyeingEnvironment:     envName,
		DyeingEnvironmentMark: envTrafficMark,
	}
	manifest, err := tpl.DeployConfigToManifest(appConfig, deployAdvanceConfig, &variables)
	if err != nil {
		log.ErrorWithCtx(ctx, "部署服务，渲染模板，发生异常异常: %v", err)
		return "", "", errors.WithMessage(err, "SimpleDeploy render manifest failed")
	}

	return manifest, envTrafficMark, nil
}

func (s *DeployChangeLogSvc) handleCallCloudDeployChain(ctx context.Context, cl *dao.DeployChangeLog, req *model.CloudDeployChainReq) error {
	// 更新流水线状态为running
	if err := s.updateSubTaskStatus(ctx, cl.TaskRunID, cl.SubtaskRunId, constants.RUNNING); err != nil {
		log.ErrorWithCtx(ctx, "[handleCallCloudDeployChain] update subtask status failed, err: %v, clID=%d", err, cl.ID)
		return err
	}

	var (
		executeErr    error
		clStatus      constants.DeployStatus
		subTaskStatus constants.PipelineStatus
	)
	defer func() {
		// 部署失败自动回滚到当前运行中的版本，且非停止部署失败
		if clStatus == constants.DeployStatusFailed && executeErr != deperr.ErrChainStoppedByUser {
			log.InfoWithCtx(ctx, "[handleCallCloudDeployChain] call auto rollback current, clID=%d", cl.ID)
			rollbackReq := &pbdep.AutoHandleDeployFailedReq{
				AppId:     cl.AppID,
				AppName:   cl.AppName,
				Cluster:   cl.Cluster,
				Namespace: cl.Namespace,
				Senv:      cl.Senv,
			}
			if err := s.AutoHandleDeployFailed(ctx, rollbackReq); err != nil {
				log.ErrorWithCtx(ctx, "[handleCallCloudDeployChain] auto rollback current failed, err: %v, clID=%d", err, cl.ID)
			}
		}
	}()

	executeErr = s.cloudDeployChain(ctx, cl, req)
	if executeErr != nil {
		clStatus = constants.DeployStatusFailed
		subTaskStatus = constants.FAILED
	} else {
		clStatus = constants.DeployStatusSuccessful
		subTaskStatus = constants.SUCCESSFUL
	}

	// 停止部署后重试后之前的运行不更新流水线状态
	latestRecord, recordErr := s.deployRecordRepo.FindLatestDeployRecord(ctx, cl.ID)
	if recordErr != nil {
		log.ErrorWithCtx(ctx, "[handleCallCloudDeployChain] find latest deploy record failed, err: %v, clID=%d", recordErr, cl.ID)
		return recordErr
	}
	isUpdateStatus := true
	if latestRecord != nil && latestRecord.Number != req.CurrentCLNumber {
		log.InfoWithCtx(ctx, "[handleCallCloudDeployChain] not need to update status, clID=%d, latestNumber=%d, currentNumber=%d", cl.ID, latestRecord.Number, req.CurrentCLNumber)
		isUpdateStatus = false
	}
	// 发送事件
	cl.Status = clStatus
	switch cl.Action {
	case constants.DeployActionDeploy:
		_ = s.deployEventDispatcher.PublishDeployEvent(ctx, cl)
	case constants.DeployActionRollback:
		_ = s.deployEventDispatcher.PublishRollbackEvent(ctx, cl)

	}

	if isUpdateStatus {
		// 更新流水线状态
		var updatedErr error
		// 重试两次，每次间隔5s，适配pipeline服务更新的情况
		for i := 0; i < 2; i++ {
			if updatedErr = s.updateSubTaskStatus(ctx, cl.TaskRunID, cl.SubtaskRunId, subTaskStatus); updatedErr == nil {
				break
			}
			log.InfoWithCtx(ctx, "[handleCallCloudDeployChain] update subtask status failed, retry %d, err: %v, clID=%d", i+1, updatedErr, cl.ID)
			time.Sleep(5 * time.Second)
		}
		if updatedErr != nil {
			log.ErrorWithCtx(ctx, "[handleCallCloudDeployChain] update subtask status failed, err: %v, clID=%d", updatedErr, cl.ID)
			return updatedErr
		}

		// 更新变更记录状态
		updatedReq := &model.UpdateChangeLogStatusReq{
			ID:     cl.ID,
			Status: clStatus,
		}
		if err := s.UpdateChangeLogStatus(ctx, updatedReq); err != nil {
			log.ErrorWithCtx(ctx, "[handleCallDeployOAMChain] update changelog status failed, err: %v, clID: %d", err, cl.ID)
			return err
		}
	}

	return nil
}

func (s *DeployChangeLogSvc) updateSubTaskStatus(ctx context.Context, prTaskId, prSubTaskId int64, status constants.PipelineStatus) error {
	if prTaskId > 0 && prSubTaskId > 0 {
		req := pipelinepb.UpdateSubTaskStatusReq{
			TaskRunId:    prTaskId,
			SubTaskRunId: prSubTaskId,
			Status:       status.String(),
		}
		_, err := s.pipelineRunCli.UpdateSubTaskStatus(ctx, &req)
		if err != nil {
			log.ErrorWithCtx(ctx, "[updateSubTaskStatus] update subtask status failed, err: %v", err)
			return err
		}
	}
	return nil
}

func (s *DeployChangeLogSvc) cloudDeployChain(ctx context.Context, cl *dao.DeployChangeLog, req *model.CloudDeployChainReq) error {
	log.InfoWithCtx(ctx, "[cloudDeployChain] start deploy chain, clID=%d, req: %+v", req)

	dr, err := s.deployRecordRepo.FindLatestDeployRecord(ctx, cl.ID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[cloudDeployChain] find latest deploy record failed, err: %v, clID=%d", err, cl.ID)
		return err
	}
	number := int64(1)
	if dr != nil {
		// dr 不为空，说明是重试
		number = dr.Number + 1
	}
	req.CurrentCLNumber = number

	taskRun, err := s.pipelineRunCli.GetPipelineRunTask(ctx, &pipelinepb.GetPRTaskReq{Tid: cl.TaskRunID})
	if err != nil {
		log.ErrorWithCtx(ctx, "[cloudDeployChain] get pipeline run task failed, err: %v, clID=%d", err, cl.ID)
		return err
	}

	var canaryConfig model.CanaryPolicyConfig
	// 子环境为canary查询关联的任务金丝雀策略，任务里的配置来源于 升级工单
	if cl.Senv == constants.CANARY && cl.TaskRunID > 0 {
		if err := json.Unmarshal(taskRun.Config, &canaryConfig); err != nil {
			log.ErrorWithCtx(ctx, "[cloudDeployChain] 序列化配置信息错误, err: %v, clID=%d", err, cl.ID)
			return fmt.Errorf("序列化解析出错：%v", err)
		}
	}

	var dpPlan model.DeployPlanResp
	if canaryConfig.CanaryPolicy == constants.PolicyClientVersion && canaryConfig.CanaryDeployPlanID > 0 {
		plan, err := s.planSvc.GetDeployPlan(ctx, canaryConfig.CanaryDeployPlanID)
		if err != nil {
			log.ErrorWithCtx(ctx, "[cloudDeployChain] GetDeployPlan, err: %v, clID=%d", err, cl.ID)
			return err
		}
		dpPlan = *plan
	}

	appName := cl.AppName
	cluster := cl.Cluster
	configID := cl.ConfigID
	envTarget := constants.TransferToEnvTargetType(int8(cl.EnvTarget))
	senv := cl.Senv
	namespace := cl.Namespace
	subNamespace := ""
	switch cl.EnvTarget {
	case constants.EnumEnvTargetSub:
		subNamespace = cl.Namespace
	}

	resp, err := s.configSvc.GetDeployConfigById(ctx, configID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[cloudDeployChain] get deploy config failed, err: %v", err)
		return err
	}
	if resp == nil {
		log.ErrorWithCtx(ctx, "[cloudDeployChain] get deploy config nil, configID: %d", configID)
		return fmt.Errorf("deploy config %d not found", configID)

	}

	// senv 不为空的情况下，EnvTarget 需要以请求为准，不能用configID 代表的。[金丝雀 使用基准环境的cfg来部署的]
	if senv != "" {
		resp.EnvTarget = constants.SUB_V2.String()
		resp.Senv = cl.Senv
	}

	m := chain.New(s.cloudAggClient, s.argo, s.deployRecordRepo, s.repo, s.pipelineRunCli, s.redisCli)
	m.SetChangelogID(cl.ID)
	m.SetNumber(number)
	m.SetDeployTimeout(req.DeployTimeout)
	m.EnableCloudCDOnline() // 牵星部署标识
	if req.NeedOperateDatabase {
		m.EnableOperateDatabase()
	}

	sidecarHandler := chain.SidecarOnlineHandle(&chain.SidecarHandleRequest{
		AppID:     cl.AppID,
		AppName:   appName,
		Cluster:   cluster,
		Env:       cl.Env,
		EnvTarget: cl.EnvTarget,
		Namespace: namespace,
		Senv:      senv,
	})
	m.Register(chain.StepNameSidecar, sidecarHandler)

	cloudCDHandler := chain.CloudCDOnlineHandle(&chain.CloudCDHandleRequest{
		Manifest:      req.Manifest,
		DeployTimeout: req.DeployTimeout,
		Cluster:       cl.Cluster,
		ClAction:      cl.Action,
		ClSubTaskID:   cl.SubtaskRunId,
	})
	m.Register(chain.StepNameCloudCD, cloudCDHandler)

	hpaHandler := chain.HPAOnlineHandle(&chain.HPAHandleRequest{
		ServiceName: appName,
		Cluster:     cluster,
		Namespace:   namespace,
		TraitConfig: resp.GetTraitConfig(),
		Senv:        senv,
		EnvTarget:   constants.EnvTargetType(resp.GetEnvTarget()),
		ConfigType:  constants.DeployConfigType(resp.ConfigType),
	})
	m.Register(chain.StepNameHPA, hpaHandler)

	routingHandler := chain.RoutingOnlineHandle(&routing.HandleRequest{
		ServiceName:   appName,
		EnvTarget:     envTarget,
		Cluster:       cluster,
		Namespace:     namespace,
		SubNamespace:  subNamespace,
		Senv:          senv,
		TrafficMark:   req.TrafficMark,
		Config:        resp.GetAppConfig(),
		ConfigType:    constants.DeployConfigType(resp.GetConfigType()),
		CanaryPolicy:  canaryConfig.CanaryPolicy,
		DeployPlan:    dpPlan,
		PipelineRunID: taskRun.PipelineRunId,
	})
	m.Register(chain.StepNameRouting, routingHandler)

	err = m.Execute(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "[cloudDeployChain] execute deploy chain failed, err: %v", err)
		return err
	}

	log.InfoWithCtx(ctx, "[cloudDeployChain] deploy chain success")
	return nil
}

// getTaskRunTimeout 查询任务超时时长，单位为s
func (s *DeployChangeLogSvc) getTaskRunTimeout(ctx context.Context, taskRunId int64) (int64, error) {
	if taskRunId == 0 {
		return constants.DefaultDeployTimeout, nil
	}
	taskRun, err := s.pipelineRunCli.GetPipelineRunTask(ctx, &pipelinepb.GetPRTaskReq{Tid: taskRunId})
	if err != nil {
		log.ErrorWithCtx(ctx, "[getTaskRunTimeout] call grpc get task run error: %v", err)
		return 0, err
	}

	type timeout struct {
		Timeout int64 `json:"timeout"`
	}
	var config timeout
	if err = json.Unmarshal(taskRun.Config, &config); err != nil {
		log.Errorf("Unmarshal Json config error: %v", err)
		return 0, err
	}

	log.DebugWithCtx(ctx, "[getTaskRunTimeout] pipelineRunTask[%d] timeout is %d", taskRunId, config.Timeout)
	if config.Timeout == 0 {
		return constants.DefaultDeployTimeout, nil
	}

	return config.Timeout * 60, nil
}

func (s *DeployChangeLogSvc) offlineDeploy(ctx context.Context, cl *dao.DeployChangeLog, appConfig *pbdep.GetAppConfigResp, runningChangeLogId int64, needUpdatePipeline bool) error {
	manifest, _, err := s.getDeployConfig(ctx, cl, appConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "[offlineDeploy] get deploy config failed: %v", err)
		return err
	}
	// 协程处理下线流
	safego.Go(func() {
		newCtx, cancelFnc := cctx.CopyCtxWithTimeout(ctx, time.Duration(constants.DefaultDeployTimeout+240)*time.Second)
		defer cancelFnc()
		req := &model.CloudOfflineChainReq{
			Manifest:            manifest,
			DeployTimeout:       uint32(constants.DefaultDeployTimeout),
			RunningChangeLogID:  runningChangeLogId,
			NeedUpdatePipeline:  needUpdatePipeline,
			NeedOperateDatabase: true,
		}
		_ = s.handleCallCloudOfflineChain(newCtx, cl, req)
	})
	return nil
}

func (s *DeployChangeLogSvc) handleCallCloudOfflineChain(ctx context.Context, cl *dao.DeployChangeLog, req *model.CloudOfflineChainReq) error {
	// 更新流水线状态为running
	if req.NeedUpdatePipeline {
		if err := s.updateSubTaskStatus(ctx, cl.TaskRunID, cl.SubtaskRunId, constants.RUNNING); err != nil {
			log.ErrorWithCtx(ctx, "[handleCallCloudOfflineChain] update subtask status failed, err: %v, clID=%d", err, cl.ID)
			return err
		}
	}

	err := s.cloudOfflineChain(ctx, cl, req)
	var clStatus constants.DeployStatus
	var subTaskStatus constants.PipelineStatus
	if err != nil {
		clStatus = constants.DeployStatusFailed
		subTaskStatus = constants.FAILED
	} else {
		clStatus = constants.DeployStatusSuccessful
		subTaskStatus = constants.SUCCESSFUL
	}
	// 更新流水线状态
	if req.NeedUpdatePipeline {
		if updatedErr := s.updateSubTaskStatus(ctx, cl.TaskRunID, cl.SubtaskRunId, subTaskStatus); updatedErr != nil {
			log.ErrorWithCtx(ctx, "[handleCallCloudOfflineChain] update subtask status failed, err: %v, clID=%d", updatedErr, cl.ID)
			return updatedErr
		}
	}

	// 发送事件
	cl.Status = clStatus
	_ = s.deployEventDispatcher.PublishOfflineEvent(ctx, cl)
	// 服务下线推送数据至事件中心
	s.sendEventToEventCenter(context.Background(), restartReq{clID: cl.ID, isRestart: false})

	if err != nil {
		// 更新下线记录失败状态
		updatedReq := &model.UpdateChangeLogStatusReq{
			ID:     cl.ID,
			Status: clStatus,
		}
		if updatedErr := s.UpdateChangeLogStatus(ctx, updatedReq); updatedErr != nil {
			log.ErrorWithCtx(ctx, "[handleCallCloudOfflineChain] update changelog status failed, err: %v, clID: %d", updatedErr, cl.ID)
			return updatedErr
		}
	} else {
		runningChangeLogId := req.RunningChangeLogID

		// 如果 runningChangeLogId 为0，说明是从流水线下线的，需要查询运行中的变更记录
		if runningChangeLogId == 0 {
			runningChangeLog, err := s.repo.FindRunningChangeLog(ctx, &model.RunningChangeLogParams{
				AppID:     cl.AppID,
				Cluster:   cl.Cluster,
				Namespace: cl.Namespace,
				Senv:      cl.Senv,
				EnvTarget: cl.EnvTarget,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "[handleCallCloudOfflineChain] find running change log failed, err: %v", err)
				return err
			}
			if runningChangeLog != nil {
				runningChangeLogId = runningChangeLog.ID
			}
		}
		// 1. 更新 newChangeLog 状态为已完成
		// 2. 更新 运行中的changeLog 的 is_current = false
		if updatedErr := s.repo.Offline(ctx, runningChangeLogId, cl.ID); updatedErr != nil {
			log.ErrorWithCtx(ctx, "[handleCallCloudOfflineChain] failed to offline, err: %v, id: %d", updatedErr, cl.ID)
			return updatedErr
		}

		return s.SendEvent(ctx, cl, pbevent.DeployChangeLogEvent_UPDATE)
	}

	return nil
}

func (s *DeployChangeLogSvc) cloudOfflineChain(ctx context.Context, cl *dao.DeployChangeLog, req *model.CloudOfflineChainReq) error {
	log.InfoWithCtx(ctx, "[cloudOfflineChain] start offline chain, req: %+v", req)

	dr, err := s.deployRecordRepo.FindLatestDeployRecord(ctx, cl.ID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[cloudOfflineChain] find latest deploy record failed, err: %v, clID=%d", err, cl.ID)
		return err
	}
	number := int64(1)
	if dr != nil {
		// dr 不为空说明是重试，number 需要 +1
		number = dr.Number + 1
	}

	routingHandler := chain.RoutingOfflineHandle(&routing.HandleRequest{
		ServiceName:  cl.AppName,
		EnvTarget:    cl.EnvTarget.ToEnvTargetType(),
		Cluster:      cl.Cluster,
		Namespace:    cl.Namespace,
		SubNamespace: "", // 下线路由逻辑会自动识别处理子环境命名空间
		Senv:         cl.Senv,
		ConfigType:   cl.ConfigType,
	})
	hpaHandler := chain.HPAOfflineHandle(&chain.HPAHandleRequest{
		ServiceName: cl.AppName,
		Cluster:     cl.Cluster,
		Namespace:   cl.Namespace,
		Senv:        cl.Senv,
		EnvTarget:   cl.EnvTarget.ToEnvTargetType(),
		ConfigType:  cl.ConfigType,
	})
	cloudCDHandler := chain.CloudCDOfflineHandle(&chain.CloudCDHandleRequest{
		Manifest:      req.Manifest,
		DeployTimeout: req.DeployTimeout,
		Cluster:       cl.Cluster,
		ClAction:      cl.Action,
		ClSubTaskID:   cl.SubtaskRunId,
	})

	m := chain.New(s.cloudAggClient, s.argo, s.deployRecordRepo, s.repo, s.pipelineRunCli, s.redisCli)
	// 这里处理的记录要放到下线的 cl 中
	m.SetChangelogID(cl.ID)
	m.SetNumber(number)
	if req.NeedOperateDatabase {
		m.EnableOperateDatabase()
	}
	m.Register(chain.StepNameRouting, routingHandler)
	m.Register(chain.StepNameHPA, hpaHandler)
	m.Register(chain.StepNameCloudCD, cloudCDHandler)

	err = m.Execute(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "[cloudOfflineChain] execute deploy chain failed, err: %v", err)
		return err
	}

	log.InfoWithCtx(ctx, "[cloudOfflineChain] offline chain success")

	return nil
}

func (s *DeployChangeLogSvc) ListDeployRecord(ctx context.Context, changeLogID int64) ([]model.DeployRecord, error) {
	req := &model.DeployRecordQuery{
		DeployChangeLogID: changeLogID,
		Step:              []string{chain.StepNameArgo, chain.StepNameCloudCD, chain.StepNameSidecar},
	}
	records, err := s.deployRecordRepo.FindDeployRecordBy(ctx, req)
	if err != nil {
		return nil, err
	}
	// 如果没有找到记录，再去找 routing 的记录 <---AI给的注释，对是对的，代码是这个意思，不用注释都能看明白
	// 没到StepNameCloudCD 这一步，一定是下线失败了
	if len(records) == 0 {
		req.Step = []string{chain.StepNameRouting}
		records, err = s.deployRecordRepo.FindDeployRecordBy(ctx, req)
		if err != nil {
			return nil, err
		}
	}

	recordMap := make(map[int64]struct{})
	resp := make([]model.DeployRecord, 0)
	for _, record := range records {
		if _, ok := recordMap[record.DeployChangeLogID]; ok {
			continue
		}
		recordMap[record.DeployChangeLogID] = struct{}{}
		resp = append(resp, model.DeployRecord{
			ID:                record.ID,
			DeployChangeLogID: record.DeployChangeLogID,
			Number:            record.Number,
		})
	}
	return resp, nil
}

func (s *DeployChangeLogSvc) SimpleDeploy(ctx context.Context, changeLog *dao.DeployChangeLog, appConfig *pbdep.GetAppConfigResp, deployTimeout int64) error {
	return s.simpleDeploy(ctx, changeLog, appConfig, deployTimeout)
}

func (s *DeployChangeLogSvc) OfflineDeploy(ctx context.Context, changeLog *dao.DeployChangeLog, appConfig *pbdep.GetAppConfigResp, runningChangeLogId int64, needUpdatePipeline bool) error {
	return s.offlineDeploy(ctx, changeLog, appConfig, runningChangeLogId, needUpdatePipeline)
}

func (s *DeployChangeLogSvc) CheckIsRunning(ctx context.Context, req *model.CheckReq) (bool, error) {
	log.InfoWithCtx(ctx, "检查当前环境是否有正在部署中的任务,参数: %v", req)
	runningChangeLog, err := s.repo.FindChangeLogBy(ctx, &model.ChangeLogParams{
		AppID:     req.AppId,
		Cluster:   req.Cluster,
		Namespace: req.Namespace,
		EnvTarget: req.EnvTarget.Value(),
		Status:    []int8{constants.DeployStatusDeploying.Value(), constants.DeployStatusRollbacking.Value()},
		Senv:      &req.Senv,
		PastTime:  time.Now().Add(-60 * time.Minute),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "查找当前环境部署中的任务失败:%v", err)
		return true, err
	}
	if len(runningChangeLog) > 0 {
		return true, nil
	}
	return false, nil
}

func (s *DeployChangeLogSvc) CheckEventlinkIsRunning(ctx context.Context, req *model.EventlinkCheckReq) (bool, error) {
	log.InfoWithCtx(ctx, "[CheckEventlinkIsRunning] check eventlink is running, req: %+v", req)
	isCurrent := 1
	params := &model.ChangeLogParams{
		AppID:     req.AppId,
		Env:       req.Env.Value(),
		IsCurrent: &isCurrent,
	}
	currentChangeLog, err := s.repo.FindChangeLogBy(ctx, params)
	if err != nil {
		log.ErrorWithCtx(ctx, "[CheckEventlinkIsRunning] list current change log list error: %v, req: %+v", err, params)
		return true, err
	}
	log.InfoWithCtx(ctx, "[CheckEventlinkIsRunning] list current change log list length: %v", len(currentChangeLog))
	if len(currentChangeLog) > 0 {
		for _, cl := range currentChangeLog {
			params = &model.ChangeLogParams{
				AppID:     req.AppId,
				Cluster:   cl.Cluster,
				Namespace: cl.Namespace,
				EnvTarget: cl.EnvTarget.Value(),
				Status:    []int8{constants.DeployStatusDeploying.Value(), constants.DeployStatusRollbacking.Value()},
				Senv:      &cl.Senv,
				PastTime:  time.Now().Add(-60 * time.Minute),
			}
			runningChangeLog, runErr := s.repo.FindChangeLogBy(ctx, params)
			if runErr != nil {
				log.ErrorWithCtx(ctx, "[CheckEventlinkIsRunning] check current running log error: %v, req: %+v", runErr, params)
				return true, runErr
			}
			if len(runningChangeLog) > 0 {
				return true, nil
			}
		}
	}

	return false, nil
}

func (s *DeployChangeLogSvc) Detail(ctx context.Context, req *model.ReplicasDetailReq) (*model.DetailResp, error) {
	operator := cctx.GetUserinfo(ctx)
	//call cloud api to detail
	detailReq := &constack.DetailRequest{
		Username: operator.Username,
	}
	if req.ReleaseID != 0 {
		changeLog, err := s.repo.FindChangeLogByID(ctx, req.ReleaseID)
		if err != nil || changeLog == nil {
			log.ErrorWithCtx(ctx, "[Detail] failed to find change log: %v, changeLogID: [%d]", err, req.ReleaseID)
			return nil, fmt.Errorf("ChangeLog not found, %v", err)
		}
		resourceName := constants.GenerateGeneralComponentName(changeLog.AppName, changeLog.EnvTarget.ToEnvTargetType(), changeLog.Senv)
		detailReq.Cluster, detailReq.Namespace, detailReq.Name, detailReq.Kind = changeLog.Cluster, changeLog.Namespace, resourceName, changeLog.GetConfigTypeWorkloadKind()
	} else {
		deployConfig, err := s.configRepo.GetConfig(ctx, req.ConfigId)
		if err != nil {
			return nil, err
		}
		resourceName := constants.GenerateGeneralComponentName(req.AppName, req.EnvTarget, req.Senv)
		detailReq.Cluster, detailReq.Namespace, detailReq.Name, detailReq.Kind = req.Cluster, req.Namespace, resourceName, deployConfig.GetConfigType().ToWorkloadKind()
	}
	log.InfoWithCtx(ctx, "[Detail] query: %+v", detailReq)
	resp, err := s.cloudAggClient.Detail(ctx, detailReq)
	if err != nil {
		if status.Code(err) == codes.NotFound {
			log.ErrorWithCtx(ctx, "[Detail] failed to get detail, deployment not existed, query: %+v", detailReq)
			return nil, deperr.ErrDeploymentNotExisted
		}
		log.ErrorWithCtx(ctx, "[Detail] failed to get detail, err: %v, query: %+v", err, detailReq)
		return nil, err
	}

	return &model.DetailResp{Url: resp.Url}, nil
}

func (s *DeployChangeLogSvc) ListStepLog(ctx context.Context, req *model.ListStepLogReq) (*model.ListStepLogResp, error) {
	// 因为之前的做法，导致这个查询分两个情况，一个是在流水线处，通过 trID/strID 查询；一个是日志弹窗通过 clID 查询
	// 因此要把 trID/strID 转 clID
	var (
		err error
		cl  *dao.DeployChangeLog
	)
	if req.SubtaskRunID != 0 {
		log.InfoWithCtx(ctx, "[ListStepLog] subtask run id exists, converted to cl id，subtaskRunID=%d", req.SubtaskRunID)
		cl, err = s.repo.FindChangeLogBySubTaskRunID(ctx, req.SubtaskRunID)
	} else if req.TaskRunID != 0 {
		log.InfoWithCtx(ctx, "[ListStepLog] task run id exists, converted to cl id，trID=%d", req.TaskRunID)
		cl, err = s.repo.FindChangeLogByTaskRunID(ctx, req.TaskRunID)
	} else {
		// 回滚的场景
		log.InfoWithCtx(ctx, "[ListStepLog] cl id exists, clID=%d", req.ChangelogID)
		cl, err = s.repo.FindChangeLogByID(ctx, req.ChangelogID)
	}
	if err != nil {
		// 之前写的人里面没判断，现在先不改
		if err == gorm.ErrRecordNotFound {
			log.ErrorWithCtx(ctx, "[ListStepLog] change log not found, trID=%d, strID=%d", req.TaskRunID, req.SubtaskRunID)
			// 这里跟前端先约定了，不抛500，抛404
			return nil, apierrors.New(fmt.Sprintf("change log not found, trID=%d, strID=%d", req.TaskRunID, req.SubtaskRunID), 404)
		}
		log.ErrorWithCtx(ctx, "[ListStepLog] failed to find change log, err: %v, trID=%d, strID=%d", err, req.TaskRunID, req.SubtaskRunID)
		return nil, err
	}
	if cl != nil {
		req.ChangelogID = cl.ID
	}

	log.DebugWithCtx(ctx, "[ListStepLog] trID=%d, clID=%d", req.TaskRunID, req.ChangelogID)
	if req.ChangelogID == 0 {
		log.ErrorWithCtx(ctx, "[ListStepLog] changelog id is empty")
		return nil, fmt.Errorf("changelog id is empty")
	}

	recordsReq := &model.DeployRecordQuery{
		DeployChangeLogID: req.ChangelogID,
	}
	if req.Number > 0 {
		recordsReq.Number = req.Number
	}
	rs, err := s.deployRecordRepo.FindDeployRecordBy(ctx, recordsReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[ListStepLog] failed to list deploy record, err: %v, clID=%d", err, req.ChangelogID)
		return nil, err
	}

	stepLogs := make([]model.StepLog, len(rs))
	for i, r := range rs {
		var recordResult map[string]interface{}
		_ = json.Unmarshal(r.Result, &recordResult)
		logResult := string(r.Result)
		if r.Step == chain.StepNameCloudCD {
			if _, ok := recordResult["message"]; ok {
				logResult = recordResult["message"].(string)
			}
			// 如果部署失败且message还没记录日志，通过流获取cloudCD日志
			if logResult == "{}" && cl.Status == constants.DeployStatusFailed && cl.Action == constants.DeployActionDeploy {
				streamName := chain.GenDeployStreamName(cl.ID, cl.Action, r.Number)
				history, err := s.redisCli.LRange(context.Background(), streamName, 0, -1).Result()
				if err != nil {
					log.ErrorWithCtx(ctx, "[ListStepLog] get cloudCD log failed, err: %v, clID=%d, number=%d", err, req.ChangelogID, r.Number)
				} else {
					logResult = strings.Join(history, "")
					logResult = fmt.Sprintf("%s\n------\n%s", logResult, deperr.ErrChainStoppedByUser.Error())
				}
			}
		}
		stepLogs[i] = model.StepLog{
			Step:   r.Step,
			Number: r.Number,
			Status: r.Status,
			Result: logResult,
		}
	}
	return &model.ListStepLogResp{Logs: stepLogs}, nil
}

const (
	UnknownFailed = "错误原因请查看日志详情"
)

func (s *DeployChangeLogSvc) GetChangeLogFailReason(ctx context.Context, clId int64, clStatus constants.DeployStatus, clAction constants.DeployAction) string {
	if clStatus != constants.DeployStatusFailed {
		return ""
	}

	dr, err := s.deployRecordRepo.FindLatestDeployRecord(ctx, clId)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetChangeLogFailReason] find latest deploy record failed, err: %v, clID=%d", err, clId)
		return UnknownFailed
	}
	if dr == nil {
		return UnknownFailed
	}

	var (
		actionName string
		reason     string
	)
	if clAction == constants.DeployActionOffline {
		actionName = "下线"
	} else {
		actionName = "创建"
	}

	log.DebugWithCtx(ctx, "[GetChangeLogFailReason] step=%s, clId=%d, drId=%d", dr.Step, clId, dr.ID)

	switch dr.Step {
	case chain.StepNameArgo, chain.StepNameCloudCD:
		reason = fmt.Sprintf("deployment%v失败", actionName)
	case chain.StepNameHPA:
		reason = fmt.Sprintf("HPA%v失败", actionName)
	case chain.StepNameRouting:
		reason = fmt.Sprintf("路由%v失败", actionName)
	case chain.StepNameSidecar:
		reason = fmt.Sprintf("sidecar%v失败", actionName)
	default:
		reason = UnknownFailed
	}

	return reason
}

func (s *DeployChangeLogSvc) AutoHandleDeployFailed(ctx context.Context, params *pbdep.AutoHandleDeployFailedReq) error {
	log.InfoWithCtx(ctx, "[AutoHandleDeployFailed] start auto rollback current running, req: %+v", params)
	runningChangeLog, err := s.repo.FindRunningChangeLog(ctx, &model.RunningChangeLogParams{
		AppID:     params.GetAppId(),
		Cluster:   params.GetCluster(),
		Namespace: params.GetNamespace(),
		Senv:      params.GetSenv(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "[AutoHandleDeployFailed] find running change log failed, err: %v, req: %+v", err, params)
		return err
	}
	if runningChangeLog == nil {
		log.InfoWithCtx(ctx, "[AutoHandleDeployFailed] current running change log not found, req: %+v", params)
		// 判断集群上是否存在正在运行的pod

		return nil
	}

	log.InfoWithCtx(ctx, "[AutoHandleDeployFailed] current running change log found, req: %+v", params)
	return s.autoRollbackCurrent(ctx, runningChangeLog)
}

// autoRollbackCurrent 自动回滚当前运行中的版本记录
func (s *DeployChangeLogSvc) autoRollbackCurrent(ctx context.Context, cl *dao.DeployChangeLog) error {
	appConfig, err := s.configSvc.GetDeployConfigById(ctx, cl.ConfigID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[AutoRollbackCurrent] get deploy config failed: %v", err)
		return errors.Wrapf(err, "get deploy config %d failed", cl.ConfigID)
	}

	deployTimeout, err := s.getTaskRunTimeout(ctx, cl.TaskRunID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[AutoRollbackCurrent] get task run timeout failed: %v", err)
		return err
	}

	// 执行部署，不需要更新流水线状态和变更记录状态
	manifest, envTrafficMark, err := s.getDeployConfig(ctx, cl, appConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "[AutoRollbackCurrent] get deploy config failed: %v", err)
		return err
	}
	// 协程处理部署流
	safego.Go(func() {
		newCtx, cancelFnc := cctx.CopyCtxWithTimeout(ctx, time.Duration(deployTimeout+240)*time.Second)
		defer cancelFnc()
		// 替换成牵星OAM部署接口
		req := &model.CloudDeployChainReq{
			Manifest:            manifest,
			DeployTimeout:       uint32(deployTimeout),
			TrafficMark:         envTrafficMark,
			NeedOperateDatabase: false,
		}
		if err = s.cloudDeployChain(newCtx, cl, req); err != nil {
			log.ErrorWithCtx(ctx, "[AutoRollbackCurrent] cloudDeployChain failed: %v", err)
		}
	})

	log.InfoWithCtx(ctx, "[AutoRollbackCurrent] call auto rollback current running success, cl: %+v", cl)

	return nil
}

func (s *DeployChangeLogSvc) ImageDeploy(ctx context.Context, req *model.ImageDeployReq) (int64, error) {
	regex := regexp.MustCompile(`^cr\.ttyuyin\.com/.*$`)
	if !regex.MatchString(req.ImageUrl) {
		log.ErrorWithCtx(ctx, "[ImageDeploy] image address invalid: %s", req.ImageUrl)
		return 0, deperr.ErrImageAddressInvalid
	}
	images := strings.Split(req.ImageUrl, "/")
	var artifactVersion string
	artifactVersion = images[len(images)-1]
	config, err := s.configRepo.GetConfig(ctx, req.ConfigID)
	if mustNotNil(config, err) != nil {
		log.ErrorWithCtx(ctx, "[ImageDeploy] GetConfig failed: %v, configID: %d", err, req.ConfigID)
		return 0, errors.Wrapf(err, ": ImageDeploy GetConfig failed")
	}

	appConfig, err := s.configSvc.GetDeployConfigById(ctx, req.ConfigID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[ImageDeploy] get deploy config %d failed: %v", req.ConfigID, err)
		return 0, errors.Wrapf(err, ": get deploy config %d failed", req.ConfigID)
	}

	appInfo, err := s.appClient.GetAppInfo(ctx, &pbapp.GetAppInfoReq{AppId: req.AppID})
	if err != nil {
		log.ErrorWithCtx(ctx, "[ImageDeploy] appClient.GetAppInfo %d failed: %v", req.AppID, err)
		return 0, errors.Wrapf(err, ": appClient.GetAppInfo %d failed", req.AppID)
	}

	operator := cctx.GetUserinfo(ctx)
	deployCL := &dao.DeployChangeLog{
		Env:                   req.Env.ToEnumEnv(),
		EnvTarget:             req.EnvTarget.ToEnumEnvTarget(),
		Cluster:               req.Cluster,
		Namespace:             req.Namespace,
		AppID:                 req.AppID,
		AppName:               req.AppName,
		ConfigID:              req.ConfigID,
		ConfigVersion:         config.Version,
		ConfigType:            constants.DeployConfigType(config.ConfigType),
		MetadataID:            config.MetadataID,
		Description:           req.Description,
		Status:                constants.DeployStatusDeploying,
		ArtifactVersion:       artifactVersion,
		ImageUrl:              req.ImageUrl,
		OperatorBy:            operator.UserID,
		OperatorByChineseName: operator.ChineseName,
		OperatorByEmployeeNo:  operator.EmployeeNo,
		OperatedAt:            time.Now(),
		Senv:                  req.Senv,
		Action:                constants.DeployActionDeploy,
		ProjectID:             appInfo.GetProject().GetId(),
	}
	if err = s.repo.CreateChangeLog(ctx, deployCL); err != nil {
		log.ErrorWithCtx(ctx, "[ImageDeploy] CreateChangeLog failed: %v", err)
		return 0, errors.Wrapf(err, "ImageDeploy CreateChangeLog failed")
	}

	if req.TimeOut <= 0 {
		req.TimeOut = constants.DefaultDeployTimeout
	}
	// 镜像部署不关联流水线，使用默认值
	if err = s.simpleDeploy(ctx, deployCL, appConfig, req.TimeOut); err != nil {
		return 0, errors.Wrapf(err, "ImageDeploy deploy failed")
	}

	return deployCL.ID, nil
}

func (s *DeployChangeLogSvc) SampleDeploy(ctx context.Context, req *model.SampleDeployReq) error {
	query := &model.DeployChangeLogQuery{
		AppID:     conf.DeployConfig.Vela.SampleAppID,
		Action:    constants.DeployActionDeploy,
		EnvTarget: constants.EnumEnvTargetOrigin, // 只查基准环境的
		Status:    constants.DeployStatusSuccessful,
	}
	sampleLog, err := s.repo.FindByCondition(ctx, query)
	if err != nil {
		log.ErrorWithCtx(ctx, "[SampleDeploy] find sample deploy log failed: %v, req: %+v", err, query)
		return err
	}
	if sampleLog == nil {
		log.ErrorWithCtx(ctx, "[SampleDeploy] sample deploy log not found, req: %+v", query)
		return errors.Wrapf(err, ": sample change log not found")
	}

	config, err := s.configRepo.GetConfig(ctx, sampleLog.ConfigID)
	if mustNotNil(config, err) != nil {
		log.ErrorWithCtx(ctx, "[SampleDeploy] GetConfig failed: %v, configID: %d", err, sampleLog.ConfigID)
		return errors.Wrapf(err, ": SampleDeploy GetConfig failed")
	}

	appConfig, err := s.configSvc.GetDeployConfigById(ctx, sampleLog.ConfigID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[SampleDeploy] get deploy config %d failed: %v", sampleLog.ConfigID, err)
		return errors.Wrapf(err, ": get deploy config %d failed", sampleLog.ConfigID)
	}

	// 部署
	deployChangeLogs := make([]*dao.DeployChangeLog, 0)
	operator := cctx.GetUserinfo(ctx)
	for _, deployEnv := range req.Envs {
		deployCL := &dao.DeployChangeLog{
			Env:                   deployEnv.Env.ToEnumEnv(),
			EnvTarget:             deployEnv.EnvTarget.ToEnumEnvTarget(),
			Cluster:               deployEnv.Cluster,
			Namespace:             deployEnv.Namespace,
			AppID:                 req.AppID,
			AppName:               req.AppName,
			ConfigID:              sampleLog.ConfigID,
			ConfigVersion:         config.Version,
			ConfigType:            constants.DeployConfigType(config.ConfigType),
			MetadataID:            config.MetadataID,
			Description:           "样例部署，以使用本地开发、Eventlink等基于子环境的功能",
			Status:                constants.DeployStatusDeploying,
			ArtifactVersion:       sampleLog.ArtifactVersion,
			ImageUrl:              sampleLog.ImageUrl,
			OperatorBy:            operator.UserID,
			OperatorByChineseName: operator.ChineseName,
			OperatorByEmployeeNo:  operator.EmployeeNo,
			OperatedAt:            time.Now(),
			Senv:                  deployEnv.Senv,
			Action:                constants.DeployActionDeploy,
			ProjectID:             req.ProjectID,
		}
		deployChangeLogs = append(deployChangeLogs, deployCL)
	}
	if err = s.repo.BatchCreateChangeLog(ctx, deployChangeLogs); err != nil {
		log.ErrorWithCtx(ctx, "[SampleDeploy] batch create change log failed: %v", err)
		return errors.Wrapf(err, "batch create change log failed")
	}
	for _, deployCL := range deployChangeLogs {
		log.DebugWithCtx(ctx, "[SampleDeploy] deployCL: %+v", deployCL)
		safego.Go(func() {
			if err = s.simpleDeploy(ctx, deployCL, appConfig, constants.DefaultDeployTimeout); err != nil {
				log.ErrorWithCtx(ctx, "[SampleDeploy] simple deploy failed: %v", err)
			}
		})

	}

	return nil
}

func (s *DeployChangeLogSvc) GetCurrentChangeLog(ctx context.Context, req *model.RunningChangeLogParams) (*model.ChangeLog, error) {
	originCl, err := s.repo.FindRunningChangeLog(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetChangeLog] find running change log failed: %v, req: %+v", err, req)
		return nil, err
	}
	if originCl == nil {
		log.ErrorWithCtx(ctx, "[GetChangeLog] change log not found, req: %+v", req)
		return nil, errors.Wrapf(err, "change log not found")
	}
	return &model.ChangeLog{
		ID:                    originCl.ID,
		Env:                   originCl.Env.ToEnvType(),
		EnvTarget:             originCl.EnvTarget.ToEnvTargetType(),
		Cluster:               originCl.Cluster,
		Namespace:             originCl.Namespace,
		AppID:                 originCl.AppID,
		AppName:               originCl.AppName,
		ConfigID:              originCl.ConfigID,
		ConfigVersion:         originCl.ConfigVersion,
		Description:           originCl.Description,
		Status:                originCl.Status.String(),
		ArtifactVersion:       originCl.ArtifactVersion,
		OperatorBy:            originCl.OperatorBy,
		OperatorByChineseName: originCl.OperatorByChineseName,
		OperatorByEmployeeNo:  originCl.OperatorByEmployeeNo,
		OperatedAt:            originCl.OperatedAt,
		Senv:                  originCl.Senv,
		Branch:                originCl.Branch,
		Action:                originCl.Action,
		ProjectID:             originCl.ProjectID,
		IsCurrent:             true,
	}, nil
}
