package upd_ser

import (
	"sync"

	cloudagg "52tt.com/cicd/pkg/cloud/aggregate"
	"52tt.com/cicd/protocol/iam"
)

// K8S 资源的修改
var UpdateAgg = &updateAgg{}
var one = &sync.Once{}

type updateAgg struct {
	cloudagg cloudagg.AggClient
	userCli  iam.UserServiceClient
}

func InitAgg(cloudagg cloudagg.AggClient, userCli iam.UserServiceClient) {
	one.Do(func() {
		UpdateAgg = &updateAgg{
			cloudagg: cloudagg,
			userCli:  userCli,
		}
	})
	return
}
