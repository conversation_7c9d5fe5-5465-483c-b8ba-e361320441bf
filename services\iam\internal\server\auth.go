package server

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/golang/protobuf/ptypes/empty"

	"52tt.com/cicd/pkg/constants"
	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/token"
	pbiam "52tt.com/cicd/protocol/iam"
	"52tt.com/cicd/services/iam/internal/conf"
	"52tt.com/cicd/services/iam/internal/dao"
	"52tt.com/cicd/services/iam/pkg/sso"
)

var _ pbiam.AuthServiceServer = new(AuthSvr)

func NewAuthService(userRepo dao.UserRepository,
	userPreferRepo dao.UserPreferRepository,
	projectUserRepo dao.ProjectUserRepository,
	authRepo dao.AuthRepository,
	openapiTokenRepo dao.OpenapiTokenRepository,
) *AuthSvr {
	return &AuthSvr{
		userRepo:         userRepo,
		userPreferRepo:   userPreferRepo,
		projectUserRepo:  projectUserRepo,
		authRepo:         authRepo,
		openapiTokenRepo: openapiTokenRepo,
	}
}

type AuthSvr struct {
	pbiam.UnimplementedAuthServiceServer
	userRepo         dao.UserRepository
	userPreferRepo   dao.UserPreferRepository
	projectUserRepo  dao.ProjectUserRepository
	authRepo         dao.AuthRepository
	openapiTokenRepo dao.OpenapiTokenRepository
}

func (as *AuthSvr) GetOwnUserinfo(ctx context.Context, req *pbiam.GetOwnUserinfoReq) (resp *pbiam.GetOwnUserinfoResp, err error) {
	tkn := cctx.Token(ctx)
	if tkn == "" {
		log.Errorf("获取ctx传递的token为空")
		return nil, errors.New("获取ctx传递的token为空")
	}

	hmacGen := token.NewJTWTokenHMAC("cicd-v2", []byte(conf.AppConfig.Sso.Key))
	verifyClaims, err := hmacGen.Verify(tkn)
	if err != nil {
		log.Errorf("校验token异常: %v", err)
		return nil, errors.New("校验token异常")
	}

	u, err := as.userRepo.FindUserByUsername(verifyClaims.Username)
	if err != nil {
		log.Errorf("获取用户自身信息异常: %v", err)
		return nil, err
	}
	if u == nil {
		log.Errorf("用户%s不存在", verifyClaims.Username)
		return nil, errors.New("数据异常，用户信息不存在")
	}

	log.Infof("尝试获取用户%s上次选择的项目记录", u.DisplayUser())
	prefer, err := as.userPreferRepo.FindPreferByUserId(verifyClaims.Uid)
	if err != nil {
		wrapErr := fmt.Errorf("获取用户%s上次选择的项目异常: %v", u.DisplayUser(), err)
		log.Errorf(wrapErr.Error())
		return nil, wrapErr
	}

	currentProject := &pbiam.GetOwnUserinfoRespSelectProject{}
	sysRole := u.GetCurrentRoleName()
	if prefer != nil {
		log.Infof("获得用户%s上次选择的项目id[%d]", u.DisplayUser(), prefer.LatestProjectId)
		ownProjectID := prefer.LatestProjectId

		pu, err := as.projectUserRepo.GetProjectUserBy(ctx, ownProjectID, prefer.UserId)
		if err != nil {
			log.Errorf("获取用户%s拥有的项目[%d]角色信息异常: %v", u.DisplayUser(), ownProjectID, err)
			return nil, err
		}

		if pu == nil && sysRole != string(constants.ADMIN) {
			log.Errorf("获取用户%s拥有的项目[%d]角色信息异常，用户存在项目但没有角色: %v", u.DisplayUser(), ownProjectID, err)
			// 存在上一次记录的组权限被回收的情况
		} else {
			currentProject.Id = ownProjectID
			currentProject.Role = sysRole
			if pu != nil {
				currentProject.Role = pu.Role.Name
			}
		}
	} else {
		log.Infof("用户%s没有项目选择记录，将返回空数据", u.DisplayUser())
	}
	resp = &pbiam.GetOwnUserinfoResp{
		HasDataPerms:   true,
		CurrentProject: currentProject,
	}

	if sysRole != string(constants.ADMIN) && req.ProjectID > 0 {
		pua, _ := as.projectUserRepo.GetProjectUserBy(ctx, req.ProjectID, verifyClaims.Uid)
		if pua == nil {
			resp.HasDataPerms = false
		}
	}

	resp.UserInfo = &pbiam.GetOwnUserinfoRespOwnUserInfo{
		Id:          u.ID,
		Username:    u.Username,
		ChineseName: u.ChineseName,
		Email:       u.Email,
		EmployeeNo:  u.EmployeeNo,
		Role:        u.GetCurrentRoleName(),
	}
	return resp, nil
}

func (as *AuthSvr) Logout(ctx context.Context, req *pbiam.LogoutReq) (*pbiam.LogoutResp, error) {
	resp := &pbiam.LogoutResp{}
	// redis set token to disable
	err := as.authRepo.BanToken(ctx, req.Token)
	if err != nil {
		return resp, err
	}

	resp.Url = sso.Logout()
	return resp, nil
}

func (as *AuthSvr) Login(ctx context.Context, req *pbiam.LoginReq) (*pbiam.LoginResp, error) {
	username, err := sso.Login(req.Ticket)
	if err != nil {
		wrapErr := fmt.Errorf("ticket校验异常: %v", err)
		log.Errorf(wrapErr.Error())
		return nil, wrapErr
	}
	return as.GenUserToken(ctx, &pbiam.GenUserTokenReq{UserName: username})
}

func (as *AuthSvr) CheckTokenIsBan(ctx context.Context, req *pbiam.CheckTokenIsBanReq) (*pbiam.CheckTokenIsBanResp, error) {
	result := as.authRepo.CheckTokenIsBan(ctx, req.Token)
	return &pbiam.CheckTokenIsBanResp{
		IsBan: result,
	}, nil
}

func (as *AuthSvr) CheckAuthenticate(ctx context.Context, req *pbiam.CheckAuthenticateReq) (*empty.Empty, error) {
	openapiToken := req.GetToken()
	if openapiToken == "" {
		return &empty.Empty{}, errors.New("token is empty")
	}
	if _, err := as.openapiTokenRepo.CheckAuth(ctx, openapiToken); err != nil {
		return &empty.Empty{}, err
	}
	return &empty.Empty{}, nil
}

func (as *AuthSvr) GenUserToken(ctx context.Context, req *pbiam.GenUserTokenReq) (*pbiam.LoginResp, error) {
	user, err := as.userRepo.FindUserByUsername(req.UserName)
	if err != nil {
		wrapErr := fmt.Errorf("校验用户[%s]发生异常: %v", req.UserName, err)
		log.Errorf(wrapErr.Error())
		return nil, wrapErr
	}
	if user == nil {
		wrapErr := fmt.Errorf("系统没有该用户[%s]", req.UserName)
		log.Errorf(wrapErr.Error())
		return nil, wrapErr
	}

	hmacGen := token.NewJTWTokenHMAC("cicd-v2", []byte(conf.AppConfig.Sso.Key))
	claims := &token.PrivateClaims{
		Uid:      user.ID,
		Username: user.Username,
	}
	if req.Expire == 0 {
		req.Expire = conf.AppConfig.Sso.Expire
	}
	tkn, err := hmacGen.GenerateToken(claims, time.Duration(req.Expire)*time.Second)
	if err != nil {
		wrapErr := fmt.Errorf("签发token异常: %v", err)
		log.Errorf(wrapErr.Error())
		return nil, wrapErr
	}

	return &pbiam.LoginResp{
		Token:   tkn,
		Expires: conf.AppConfig.Sso.Expire,
	}, nil

}
