// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.4
// source: quwan/cloud/constack/v1alpha/scaledobject.proto

package constack

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ScaledObjectService_Save_FullMethodName                  = "/quwan.cloud.constack.v1alpha.ScaledObjectService/Save"
	ScaledObjectService_ScaleWorkloadReplicas_FullMethodName = "/quwan.cloud.constack.v1alpha.ScaledObjectService/ScaleWorkloadReplicas"
)

// ScaledObjectServiceClient is the client API for ScaledObjectService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ScaledObjectServiceClient interface {
	// restart workload
	Save(ctx context.Context, in *SaveRequest, opts ...grpc.CallOption) (*SaveResponse, error)
	// 调整工作负载的副本数，按照 ScaledObject、HPA、Deployment/StatefulSet 三者的优先级进行调整
	ScaleWorkloadReplicas(ctx context.Context, in *ScaleWorkloadReq, opts ...grpc.CallOption) (*ScaleWorkloadResp, error)
}

type scaledObjectServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewScaledObjectServiceClient(cc grpc.ClientConnInterface) ScaledObjectServiceClient {
	return &scaledObjectServiceClient{cc}
}

func (c *scaledObjectServiceClient) Save(ctx context.Context, in *SaveRequest, opts ...grpc.CallOption) (*SaveResponse, error) {
	out := new(SaveResponse)
	err := c.cc.Invoke(ctx, ScaledObjectService_Save_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scaledObjectServiceClient) ScaleWorkloadReplicas(ctx context.Context, in *ScaleWorkloadReq, opts ...grpc.CallOption) (*ScaleWorkloadResp, error) {
	out := new(ScaleWorkloadResp)
	err := c.cc.Invoke(ctx, ScaledObjectService_ScaleWorkloadReplicas_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ScaledObjectServiceServer is the server API for ScaledObjectService service.
// All implementations must embed UnimplementedScaledObjectServiceServer
// for forward compatibility
type ScaledObjectServiceServer interface {
	// restart workload
	Save(context.Context, *SaveRequest) (*SaveResponse, error)
	// 调整工作负载的副本数，按照 ScaledObject、HPA、Deployment/StatefulSet 三者的优先级进行调整
	ScaleWorkloadReplicas(context.Context, *ScaleWorkloadReq) (*ScaleWorkloadResp, error)
	mustEmbedUnimplementedScaledObjectServiceServer()
}

// UnimplementedScaledObjectServiceServer must be embedded to have forward compatible implementations.
type UnimplementedScaledObjectServiceServer struct {
}

func (UnimplementedScaledObjectServiceServer) Save(context.Context, *SaveRequest) (*SaveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Save not implemented")
}
func (UnimplementedScaledObjectServiceServer) ScaleWorkloadReplicas(context.Context, *ScaleWorkloadReq) (*ScaleWorkloadResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScaleWorkloadReplicas not implemented")
}
func (UnimplementedScaledObjectServiceServer) mustEmbedUnimplementedScaledObjectServiceServer() {}

// UnsafeScaledObjectServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ScaledObjectServiceServer will
// result in compilation errors.
type UnsafeScaledObjectServiceServer interface {
	mustEmbedUnimplementedScaledObjectServiceServer()
}

func RegisterScaledObjectServiceServer(s grpc.ServiceRegistrar, srv ScaledObjectServiceServer) {
	s.RegisterService(&ScaledObjectService_ServiceDesc, srv)
}

func _ScaledObjectService_Save_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScaledObjectServiceServer).Save(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScaledObjectService_Save_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScaledObjectServiceServer).Save(ctx, req.(*SaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScaledObjectService_ScaleWorkloadReplicas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScaleWorkloadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScaledObjectServiceServer).ScaleWorkloadReplicas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScaledObjectService_ScaleWorkloadReplicas_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScaledObjectServiceServer).ScaleWorkloadReplicas(ctx, req.(*ScaleWorkloadReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ScaledObjectService_ServiceDesc is the grpc.ServiceDesc for ScaledObjectService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ScaledObjectService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "quwan.cloud.constack.v1alpha.ScaledObjectService",
	HandlerType: (*ScaledObjectServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Save",
			Handler:    _ScaledObjectService_Save_Handler,
		},
		{
			MethodName: "ScaleWorkloadReplicas",
			Handler:    _ScaledObjectService_ScaleWorkloadReplicas_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "quwan/cloud/constack/v1alpha/scaledobject.proto",
}
