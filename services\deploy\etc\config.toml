[default.app]
name = "deploy-service"
version = 0.1
http.port = 8092
rpc.port = 9002
rpc.timeout = "30s"
mode = "debug"
description = "部署服务"

[default.database]
max_life_time = "30m"
idle_connection = 30
open_connection = 20
parse_time = true
host = "************"
port = 3306
username = "rd_dev"
password = "vRcfj3W#2nGdBeu@"
dbname = "deploy"


[default.logger]
logname = "/tmp/deploy.log"
loglevel = "debug"
max_size = 50
max_age = 30
max_backups = 10
compress = false

[default.registry]
url = "127.0.0.1:9000"
app_rpc_url = "127.0.0.1:9001"
iam_rpc_url = "127.0.0.1:9003"
notify_rpc_url = "127.0.0.1:9004"
pipeline_rpc_url ="127.0.0.1:9005"
tools_rpc_url = "127.0.0.1:9009"

[default.redis]
addr = "**************:6379"
db = 0

[default.kafka]
name = "deploy"
brokers = "***********:9092"
[default.kafka.producer]
topic = "cicd-deploy-event-local"
[default.kafka.consumer]
topics = "cicd-deploy-event-local,cicd-app-event-local"
sub_topics = "cicd-pipeline-event-local"
offsets = "latest"

[timezone]
list = "Asia/Shanghai,Asia/Singapore,Asia/Bahrain,Europe/Berlin"

[tekton]
    host = "http://cicd-testing.ttyuyin.com/b/api/v1/approval/"
    helm_repo = "cr.ttyuyin.com/devops/helm"
    namespace = "default"
    env = "dev"
    registry_url = "cr.ttyuyin.com/devops"

[lark_template]
approval = "ctp_AA6MlvD2kwT4" # 迭代v17 这个配置项可以删掉了，不需要用到了
update = "ctp_AAgl3G0JDIL8" # 迭代v17 这个配置项可以删掉了，不需要用到了
accepting = "ctp_AAglqRqxfm2o" # 迭代v17 这个配置项可以删掉了，不需要用到了
domain = "http://cicd-testing.ttyuyin.com"
ticket = "/#/tickets/processor/detail/id"
# 新
#approval = "ctp_AAmKfJeaOHkL" # 迭代v17 这个配置项可以删掉了，不需要用到了
#update = "ctp_AAmvGiJsdbQI" # 迭代v17 这个配置项可以删掉了，不需要用到了
#accepting = "ctp_AAmvJ8LafBRI" # 迭代v17 这个配置项可以删掉了，不需要用到了

[cloud]
host = "https://alpha-cloud.ttyuyin.com/api/v1/openapi"
token = "Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663"
grpc_target = "alpha-cloud.ttyuyin.com:8100"
deploy_grpc_target = "alpha-cloud.ttyuyin.com:8107"

[dyeing]
types = ["uid", "Sec-WebSocket-Protocol", "客户端版本", "ip"]
env = "testing"
[[dyeing.configure]]
env = "testing"
authority = "testing-dyeing.ttyuyin.com"
host = "************:80"
[[dyeing.organization]]
namespace = "cicd-testing"
projects = [1]
[dyeing.organization.provider]
uid = "cicd-request"
"客户端版本" = "canary-client-version"
cli="req_cli_type_from_header"
market="req_market_id_from_header"
[[dyeing.organization]]
namespace = "tt"
projects = [6,7,14,22]
[dyeing.organization.provider]
uid = "tt-req-uid"
"客户端版本" = "canary-client-version"

#[[dyeing.configure]]
#env = "dev"

[deploy]
projects = [1,2,3,5] # deployment spec.containers.name 采用服务名的项目
default_container_name = "service"

[argo]
serveraddr = "https://argocd-server.argocd.svc.cluster.local:80"
defaultappnamespace = "argocd"
defaultappproject = "default"

[argo.SessionRequest]
password = "pDvQPczTXGoKYhjJ"
username = "admin"

[event_center]
name = "event-center"
brokers = "**********:9092,**********:9092,**********:9092"
[event_center.producer]
topic = "event-hub"

[cmdb]
host="http://dev-yw-cmdb.ttyuyin.com/api/jsonrpc/"
token="iqNOUYbEJnBJFFe2di1n1T87ssTxO1UXRZo5iIHY8Sh119IWQwsgHcQmyo49rWQd"

[rd_log]
host = "https://yw-rd-logs.ttyuyin.com"
# search 已废弃
search = "/api/cls/search"
topic = "rd-testing-svc"
auth = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9ZSI6IlQyNzE2I$@@iwi^ZDDsZ.2!"

[pod_log]
max_line_num = 5

[vela]
vip_mount_path  = "/config;/app/config;/app/etc"  # 兼容argo patch问题，对某些特殊的目录 继续将整个配置文件合并到一个目录里面
rancher_app_list = ["cpp-test"]
tapd_user = "VITufomt"
tapd_secret = "28091543-832D-482A-CFE2-8A6FEFFE4EDA"
sample_app_id = 1

[open.kafka]
name = "deploy"
brokers = "***********:9092"
[open.kafka.producer]
topic = "sentinel-event-local"

[self_deploy]
cluster = "k8s-tc-bj-cicd-prod"
namespace = "cicd"
name = "cicd-deploy"

[tt_di]
host = "https://dev-insight-new.ttyuyin.com"

[tt_db]
host = "https://yw-db.ttyuyin.com"
user = "cicd_user"
pwd = "b5pP4r46qYzrykwy"

[eventlink]
[[eventlink.configure]]
project_id = 1
port = 15678
[[eventlink.configure]]
project_id = 2
port = 9098

[retry]
timeout_days = 30

[sidecar_trigger]
namespace="appsvr,quicksilver,cicd"
