package rest

import (
	"errors"
	"fmt"
	"net/http"
	"time"

	"52tt.com/cicd/services/migrate/internal/new_sentinel"

	"52tt.com/cicd/pkg/para"
	"52tt.com/cicd/pkg/render"
	"52tt.com/cicd/services/migrate/internal/sentinel"
	"github.com/gin-contrib/timeout"
	"github.com/gin-gonic/gin"
)

func AddToolsRoute(route *gin.RouterGroup) {
	tools := route.Group("/tools")
	tools.POST("checkCfgs", checkCfgs)
	tools.POST("testOnly", testOnly)
	tools.GET("auto/test", autoTest, timeout.New(
		timeout.WithTimeout(10*time.Minute), timeout.WithHandler(func(c *gin.Context) {
			c.Next()
		})))
	tools.POST("syncFileImageUrl", syncFileImageUrl)
	tools.POST("syncRoutes", syncRoutes)
	tools.POST("syncToHSY", syncToHSY)
	tools.POST("syncPplToHSY", syncPplToHSY)
	tools.POST("syncStoryRun", syncStoryRun)
	tools.POST("syncEvtLink", syncEvtLink)
	tools.POST("syncAppEventlink", syncAppEventlink)
	tools.POST("syncAppEventlinkByCdLog", syncAppEventlinkByCdLog)
	tools.POST("updateEvtLinkTarget", updateEventLinkTarget)
	tools.POST("clearRedisKey", clearRedisKey)
	tools.POST("findAppOldCfgs", findAppOldCfgs)
	tools.POST("syncAppOldCfgs", syncAppOldCfgs)
	tools.POST("updateChangeLogProjectId", updateChangeLogProjectId)
	tools.POST("syncStoryRunRemark", syncStoryRunRemark)
	tools.POST("completeUserLarkUnionId", completeUserLarkUnionId)
	tools.POST("syncCdRcd", syncCdRcd)
}

func checkCfgs(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "检查部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindJSON,
	)
	var req sentinel.CheckCfgsReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}

	err := sentinel.SingletonAgg.CheckCfgs(req)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok()
}

func testOnly(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "检查部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindJSON,
	)
	var req sentinel.TestOnlyCmd
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}

	data, err := sentinel.SingletonAgg.TestOnly(req)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok(data)
}

func autoTest(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "自动化测试")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindJSON,
	)

	token := ctx.GetHeader("Tokens")
	if token == "" || token != "GpSMptcjsL+EEXIJcG+PFAgS1dkgLntOjS5PhqCX6a7CwF9VElIJNyTwtuf2bn+RX/KcIqPxp/zJ/UYdPiuK4rgeA4riuK4rjMZiuK4riuMxm" {
		ctxWrapper.Failf(http.StatusUnauthorized, "token缺失")
		return
	}

	var req sentinel.TestOnlyCmd
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}

	rst, err := sentinel.SingletonAgg.AutoTest(req)
	if err != nil {
		ctxWrapper.FailError(http.StatusBadRequest, err)
		return
	}
	ctxWrapper.Ok(rst)
}

func syncFileImageUrl(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "同步配置文件中的镜像地址到数据库中")
	err := new_sentinel.SyncImageUrl(ctx)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok()
}

func syncRoutes(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "同步路由信息到数据库中")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.SyncRoutesReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}

	data, err := new_sentinel.SyncRoutes(ctx, req)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok(data)
}

func syncToHSY(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "同步路由信息到数据库中")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.SyncToHSYReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}
	req.Authorization = ctx.GetHeader("Authorization")

	data, err := new_sentinel.SyncToHSY(ctx, req)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok(data)
}

func syncPplToHSY(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "同步路由信息到数据库中")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.SyncPplToHSYReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}

	data, err := new_sentinel.SyncPplToHSY(ctx, req)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok(data)
}

func syncEvtLink(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "同步路由信息到数据库中")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.SyncEvtLinkReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}
	data, err := new_sentinel.SyncEventLink(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusOK, render.Fatal(http.StatusBadRequest, err.Error(), nil))
		return
	}
	if data != "" {
		ctx.JSON(http.StatusOK, render.Fatal(http.StatusBadRequest, data, nil))
		return
	}
	ctx.JSON(http.StatusOK, render.Fatal(http.StatusOK, "success", nil))
}

func syncAppEventlink(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "同步应用的eventlink信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.SyncAppEventlinkReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}

	err := new_sentinel.SyncAppEventlink(ctx, req)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok()
}

func syncStoryRun(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "同步需求运行信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.SyncStoryRunReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}

	data, err := new_sentinel.SyncStoryRun(ctx, req)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok(data)
}
func syncAppEventlinkByCdLog(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "同步应用的eventlink信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.SyncAppEventlinkReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}
	err := new_sentinel.SyncAppEventlinkByCdLog(ctx, req)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok()
}

func updateEventLinkTarget(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "更新EventLink Target信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.UpdateEventlinkTargetReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}
	if err := new_sentinel.UpdateEventlinkTarget(ctx, req); err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok()
}

func clearRedisKey(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "清理Redis Key")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.ClearRedisKeyReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}
	if err := new_sentinel.ClearRedisKey(ctx, &req); err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok()
}

func findAppOldCfgs(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "遍历应用和旧动态配置关系")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.FindOldCfgsReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}
	bf, msg, err := new_sentinel.FindAppOldCfgs(ctx, req)
	if err != nil {
		msg.Code = 400
		msg.Msg = err.Error()
	}

	if !req.NeedExcel {
		ctxWrapper.Ok(msg)
		return
	}

	if bf == nil {
		ctxWrapper.FailError(400, errors.New("写入文件时发生了错误"))
		return
	}

	fileName := fmt.Sprintf("attachment; filename=By_%s.xlsx", time.Now().Format("20060102_1504"))
	ctx.Header("Content-Disposition", fileName)
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", bf.Bytes())
}

func syncAppOldCfgs(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "同步动态配置旧数据")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.SyncConfigsReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}
	msg, err := new_sentinel.SyncAppOldCfgs(ctx, req)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}

	ctxWrapper.Ok(msg)
	return

}

func updateChangeLogProjectId(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "更新ChangeLog ProjectId")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.UpdateChangeLogProjectIdReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}
	if err := new_sentinel.UpdateChangeLogProjectId(ctx, req); err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok()
}

func syncStoryRunRemark(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "同步StoryRun备注")
	err := new_sentinel.SyncSuccessStoryRunRemark(ctx)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok()
}

func completeUserLarkUnionId(c *gin.Context) {
	ctxWrapper := para.WrapperContext(c, true, "补全用户飞书unionId")
	ctxWrapper.RegisterBind(c.ShouldBindJSON)
	var req new_sentinel.CompleteUserLarkUnionIdReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}

	err := new_sentinel.CompleteUserLarkUnionId(c, &req)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}
	ctxWrapper.Ok()
}

func syncCdRcd(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "同步路由信息到数据库中")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindJSON,
	)
	var req new_sentinel.SyncCdRcdReq
	if err := ctxWrapper.Bind(&req); err != nil {
		return
	}
	req.Authorization = ctx.GetHeader("Authorization")

	err := new_sentinel.SyncCDRcd(ctx, req)
	if err != nil {
		ctxWrapper.FailError(400, err)
		return
	}

	ctxWrapper.Ok("OK")
}
