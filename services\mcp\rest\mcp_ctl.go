package rest

import (
	"context"
	"fmt"

	"github.com/invopop/jsonschema"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

func Register(ser *server.MCPServer) {

	ser.AddTool(TTCloudMCP.ToolsRestart(), TTCloudMCP.RestartWorkload)
	ser.AddTool(TTCloudMCP.ToolsRunPipeline(), TTCloudMCP.RunPipeline)
	ser.AddTool(TTCloudMCP.ToolsFindMultiResource(), TTCloudMCP.FindK8SResources)
	ser.AddTool(TTCloudMCP.ToolsKillPod(), TTCloudMCP.KillPod)
	ser.AddTool(TTCloudMCP.ToolsScaleWorkload(), TTCloudMCP.ScaleWorkload)
	ser.AddTool(TTCloudMCP.ToolsFindClusterOrNamespaces(), TTCloudMCP.FindClusterOrNamespaces)

	ser.AddResource(
		mcp.NewResource("test://static/resource", "Static Resource", mcp.WithMIMEType("text/plain")),
		func(ctx context.Context, request mcp.ReadResourceRequest) ([]mcp.ResourceContents, error) {
			return []mcp.ResourceContents{mcp.TextResourceContents{URI: "test://static/resource", MIMEType: "text/plain", Text: "This is a sample resource"}}, nil
		})
	ser.AddResourceTemplate(
		mcp.NewResourceTemplate(
			"test://dynamic/resource/{id}", "Dynamic Resource"),
		func(ctx context.Context, request mcp.ReadResourceRequest) ([]mcp.ResourceContents, error) {
			return []mcp.ResourceContents{mcp.TextResourceContents{URI: request.Params.URI, MIMEType: "text/plain", Text: "This is a sample resource"}}, nil
		},
	)
	ser.AddPrompt(
		mcp.NewPrompt(string("SIMPLE"), mcp.WithPromptDescription("A simple prompt")),
		func(ctx context.Context, request mcp.GetPromptRequest) (*mcp.GetPromptResult, error) {
			return &mcp.GetPromptResult{Description: "A simple prompt without arguments", Messages: []mcp.PromptMessage{{
				Role: mcp.RoleUser, Content: mcp.TextContent{Type: "text", Text: "This is a simple prompt without arguments."}}}}, nil
		})
}

var (
	jsonSchemaReflector = jsonschema.Reflector{
		BaseSchemaID:               "",
		Anonymous:                  true,
		AssignAnchor:               false,
		AllowAdditionalProperties:  true,
		RequiredFromJSONSchemaTags: true,
		DoNotReference:             true,
		ExpandedStruct:             true,
		FieldNameTag:               "",
		IgnoredTypes:               nil,
		Lookup:                     nil,
		Mapper:                     nil,
		Namer:                      nil,
		KeyNamer:                   nil,
		AdditionalFields:           nil,
		CommentMap:                 nil,
	}
)

type ttCloudMCPController struct {
}

var TTCloudMCP = &ttCloudMCPController{}

func (ctl *ttCloudMCPController) ToolsHello() mcp.Tool {
	return mcp.NewTool("hello_world",
		mcp.WithDescription("Say hello to someone"),
		mcp.WithString("name",
			mcp.Required(),
			mcp.Description("Name of the person to greet"),
		))
}

func (ctl *ttCloudMCPController) HelloHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	name, ok := request.Params.Arguments["name"].(string)
	if !ok {
		return mcp.NewToolResultError("name must be a string"), nil
	}

	return mcp.NewToolResultText(fmt.Sprintf("Hello, %s!", name)), nil
}
