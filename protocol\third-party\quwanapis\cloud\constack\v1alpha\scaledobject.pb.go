// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.4
// source: quwan/cloud/constack/v1alpha/scaledobject.proto

package constack

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SaveRequest
type SaveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名, e.g.: k8s-tc-bj-1-yunwei
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间, e.g.: infra
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 服务名, e.g.: cloud-enterprise
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// ScaledObject中的spec, e.g.: `{ "maxReplicaCount": 4,"minReplicaCount": 2}`
	Spec string `protobuf:"bytes,4,opt,name=spec,proto3" json:"spec,omitempty"`
}

func (x *SaveRequest) Reset() {
	*x = SaveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveRequest) ProtoMessage() {}

func (x *SaveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveRequest.ProtoReflect.Descriptor instead.
func (*SaveRequest) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDescGZIP(), []int{0}
}

func (x *SaveRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *SaveRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *SaveRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SaveRequest) GetSpec() string {
	if x != nil {
		return x.Spec
	}
	return ""
}

// SaveResponse
type SaveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveResponse) Reset() {
	*x = SaveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveResponse) ProtoMessage() {}

func (x *SaveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveResponse.ProtoReflect.Descriptor instead.
func (*SaveResponse) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDescGZIP(), []int{1}
}

type ScaleWorkloadReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名, e.g.: k8s-tc-bj-1-yunwei
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间, e.g.: infra
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 服务名, e.g.: cloud-enterprise
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 工作负载种类, e.g.: Deployment或StatefulSet
	Kind string `protobuf:"bytes,4,opt,name=kind,proto3" json:"kind,omitempty"`
	// 副本数, e.g.: 3
	Replicas int32 `protobuf:"varint,5,opt,name=replicas,proto3" json:"replicas,omitempty"`
	// 操作用户名, e.g.: qiujunfeng
	Username string `protobuf:"bytes,6,opt,name=username,proto3" json:"username,omitempty"`
}

func (x *ScaleWorkloadReq) Reset() {
	*x = ScaleWorkloadReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScaleWorkloadReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScaleWorkloadReq) ProtoMessage() {}

func (x *ScaleWorkloadReq) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScaleWorkloadReq.ProtoReflect.Descriptor instead.
func (*ScaleWorkloadReq) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDescGZIP(), []int{2}
}

func (x *ScaleWorkloadReq) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ScaleWorkloadReq) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ScaleWorkloadReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ScaleWorkloadReq) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *ScaleWorkloadReq) GetReplicas() int32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

func (x *ScaleWorkloadReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type ScaleWorkloadResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ScaleWorkloadResp) Reset() {
	*x = ScaleWorkloadResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScaleWorkloadResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScaleWorkloadResp) ProtoMessage() {}

func (x *ScaleWorkloadResp) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScaleWorkloadResp.ProtoReflect.Descriptor instead.
func (*ScaleWorkloadResp) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDescGZIP(), []int{3}
}

var File_quwan_cloud_constack_v1alpha_scaledobject_proto protoreflect.FileDescriptor

var file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2f, 0x73,
	0x63, 0x61, 0x6c, 0x65, 0x64, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1c, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x22,
	0x6d, 0x0a, 0x0b, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x70,
	0x65, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63, 0x22, 0x0e,
	0x0a, 0x0c, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xaa,
	0x01, 0x0a, 0x10, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b,
	0x69, 0x6e, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x13, 0x0a, 0x11, 0x53,
	0x63, 0x61, 0x6c, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x32, 0xf2, 0x01, 0x0a, 0x13, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5f, 0x0a, 0x04, 0x53, 0x61, 0x76, 0x65,
	0x12, 0x29, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e,
	0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x71, 0x75,
	0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x15, 0x53, 0x63, 0x61,
	0x6c, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x73, 0x12, 0x2e, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x52,
	0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x47, 0x5a, 0x45, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e,
	0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2f, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x3b, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDescOnce sync.Once
	file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDescData = file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDesc
)

func file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDescGZIP() []byte {
	file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDescOnce.Do(func() {
		file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDescData = protoimpl.X.CompressGZIP(file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDescData)
	})
	return file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDescData
}

var file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_quwan_cloud_constack_v1alpha_scaledobject_proto_goTypes = []any{
	(*SaveRequest)(nil),       // 0: quwan.cloud.constack.v1alpha.SaveRequest
	(*SaveResponse)(nil),      // 1: quwan.cloud.constack.v1alpha.SaveResponse
	(*ScaleWorkloadReq)(nil),  // 2: quwan.cloud.constack.v1alpha.ScaleWorkloadReq
	(*ScaleWorkloadResp)(nil), // 3: quwan.cloud.constack.v1alpha.ScaleWorkloadResp
}
var file_quwan_cloud_constack_v1alpha_scaledobject_proto_depIdxs = []int32{
	0, // 0: quwan.cloud.constack.v1alpha.ScaledObjectService.Save:input_type -> quwan.cloud.constack.v1alpha.SaveRequest
	2, // 1: quwan.cloud.constack.v1alpha.ScaledObjectService.ScaleWorkloadReplicas:input_type -> quwan.cloud.constack.v1alpha.ScaleWorkloadReq
	1, // 2: quwan.cloud.constack.v1alpha.ScaledObjectService.Save:output_type -> quwan.cloud.constack.v1alpha.SaveResponse
	3, // 3: quwan.cloud.constack.v1alpha.ScaledObjectService.ScaleWorkloadReplicas:output_type -> quwan.cloud.constack.v1alpha.ScaleWorkloadResp
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_quwan_cloud_constack_v1alpha_scaledobject_proto_init() }
func file_quwan_cloud_constack_v1alpha_scaledobject_proto_init() {
	if File_quwan_cloud_constack_v1alpha_scaledobject_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SaveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SaveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ScaleWorkloadReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ScaleWorkloadResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_quwan_cloud_constack_v1alpha_scaledobject_proto_goTypes,
		DependencyIndexes: file_quwan_cloud_constack_v1alpha_scaledobject_proto_depIdxs,
		MessageInfos:      file_quwan_cloud_constack_v1alpha_scaledobject_proto_msgTypes,
	}.Build()
	File_quwan_cloud_constack_v1alpha_scaledobject_proto = out.File
	file_quwan_cloud_constack_v1alpha_scaledobject_proto_rawDesc = nil
	file_quwan_cloud_constack_v1alpha_scaledobject_proto_goTypes = nil
	file_quwan_cloud_constack_v1alpha_scaledobject_proto_depIdxs = nil
}
