//go:generate mockgen -destination=plan_mock.go -package=service -source=plan.go
package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	cctx "52tt.com/cicd/pkg/context"
	"google.golang.org/protobuf/types/known/wrapperspb"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/db"
	pbapp "52tt.com/cicd/protocol/app"
	pbpl "52tt.com/cicd/protocol/pipeline"
	bizerr "52tt.com/cicd/services/deploy/internal/pkg/error"
	"gorm.io/gorm"

	apk "52tt.com/cicd/pkg/apk"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/page"
	"52tt.com/cicd/pkg/tools"
	pbdep "52tt.com/cicd/protocol/deploy"
	"52tt.com/cicd/services/deploy/internal/conf"
	"52tt.com/cicd/services/deploy/internal/dao"
	"52tt.com/cicd/services/deploy/internal/model"
)

func DeployPlanToDyeingIdentity(plan dao.DeployPlan) string {
	return fmt.Sprintf("%s%d", plan.UserGroup, plan.ID)
}

type DeployPlanService interface {
	GetDeployPlanList(ctx context.Context, params *model.DeployPlanListReq) (page.Paginator, error)
	DelDeployPlan(ctx context.Context, deployPlanId int64) error
	GetAppDeployPlan(ctx context.Context, req *model.AppDeployPlanReq) ([]model.AppDeployPlanResp, error)
	CreateDeployPlan(ctx context.Context, params *model.CreateDeployPlanReq) error
	UpdateDeployPlan(ctx context.Context, params *model.UpdateDeployPlanReq) error
	EnableDeployPlan(ctx context.Context, deployPlanID int64, op *model.Operator) error
	DisableDeployPlan(ctx context.Context, deployPlanID int64, op *model.Operator) error
	GetDeployPlan(ctx context.Context, deployPlanId int64) (*model.DeployPlanResp, error)
	GetSameVersionDeployPlanList(ctx context.Context, req model.GetSameVersionDeployPlanListReq) ([]*model.GetSameVersionDeployPlanListResp, error)
	GetDeployPlanNodeRunList(ctx context.Context, req model.GetDeployPlanNodeRunListReq) (*model.GetDeployPlanNodeRunListResp, error)
	GetDeployPlanRecordList(ctx context.Context, req *model.DeployPlanRecordListReq) (page.Paginator, error)
	UnbindDeployPlanRecord(ctx context.Context, params model.UnbindDeployPlanRecordReq) error
	BindDeployPlanRecord(ctx context.Context, params model.BindDeployPlanRecordReq) (*dao.DeployPlanRecord, error)
	GetDeployPlansBy(ctx context.Context, appId int64) ([]*int64, error)
	GetDeployPlanStoryList(ctx context.Context, req *model.DeployPlanStoryListReq) (page.Paginator, error)
}

func NewDeployPlanService(dpRepo dao.DeployPlanRepository,
	dpRunRepo dao.PlanRunRepository,
	dyeingSvr pbdep.DyeingProxyServiceServer,
	conf *conf.Dyeing,
	projectSvr pbapp.ProjectServiceClient,
	prSvr pbpl.PipelineRunServiceClient,
	storyRunRepo dao.StoryRunRepository) *DeployPlanSvc {
	return &DeployPlanSvc{
		dpRepo:       dpRepo,
		dyeingSvr:    dyeingSvr,
		config:       conf,
		projectSvr:   projectSvr,
		dpRunRepo:    dpRunRepo,
		prSvr:        prSvr,
		storyRunRepo: storyRunRepo,
	}
}

type DeployPlanSvc struct {
	dpRepo       dao.DeployPlanRepository
	dpRunRepo    dao.PlanRunRepository
	dyeingSvr    pbdep.DyeingProxyServiceServer
	config       *conf.Dyeing
	projectSvr   pbapp.ProjectServiceClient
	prSvr        pbpl.PipelineRunServiceClient
	storyRunRepo dao.StoryRunRepository
}

var _ DeployPlanService = (*DeployPlanSvc)(nil)

func (dps *DeployPlanSvc) GetDeployPlanList(ctx context.Context, params *model.DeployPlanListReq) (page.Paginator, error) {
	var (
		total int64
	)
	list, err := dps.dpRepo.GetDeployPlanList(ctx, params, &total)
	if err != nil {
		log.ErrorWithCtx(ctx, "查询发布计划列表发生错误： %v", err)
		return nil, err
	}
	// 获取发布计划下的应用数量
	deployPlanIDs := tools.MapTo(list, func(dp dao.DeployPlan) int64 {
		return dp.ID
	})
	appRelated, err := dps.dpRepo.CountRelatedApps(ctx, deployPlanIDs)
	if err != nil {
		return nil, err
	}
	deployPlanMap := make(map[int64]int64)
	for _, result := range appRelated {
		deployPlanMap[result.DeployPlanID] = result.Count
	}
	var res []model.DeployPlanListResp
	for _, dp := range list {
		result := model.DeployPlanListResp{
			ID:                    dp.ID,
			Status:                string(dp.Status),
			ProjectID:             dp.ProjectID,
			Name:                  dp.Name,
			UserGroup:             dp.UserGroup,
			OperatorBy:            dp.OperatorBy,
			OperatorByChineseName: dp.OperatorByChineseName,
			OperatorByEmployeeNo:  dp.OperatorByEmployeeNo,
			UpdatedAt:             dp.UpdatedAt,
			AppRelatedTotal:       deployPlanMap[dp.ID],
		}
		res = append(res, result)
	}
	return page.PageOf(params, res, total), nil

}

func (dps *DeployPlanSvc) DelDeployPlan(ctx context.Context, deployPlanId int64) error {
	if err := dps.checkDeployPlanHasRelatedRecords(ctx, deployPlanId); err != nil {
		return err
	}
	// 删除发布计划 并删除发布计划版本
	if dbErr := db.Transaction(func(tx *gorm.DB) error {
		dbCtx := db.CtxWithTX(ctx, tx)
		if err := dps.dpRepo.DelDeployPlan(ctx, deployPlanId); err != nil {
			log.ErrorWithCtx(ctx, "DelDeployPlan dao layer error:%v", err)
			return err
		}
		if err := dps.dpRepo.DeleteDeployPlanVersions(dbCtx, deployPlanId); err != nil {
			log.ErrorWithCtx(ctx, "UpdateDeployPlan delete deploy plan versions error:%v", err)
			return err
		}
		return nil
	}); dbErr != nil {
		return dbErr
	}
	return nil
}

func (dps *DeployPlanSvc) checkDeployPlanHasRelatedRecords(ctx context.Context, deployPlanId int64) error {
	records, err := dps.dpRepo.GetDeployPlanRecords(ctx, &model.DeployPlanRecordParams{DeployPlanID: deployPlanId, Status: constants.PlanRecordStatusRelated})
	if err != nil {
		log.ErrorWithCtx(ctx, "[checkDeployPlanHasRelatedRecords] get deploy plan records error: %v, deployPlanId[%d]", err, deployPlanId)
		return err
	}
	if len(records) > 0 {
		return bizerr.ErrDeployPlanHasRelatedRecords
	}
	return nil
}

// GetAppDeployPlan 查询服务发布计划列表
func (dps *DeployPlanSvc) GetAppDeployPlan(ctx context.Context, req *model.AppDeployPlanReq) ([]model.AppDeployPlanResp, error) {
	planReq := model.QueryPlanListReq{
		UserGroup: req.UserGroup,
		Status:    req.Status,
		ProjectID: req.ProjectID,
	}
	plans, err := dps.dpRepo.FindDeployPlanBy(ctx, &planReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetAppDeployPlan] get app deploy plan error: %v", err)
		return nil, err
	}

	resp := make([]model.AppDeployPlanResp, 0, len(plans))
	for _, plan := range plans {
		// 发布计划已关联当前服务不能被选取
		req.DeployPlanID = plan.ID
		req.RecordStatus = []constants.DeployPlanRecordStatus{constants.PlanRecordStatusRelated}
		var planIsUsed bool
		record, recordErr := dps.dpRepo.GetAppDeployPlanRecord(ctx, req)
		if recordErr != nil {
			log.ErrorWithCtx(ctx, "[GetAppDeployPlan] get app deploy plan record error: %v", recordErr)
			return nil, recordErr
		}
		if record != nil {
			planIsUsed = true
		}
		// 查询发布计划版本
		planVersions, err := dps.dpRepo.GetDeployPlanVersions(ctx, plan.ID)
		if err != nil {
			log.ErrorWithCtx(ctx, "[GetAppDeployPlan] get deploy plan versions error: %v", err)
			return nil, err
		}
		versions := tools.MapTo(planVersions, func(version dao.DeployPlanVersion) model.VersionObj {
			return model.VersionObj{Version: version.Version, ReqCliType: version.ReqCliType, ReqMarketID: version.ReqMarketID, Operator: version.Operator}
		})
		resp = append(resp, model.AppDeployPlanResp{
			ID:        plan.ID,
			Status:    plan.Status,
			ProjectID: plan.ProjectID,
			Name:      plan.Name,
			UserGroup: plan.UserGroup,
			IsUsed:    planIsUsed,
			Versions:  versions,
		})
	}

	return resp, nil
}

type deployPlanVerifyParams struct {
	deployPlanID       int64
	userGroup          string
	projectID          int64
	versions           []string
	trafficControlPlan model.DeployPlanTrafficControlPlan
}

func checkTrafficControlPlan(plan model.DeployPlanTrafficControlPlan) error {
	// 校验初始化流量比例：仅能输入5的倍数，且<=50
	var initialTrafficPercentage int8
	if plan.InitialTrafficPercentage != nil {
		initialTrafficPercentage = *plan.InitialTrafficPercentage
		if initialTrafficPercentage%5 != 0 || initialTrafficPercentage < 0 || initialTrafficPercentage > 50 {
			return bizerr.ErrDeployPlanTrafficPercentage
		}
	}
	if len(plan.Nodes) > 0 {
		var prevActionName constants.PlanAction
		currentTime := time.Now()
		prevNodeTime := currentTime.Unix()
		for _, node := range plan.Nodes {
			// 校验放量计划时间
			if plan.TimeType == constants.PlanTimeTypeAbsolute {
				// 绝对时间第一个节点的时间比当前时间大
				// 绝对时间后面节点的时间需比前面节点的时间大
				if *node.AbsoluteTime < 1 || *node.AbsoluteTime <= prevNodeTime {
					return bizerr.ErrDeployPlanAbsoluteTime
				}
				prevNodeTime = *node.AbsoluteTime
			} else if plan.TimeType == constants.PlanTimeTypeRelative {
				// 相对时间必须大于0
				if *node.RelativeTime < 0 {
					return bizerr.ErrDeployPlanRelativeTime
				}
			}
			// 校验流量比例：仅能输入5的倍数，且<=50
			if node.Action == constants.PlanActionControlTraffic {
				trafficPercentage := *node.TrafficPercentage
				if trafficPercentage%5 != 0 || trafficPercentage < 0 || trafficPercentage > 50 {
					return bizerr.ErrDeployPlanTrafficPercentage
				}
				// 校验放量计划的流量比例是否小于等于初始流量比例
				if plan.InitialTrafficPercentage != nil && trafficPercentage <= initialTrafficPercentage {
					return bizerr.ErrDeployPlanTrafficPercentageLessThanInitial
				}
			}
			// 校验节点顺序 调整流量比例->调整流量比例->通过金丝雀验证->全量部署基准环境
			switch prevActionName {
			case constants.PlanActionPassCanary:
				if node.Action == constants.PlanActionControlTraffic {
					return bizerr.ErrDeployPlanActionOrder
				}
			case constants.PlanActionDeployOrigin:
				if node.Action == constants.PlanActionControlTraffic || node.Action == constants.PlanActionPassCanary {
					return bizerr.ErrDeployPlanActionOrder
				}
			}
			prevActionName = node.Action
		}
	}

	return nil
}

func (dps *DeployPlanSvc) checkThenReturnDeployPlan(ctx context.Context, params deployPlanVerifyParams) (*dao.DeployPlan, error) {
	// 校验发布计划是否被删除了
	var (
		deployPlanObj *dao.DeployPlan
		err           error
	)
	if params.deployPlanID != 0 {
		deployPlanObj, err = dps.dpRepo.GetDeployPlanByID(ctx, params.deployPlanID)
		if err != nil {
			return nil, err
		}
		if deployPlanObj == nil || deployPlanObj.HasDeleted() {
			return nil, bizerr.ErrDeployPlanHasDeleted
		}
	}
	if err = checkTrafficControlPlan(params.trafficControlPlan); err != nil {
		return deployPlanObj, err
	}
	// 先注释掉 校验版本号是否重复 已有界面弹框提示
	//sameDeployPlanVersions, err := dps.dpRepo.GetDeployPlanVersionsByVerify(ctx, params.deployPlanID, params.userGroup, params.projectID, params.versions)
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "[checkDeployPlan] get deploy plan versions error:%v", err)
	//	return deployPlanObj, err
	//}
	//if len(sameDeployPlanVersions) > 0 {
	//	return deployPlanObj, bizerr.ErrHasSameVersionDeployPlan
	//}

	return deployPlanObj, nil
}

func (dps *DeployPlanSvc) CreateDeployPlan(ctx context.Context, params *model.CreateDeployPlanReq) error {
	if _, err := dps.checkThenReturnDeployPlan(ctx, deployPlanVerifyParams{
		deployPlanID:       0,
		userGroup:          params.UserGroup,
		projectID:          params.ProjectID,
		trafficControlPlan: params.TrafficControlPlan,
	}); err != nil {
		return err
	}

	var dp *dao.DeployPlan
	if dbErr := db.Transaction(func(tx *gorm.DB) error {
		dbCtx := db.CtxWithTX(ctx, tx)
		// 添加发布计划
		newDp, err := dps.dpRepo.CreateDeployPlan(dbCtx, params)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateDeployPlan dao layer error:%v", err)
			return err
		}

		// 创建发布计划后，添加版本信息
		if len(params.Versions) > 0 {
			deployPlanVersions := tools.MapTo(params.Versions, func(versionObj model.VersionObj) dao.DeployPlanVersion {
				return dao.DeployPlanVersion{
					DeployPlanID: newDp.ID,
					Version:      versionObj.Version,
					ReqCliType:   versionObj.ReqCliType,
					ReqMarketID:  versionObj.ReqMarketID,
					Operator:     versionObj.Operator,
				}
			})
			if err := dps.dpRepo.BatchCreateDeployPlanVersion(dbCtx, deployPlanVersions); err != nil {
				log.ErrorWithCtx(ctx, "BatchCreateDeployPlanVersion dao layer error:%v", err)
				return err
			}
		}
		// 创建发布计划后，添加服务列表记录
		if len(params.Apps) > 0 {
			deployPlanRecords := tools.MapTo(params.Apps, func(app model.DeployPlanApp) dao.DeployPlanRecord {
				return dao.DeployPlanRecord{
					DeployPlanID:  newDp.ID,
					ProjectID:     app.ProjectID,
					Status:        constants.PlanRecordStatusUnrelated,
					AppID:         app.ID,
					AppName:       app.Name,
					PipelineRunID: 0,
				}
			})
			if err := dps.dpRepo.BatchCreateDeployPlanRecord(dbCtx, deployPlanRecords); err != nil {
				log.ErrorWithCtx(ctx, "BatchCreateDeployPlanRecord dao layer error: %v", err)
				return err
			}
		}
		log.InfoWithCtx(ctx, "CreateDeployPlan new deploy plan: %+v", newDp)

		dp = newDp
		return nil
	}); dbErr != nil {
		return dbErr
	}

	// 更新发布计划流量标记名称
	trafficMarkName := fmt.Sprintf("%s-%d", constants.TrafficMarkPrefix, dp.ID)
	if err := dps.dpRepo.UpdateDeployPlanField(ctx, dp.ID, map[string]interface{}{"traffic_mark_name": trafficMarkName}); err != nil {
		log.ErrorWithCtx(ctx, "UpdateDeployPlanField dao layer error:%v", err)
		return err
	}

	enabledDeployPlans, err := dps.dpRepo.FindDeployPlanPool(ctx, dp.ProjectID, dp.UserGroup, &model.DeployPoolSearchOpt{
		PageModel: page.PageModel{
			Size: 1,
		},
		Status: constants.Enable,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeployPlanList dao layer error:%v", err)
		// Notice: 虽然发生错误，只影响未自动启用发布计划，可以在后面手动启用
		return nil
	}
	if len(enabledDeployPlans) == 0 {
		log.InfoWithCtx(ctx, "No enabled deploy plan found in same pool, enable %d deploy plan and create routing policy", dp.ID)
		dp.TrafficMarkName = trafficMarkName
		if err = dps.createOrUpdateDyeingPolicy(ctx, *dp); err != nil {
			log.ErrorWithCtx(ctx, "CreateOrUpdateDyeingPolicy error:%v, deployPlanId:%d, name:%s", err, dp.ID, dp.Name)
			return nil
		}
		op := &model.Operator{
			OperatorBy:            params.OperatorBy,
			OperatorByChineseName: params.OperatorByChineseName,
			OperatorByEmployeeNo:  params.OperatorByEmployeeNo,
		}
		if _, err = dps.dpRepo.SwitchDeployPlan(ctx, dp.ID, constants.Enable, op); err != nil {
			log.ErrorWithCtx(ctx, "Enable deploy plan %d error:%v", dp.ID, err)
			return nil
		}
		log.InfoWithCtx(ctx, "Enable deploy plan %d and create routing policy success", dp.ID)
	} else {
		log.InfoWithCtx(ctx, "Has enabled deploy plan %+v found in same pool, no need to create routing policy", enabledDeployPlans)
	}

	log.InfoWithCtx(ctx, "CreateDeployPlan %d success", dp.ID)
	return nil
}

func (dps *DeployPlanSvc) UpdateDeployPlan(ctx context.Context, params *model.UpdateDeployPlanReq) error {
	dp, err := dps.checkThenReturnDeployPlan(ctx, deployPlanVerifyParams{
		deployPlanID:       params.ID,
		userGroup:          params.UserGroup,
		projectID:          params.ProjectID,
		trafficControlPlan: params.TrafficControlPlan,
	})
	if err != nil {
		return err
	}

	oldRecords, err := dps.dpRepo.GetDeployPlanRecords(ctx, &model.DeployPlanRecordParams{DeployPlanID: dp.ID})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateDeployPlan get deploy plan records error:%v, deployPlanID: %d", err, dp.ID)
		return err
	}

	if dbErr := db.Transaction(func(tx *gorm.DB) error {
		dbCtx := db.CtxWithTX(ctx, tx)
		err = dps.dpRepo.UpdateVersions(dbCtx, params)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateDeployPlan dao layer error:%v", err)
			return err
		}
		// 更新发布计划后，更新版本信息（清空后，新增）
		err = dps.dpRepo.DeleteDeployPlanVersions(dbCtx, dp.ID)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateDeployPlan delete deploy plan versions error:%v", err)
			return err
		}
		if len(params.Versions) > 0 {
			deployPlanVersions := tools.MapTo(params.Versions, func(versionObj model.VersionObj) dao.DeployPlanVersion {
				return dao.DeployPlanVersion{
					DeployPlanID: dp.ID,
					Version:      versionObj.Version,
					ReqCliType:   versionObj.ReqCliType,
					ReqMarketID:  versionObj.ReqMarketID,
					Operator:     versionObj.Operator,
				}
			})
			err = dps.dpRepo.BatchCreateDeployPlanVersion(dbCtx, deployPlanVersions)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateDeployPlan batch create deploy plan versions error:%v", err)
				return err
			}
		}
		// 更新发布计划后，更新服务列表记录（删除不存在的，新增）
		deletedApps, insertApps := generatePlanUpdatedApps(dp.ID, oldRecords, params.Apps)
		if len(deletedApps) > 0 {
			err = dps.dpRepo.DeleteDeployPlanRecordsBy(dbCtx, dp.ID, deletedApps)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateDeployPlan delete deploy plan records error:%v", err)
				return err
			}
		}
		if len(insertApps) > 0 {
			err = dps.dpRepo.BatchCreateDeployPlanRecord(dbCtx, insertApps)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateDeployPlan batch create deploy plan records error:%v", err)
				return err
			}
		}

		return nil
	}); dbErr != nil {
		return dbErr
	}

	if dp.Status == constants.Enable {
		log.InfoWithCtx(ctx, "UpdateDeployPlan %+v is enabled, should update routing policy", *dp)
		if err = dps.createOrUpdateDyeingPolicy(ctx, *dp); err != nil {
			log.ErrorWithCtx(ctx, "CreateOrUpdateDyeingPolicy error:%v", err)
			return err
		}
		return nil
	}
	log.InfoWithCtx(ctx, "UpdateDeployPlan success")
	return nil
}

// generatePlanUpdatedApps 生成编辑发布计划更新的应用列表
// oldDpr 旧的发布计划记录 newApp 新传入的服务列表
func generatePlanUpdatedApps(deployPlanId int64, oldDpr []dao.DeployPlanRecord, newApp []model.DeployPlanApp) ([]int64, []dao.DeployPlanRecord) {
	oldAppMap := make(map[int64]bool)
	newAppMap := make(map[int64]bool)
	tools.Do(oldDpr, func(record dao.DeployPlanRecord) {
		oldAppMap[record.AppID] = true
	})
	tools.Do(newApp, func(app model.DeployPlanApp) {
		newAppMap[app.ID] = true
	})
	// 旧的发布计划记录中存在，新传入的服务列表中不存在的服务
	deletedApps := make([]int64, 0)
	for _, record := range oldDpr {
		if _, ok := newAppMap[record.AppID]; !ok {
			deletedApps = append(deletedApps, record.AppID)
		}
	}
	// 新传入的服务列表中存在，旧的发布计划记录中不存在的服务
	insertApps := make([]dao.DeployPlanRecord, 0)
	for _, app := range newApp {
		if _, ok := oldAppMap[app.ID]; !ok {
			insertApps = append(insertApps, dao.DeployPlanRecord{
				DeployPlanID: deployPlanId,
				ProjectID:    app.ProjectID,
				Status:       constants.PlanRecordStatusUnrelated,
				AppID:        app.ID,
				AppName:      app.Name,
			})
		}
	}

	return deletedApps, insertApps
}

func (dps *DeployPlanSvc) EnableDeployPlan(ctx context.Context, deployPlanID int64, op *model.Operator) error {
	dp, err := dps.dpRepo.GetDeployPlanByID(ctx, deployPlanID)
	if err != nil {
		log.ErrorWithCtx(ctx, "EnableDeployPlan %d get deployPlan error: %v", deployPlanID, err)
		return err
	}
	if dp == nil {
		return fmt.Errorf("deployPlan %d not found", deployPlanID)
	}
	if dp.HasDeleted() {
		return fmt.Errorf("deployPlan %d has been deleted", deployPlanID)
	}

	statusChanged, err := dps.dpRepo.SwitchDeployPlan(ctx, deployPlanID, constants.Enable, op)
	if err != nil {
		log.ErrorWithCtx(ctx, "EnableDeployPlan %d switch deployPlan error: %v", deployPlanID, err)
		return err
	}

	if !statusChanged {
		log.InfoWithCtx(ctx, "EnableDeployPlan %d already enabled", deployPlanID)
		return nil
	}
	if err = dps.createOrUpdateDyeingPolicy(ctx, *dp); err != nil {
		log.ErrorWithCtx(ctx, "EnableDeployPlan %d createOrUpdateDyeingPolicy error: %v", deployPlanID, err)
		return err
	}

	log.InfoWithCtx(ctx, "EnableDeployPlan %d success", deployPlanID)
	return nil
}

func (dps *DeployPlanSvc) DisableDeployPlan(ctx context.Context, deployPlanID int64, op *model.Operator) error {
	if err := dps.checkDeployPlanHasRelatedRecords(ctx, deployPlanID); err != nil {
		return err
	}
	statusChanged, err := dps.dpRepo.SwitchDeployPlan(ctx, deployPlanID, constants.Disable, op)
	if err != nil {
		log.ErrorWithCtx(ctx, "DisableDeployPlan %d error: %v", deployPlanID, err)
		return err
	}

	if !statusChanged {
		log.InfoWithCtx(ctx, "DisableDeployPlan %d already disabled", deployPlanID)
		return nil
	}
	dp, err := dps.dpRepo.GetDeployPlanByID(ctx, deployPlanID)
	if err != nil {
		log.ErrorWithCtx(ctx, "DisableDeployPlan %d get deployPlan error: %v", deployPlanID, err)
		return err
	}
	versions, err := dps.dpRepo.GetDeployPlanVersions(ctx, deployPlanID)
	if err != nil {
		log.ErrorWithCtx(ctx, "DisableDeployPlan %d get deployPlan versions error: %v", deployPlanID, err)
		return err
	}
	// 版本号不为空，删除染色策略
	if len(versions) > 0 {
		if err = dps.deleteDyeingPolicy(ctx, *dp); err != nil {
			log.ErrorWithCtx(ctx, "DisableDeployPlan %d deleteDyeingPolicy error: %v", deployPlanID, err)
			return err
		}
	}

	log.InfoWithCtx(ctx, "DisableDeployPlan %d success", deployPlanID)
	return nil
}

func (dps *DeployPlanSvc) createOrUpdateDyeingPolicy(ctx context.Context, dp dao.DeployPlan) error {
	var cancelFnc context.CancelFunc
	ctx, cancelFnc = cctx.CopyCtxWithTimeout(ctx, 1*time.Minute) // 1分钟啥都干完了
	defer cancelFnc()
	versions, err := dps.dpRepo.GetDeployPlanVersions(ctx, dp.ID)
	if err != nil {
		log.ErrorWithCtx(ctx, "createOrUpdateDyeingPolicy get deploy plan versions error: %v", err)
		return err
	}
	// 版本号为空，不创建染色策略
	if len(versions) == 0 {
		log.InfoWithCtx(ctx, "createOrUpdateDyeingPolicy deploy plan %d, name: %s versions is empty, skip", dp.ID, dp.Name)
		return nil
	}
	var matches []*pbdep.Match
	for _, version := range versions {
		clientVersion, err := apk.GetInt64ClientVersion(version.Version)
		if err != nil {
			log.ErrorWithCtx(ctx, "createOrUpdateDyeingPolicy generate ClientVersionString from deploy plan error: %v", err)
			return err
		}
		match := &pbdep.Match{ReqCliVersion: clientVersion}
		if version.ReqCliType != nil {
			match.ReqCliType = wrapperspb.Int64(*version.ReqCliType)
		}
		if version.ReqMarketID != nil {
			match.ReqMarketId = wrapperspb.Int64(*version.ReqMarketID)
		}
		if version.Operator != nil {
			match.Operator = wrapperspb.Int64(*version.Operator)
		}

		matches = append(matches, match)
	}
	// Notice: 在我们的使用场景中，不会出现多个 Project 跨多个染色策略的 namespace 的情况，所以这里直接创建发布计划的 Project
	log.InfoWithCtx(ctx, "deployPlan name:%s, start CreateOrUpdateDeployPlanDyeingRule matches: %v", dp.Name, matches)
	_, err = dps.dyeingSvr.CreateOrUpdateDeployPlanDyeingRule(ctx, &pbdep.DeployPlanDyeingRuleReq{
		Identity:    DeployPlanToDyeingIdentity(dp),
		ProjectId:   dp.ProjectID,
		Env:         dps.config.Env,
		TrafficMark: dp.TrafficMarkName,
		Matches:     matches,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "createOrUpdateDyeingPolicy create deploy plan %+v dyeing rule error: %v", dp, err)
		return err
	}
	log.InfoWithCtx(ctx, "createOrUpdateDyeingPolicy with DeployPlan %+v success", dp)
	return nil
}

// deleteDyeingPolicy 在发布计划删除的时候调用，删除网关染色规则
func (dps *DeployPlanSvc) deleteDyeingPolicy(ctx context.Context, dp dao.DeployPlan) error {
	_, err := dps.dyeingSvr.DeleteDyeingRule(ctx, &pbdep.DeleteDyeingRuleReq{
		ProjectId: dp.ProjectID,
		Env:       dps.config.Env,
		Identity:  DeployPlanToDyeingIdentity(dp),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "deleteDyeingPolicy delete deploy plan %+v dyeing rule error: %v", dp, err)
		return err
	}
	log.InfoWithCtx(ctx, "deleteDyeingPolicy with DeployPlan %+v success", dp)
	return nil
}

func (dps *DeployPlanSvc) GetDeployPlan(ctx context.Context, deployPlanId int64) (*model.DeployPlanResp, error) {
	dp, err := dps.dpRepo.GetDeployPlanByID(ctx, deployPlanId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeployPlan %d get deployPlan error: %v", deployPlanId, err)
		return nil, err
	}
	if dp == nil {
		log.ErrorWithCtx(ctx, "GetDeployPlan %d not found", deployPlanId)
		return nil, fmt.Errorf("DeployPlan %d not found", deployPlanId)
	}

	// 获取发布计划版本号列表
	dpVersions, err := dps.dpRepo.GetDeployPlanVersions(ctx, deployPlanId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeployPlan %d get deployPlan versions error: %v", deployPlanId, err)
		return nil, err
	}
	versions := tools.MapTo(dpVersions, func(version dao.DeployPlanVersion) model.VersionObj {
		return model.VersionObj{Version: version.Version, ReqCliType: version.ReqCliType, ReqMarketID: version.ReqMarketID, Operator: version.Operator}
	})

	// 获取发布计划服务列表
	records, err := dps.dpRepo.GetDeployPlanRecords(ctx, &model.DeployPlanRecordParams{DeployPlanID: deployPlanId})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeployPlan %d get deployPlan records error: %v", deployPlanId, err)
		return nil, err
	}
	projectIds := tools.MapTo(records, func(record dao.DeployPlanRecord) int64 {
		return record.ProjectID
	})
	projectMap, err := dps.getAppProjectMap(ctx, projectIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeployPlan %d get app project map error: %v", deployPlanId, err)
		return nil, err
	}

	apps := tools.MapTo(records, func(record dao.DeployPlanRecord) model.DeployPlanApp {
		var projectName string
		if project, ok := projectMap[record.ProjectID]; ok {
			projectName = project
		}
		return model.DeployPlanApp{
			ID:          record.AppID,
			Name:        record.AppName,
			ProjectID:   record.ProjectID,
			ProjectName: projectName,
			Status:      record.Status,
		}
	})

	plan := model.DeployPlanTrafficControlPlan{}
	if dp.TrafficControlPlan != nil {
		err = json.Unmarshal(dp.TrafficControlPlan, &plan)
		if err != nil {
			return nil, err
		}
	}
	resp := model.DeployPlanResp{
		ID:                    dp.ID,
		Name:                  dp.Name,
		ProjectID:             dp.ProjectID,
		UserGroup:             dp.UserGroup,
		Status:                string(dp.Status),
		CanaryPolicy:          strings.Split(dp.CanaryPolicy, ","),
		Versions:              versions,
		TrafficControlPlan:    plan,
		Apps:                  apps,
		OperatorBy:            dp.OperatorBy,
		OperatorByChineseName: dp.OperatorByChineseName,
		OperatorByEmployeeNo:  dp.OperatorByEmployeeNo,
		TrafficMarkName:       dp.TrafficMarkName,
	}

	return &resp, nil
}

func (dps *DeployPlanSvc) getAppProjectMap(ctx context.Context, projectIds []int64) (map[int64]string, error) {
	projects, err := dps.projectSvr.ListProjects(ctx, &pbapp.ListProjectsReq{Ids: projectIds})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAppProjectMap get projects error: %v", err)
		return nil, err
	}
	var projectMap = make(map[int64]string)
	for _, project := range projects.Projects {
		projectMap[project.Id] = project.Name
	}
	return projectMap, nil
}

func getFrontVersionObj(ctx context.Context, obj model.VersionObj) model.DeployPlanFrontVersion {
	// 全部
	if obj.ReqCliType == nil && obj.ReqMarketID == nil {
		return model.DeployPlanFrontVersion{Version: obj.Version, ReqCliTypeName: "ALL", ReqMarketName: "-", Operator: obj.Operator}
	}
	// pc
	if obj.ReqCliType != nil && *obj.ReqCliType == constants.EnumEnumReqCliTypePc.Value() {
		return model.DeployPlanFrontVersion{Version: obj.Version, ReqCliTypeName: constants.EnumReqCliType(*obj.ReqCliType).ToReqCliTypeName(), ReqMarketName: "-", Operator: obj.Operator}
	}
	// android ios
	if obj.ReqCliType != nil && (*obj.ReqCliType == constants.EnumEnumReqCliTypeAndroid.Value() || *obj.ReqCliType == constants.EnumEnumReqCliTypeIos.Value()) {
		reqMarketName := "ALL"
		if obj.ReqMarketID != nil {
			reqMarketName = constants.EnumReqMarket(*obj.ReqMarketID).ToReqMarketName()
		}
		return model.DeployPlanFrontVersion{Version: obj.Version, ReqCliTypeName: constants.EnumReqCliType(*obj.ReqCliType).ToReqCliTypeName(), ReqMarketName: reqMarketName, Operator: obj.Operator}
	}
	return model.DeployPlanFrontVersion{Version: obj.Version, ReqCliTypeName: "-", ReqMarketName: "-", Operator: obj.Operator}
}

func isSameVersion(dp dao.DeployPlanVersion, reqVersion model.VersionObj) bool {
	// 建议别乱动 写了逻辑之后gpt优化出来的代码，参考产品提供的飞书 https://q9jvw0u5f5.feishu.cn/wiki/Btu8w1M0oi5oGdkAACrcVCWxnte
	if dp.Version != reqVersion.Version {
		return false
	}
	if reqVersion.ReqCliType == nil || dp.ReqCliType == nil {
		return true
	}
	if *reqVersion.ReqCliType != *dp.ReqCliType {
		return false
	}
	if *reqVersion.ReqCliType == constants.EnumEnumReqCliTypePc.Value() {
		return true
	}
	if reqVersion.ReqMarketID == nil || dp.ReqMarketID == nil {
		return true
	}
	return *reqVersion.ReqMarketID == *dp.ReqMarketID
}

func (dps *DeployPlanSvc) GetSameVersionDeployPlanList(ctx context.Context, req model.GetSameVersionDeployPlanListReq) ([]*model.GetSameVersionDeployPlanListResp, error) {
	var res []*model.GetSameVersionDeployPlanListResp
	versionStrList := tools.MapTo(req.Versions, func(versionObj model.VersionObj) string {
		return versionObj.Version
	})
	deployPlans, err := dps.dpRepo.GetDeployPlanVersionsByVerify(ctx, req.DeployPlanId, req.UserGroup, req.ProjectID, versionStrList)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetSameVersionDeployPlanList] get same version deploy plan list error: %v", err)
		return nil, err
	}
	for _, dp := range deployPlans {

		ConflictVersion := model.VersionObj{Version: dp.Version, ReqCliType: dp.ReqCliType, ReqMarketID: dp.ReqMarketID, Operator: dp.Operator}
		for _, reqVersion := range req.Versions {
			if isSameVersion(dp, reqVersion) {
				result := &model.GetSameVersionDeployPlanListResp{
					Name:            dp.DeployPlan.Name,
					DeployPlanId:    dp.DeployPlanID,
					ProjectID:       dp.DeployPlan.ProjectID,
					UserGroup:       dp.DeployPlan.UserGroup,
					RawVersion:      getFrontVersionObj(ctx, reqVersion),
					ConflictVersion: getFrontVersionObj(ctx, ConflictVersion),
				}
				res = append(res, result)
			}
		}
	}
	return res, nil
}

func (dps *DeployPlanSvc) GetDeployPlanNodeRunList(ctx context.Context, req model.GetDeployPlanNodeRunListReq) (*model.GetDeployPlanNodeRunListResp, error) {
	list, err := dps.dpRunRepo.GetDeployPlanNodeRunList(ctx, req.PipelineRunId, req.DeployPlanId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeployPlanNodeRunList dao layer error: %v", err)
		return nil, err
	}
	resp := &model.GetDeployPlanNodeRunListResp{}
	var res []*model.DeployPlanNodeRunList
	if len(list) > 0 && list[0].PlanRun.CanceledByEmployeeNo != "" {
		resp.DeployPlanRunCancelObj = &model.DeployPlanRunCancelObj{CanceledByEmployeeNo: list[0].PlanRun.CanceledByEmployeeNo, CanceledByChineseName: list[0].PlanRun.CanceledByChineseName, CanceledAt: &list[0].PlanRun.CanceledAt}
	}
	for _, nodeRun := range list {
		var nodeRunCfg model.TrafficControlPlanNode
		err := json.Unmarshal(nodeRun.NodeRunCfg, &nodeRunCfg)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetDeployPlanNodeRunList json.Unmarshal NodeRunCfg error: %v", err)
			return nil, err
		}
		var timeStart *time.Time
		if nodeRun.TimeStart != 0 {
			t := time.Unix(nodeRun.TimeStart, 0)
			timeStart = &t
		}
		result := &model.DeployPlanNodeRunList{
			PlanNodeRunId:     nodeRun.ID,
			PipelineRunId:     nodeRun.PlanRun.PipelineRunId,
			DeployPlanId:      req.DeployPlanId,
			Action:            nodeRun.Action,
			Status:            nodeRun.Status,
			TimeStart:         timeStart,
			TrafficPercentage: nodeRunCfg.TrafficPercentage,
		}
		res = append(res, result)
	}
	resp.DeployPlanNodeRunList = res
	return resp, nil
}

func (dps *DeployPlanSvc) GetDeployPlanRecordList(ctx context.Context, req *model.DeployPlanRecordListReq) (page.Paginator, error) {
	var total int64
	records, err := dps.dpRepo.GetDeployPlanRecordList(ctx, req, &total)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeployPlanRecordList dao layer error: %v", err)
		return nil, err
	}

	var (
		projectIds, pipelineRunIds []int64
	)
	for _, record := range records {
		projectIds = append(projectIds, record.ProjectID)
		pipelineRunIds = append(pipelineRunIds, record.PipelineRunID)
	}
	projectMap, err := dps.getAppProjectMap(ctx, projectIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeployPlan %d get app project map error: %v", req.DeployPlanID, err)
		return nil, err
	}
	pipelineRuns, err := dps.prSvr.ListPipelineRuns(ctx, &pbpl.ListPipelineRunReq{Ids: pipelineRunIds})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeployPlanRecordList get pipeline runs error: %v", err)
		return nil, err
	}
	pipelineRunMap := make(map[int64]*pbpl.GetPRStatusResp)
	for _, pr := range pipelineRuns.Runs {
		pipelineRunMap[pr.Id] = pr
	}

	var res []model.DeployPlanRecordListResp
	for _, record := range records {
		var projectName string
		if project, ok := projectMap[record.ProjectID]; ok {
			projectName = project
		}
		var (
			pipelineId      int64
			buildNumber     int64
			deployProdTasks []model.DeployProdTask
		)
		if run, ok := pipelineRunMap[record.PipelineRunID]; ok {
			pipelineId = run.PipelineId
			buildNumber = run.BuildNumber
			deployProdTasks = generateDeployProdTasks(run.Stages)
		}
		result := model.DeployPlanRecordListResp{
			DeployPlanApp: model.DeployPlanApp{
				ID:          record.AppID,
				Name:        record.AppName,
				ProjectID:   record.ProjectID,
				ProjectName: projectName,
				Status:      record.Status,
			},
			PipelineID:      pipelineId,
			BuildNumber:     buildNumber,
			PipelineRunID:   record.PipelineRunID,
			DeployProdTasks: deployProdTasks,
		}
		res = append(res, result)
	}

	return page.PageOf(req, res, total), nil
}

func generateDeployProdTasks(stages []*pbpl.GetPRStatusResp_Stage) []model.DeployProdTask {
	for i := len(stages) - 1; i >= 0; i-- {
		stage := stages[i]
		if stage.Type == constants.STAGE_DEPLOY_PROD_ENV.String() {
			var tasks []model.DeployProdTask
			for _, task := range stage.Tasks {
				tasks = append(tasks, model.DeployProdTask{
					Name:   task.Name,
					Status: task.Status,
				})
			}
			return tasks
		}
	}
	return nil
}

// UnbindDeployPlanRecord 解绑发布计划记录
// 更新发布计划的pipeline_run_id为0，status为Unrelated/Published
func (dps *DeployPlanSvc) UnbindDeployPlanRecord(ctx context.Context, params model.UnbindDeployPlanRecordReq) error {
	req := &model.AppDeployPlanReq{
		DeployPlanID:  params.DeployPlanId,
		AppID:         params.AppId,
		PipelineRunID: params.PipelineRunId,
	}
	oldPipelineRunRecord, err := dps.dpRepo.GetAppDeployPlanRecord(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "[UnbindDeployPlanRecord] get app deploy plan record error: %v", err)
		return err
	}
	if oldPipelineRunRecord != nil {
		status := constants.PlanRecordStatusUnrelated.String()
		if params.Status != "" {
			status = params.Status
		}
		updatedVal := map[string]interface{}{
			"status": status,
		}
		// 如果状态为Unrelated，pipeline_run_id置为0
		if status == constants.PlanRecordStatusUnrelated.String() {
			updatedVal["pipeline_run_id"] = 0
		}
		err = dps.dpRepo.UpdateDeployPlanRecordField(ctx, oldPipelineRunRecord.ID, updatedVal)
		if err != nil {
			log.ErrorWithCtx(ctx, "[UnbindDeployPlanRecord] update deploy plan record error: %v", err)
			return err
		}
	}

	return nil
}

// BindDeployPlanRecord 绑定发布计划记录
// 更新发布计划的pipeline_run_id为params.PipelineRunId，status为Related
func (dps *DeployPlanSvc) BindDeployPlanRecord(ctx context.Context, params model.BindDeployPlanRecordReq) (*dao.DeployPlanRecord, error) {
	var (
		recordResult *dao.DeployPlanRecord
		err          error
	)
	req := &model.AppDeployPlanReq{
		DeployPlanID: params.DeployPlanId,
		AppID:        params.AppId,
	}
	recordResult, err = dps.dpRepo.GetAppDeployPlanRecord(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "[BindDeployPlanRecord] get app deploy plan record error: %v, req: %+v", err, req)
		return nil, err
	}
	if recordResult == nil {
		newRecord := &dao.DeployPlanRecord{
			DeployPlanID:  params.DeployPlanId,
			ProjectID:     params.ProjectId,
			AppID:         params.AppId,
			AppName:       params.AppName,
			Status:        constants.PlanRecordStatusRelated,
			PipelineRunID: params.PipelineRunId,
		}
		recordResult, err = dps.dpRepo.CreateDeployPlanRecord(ctx, newRecord)
		if err != nil {
			log.ErrorWithCtx(ctx, "[BindDeployPlanRecord] create deploy plan record error: %v", err)
			return nil, err
		}
	} else {
		if recordResult.PipelineRunID == params.PipelineRunId && recordResult.Status == constants.PlanRecordStatusRelated {
			// 无需更新
			log.InfoWithCtx(ctx, "[BindDeployPlanRecord] deploy plan record already related, no need to update")
			return recordResult, nil
		}
		// 存在关联记录，更新
		err = dps.dpRepo.UpdateDeployPlanRecordField(ctx, recordResult.ID, map[string]interface{}{
			"pipeline_run_id": params.PipelineRunId,
			"status":          constants.PlanRecordStatusRelated,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "[BindDeployPlanRecord] update deploy plan record error: %v", err)
			return nil, err
		}
	}

	return recordResult, nil
}

func (dps *DeployPlanSvc) GetDeployPlansBy(ctx context.Context, appId int64) ([]*int64, error) {
	var deployPlanIds []*int64
	deployPlans, err := dps.dpRepo.GetDeployPlanRecords(ctx, &model.DeployPlanRecordParams{AppID: appId, IgnoreStatus: constants.PlanRecordStatusRelated})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeployPlansBy GetDeployPlanRecords error: %v", err)
		return nil, err
	}
	// 去掉删除 未启用的发布计划
	var deployPlansFilter []dao.DeployPlanRecord
	for _, dp := range deployPlans {
		if dp.DeployPlan.HasDeleted() || dp.DeployPlan.Status != constants.Enable {
			continue
		}
		deployPlansFilter = append(deployPlansFilter, dp)
	}
	deployPlanIds = tools.MapTo(deployPlansFilter, func(record dao.DeployPlanRecord) *int64 {
		return &record.DeployPlan.ID
	})
	return deployPlanIds, nil
}

func (dps *DeployPlanSvc) GetDeployPlanStoryList(ctx context.Context, req *model.DeployPlanStoryListReq) (page.Paginator, error) {
	res := make([]model.DeployPlanStoryListResp, 0)

	// 根据发布计划关联的发布计划记录获取pipeline run id
	records, err := dps.dpRepo.GetDeployPlanRecords(ctx, &model.DeployPlanRecordParams{DeployPlanID: req.DeployPlanID})
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetDeployPlanStoryList] get deploy plan records error: %v", err)
		return nil, err
	}
	planPRIds := make([]int64, 0)
	appRecordMap := make(map[int64]dao.DeployPlanRecord)
	for _, record := range records {
		if record.PipelineRunID != 0 {
			planPRIds = append(planPRIds, record.PipelineRunID)
		}
		appRecordMap[record.AppID] = record
	}
	if len(planPRIds) == 0 {
		return page.PageOf(req, res, 0), nil
	}

	// 获取发布计划记录关联的pipeline run信息
	pipelineRuns, err := dps.prSvr.ListPipelineRuns(ctx, &pbpl.ListPipelineRunReq{Ids: planPRIds})
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetDeployPlanStoryList] get pipeline runs error: %v", err)
		return nil, err
	}
	// 获取发布计划关联的pipeline run 提测审批任务id
	testApprovalTaskIds := make([]int64, 0)
	approvalTaskPRMap := make(map[int64]*pbpl.GetPRStatusResp)
	for _, run := range pipelineRuns.Runs {
		var isHasApprovalTask bool
		for _, stage := range run.Stages {
			for _, task := range stage.Tasks {
				if task.Type == constants.TASK_TEST_APPROVAL.String() {
					testApprovalTaskIds = append(testApprovalTaskIds, task.Id)
					approvalTaskPRMap[task.Id] = run
					isHasApprovalTask = true
					break
				}
			}
			if isHasApprovalTask {
				break
			}
		}
	}
	log.InfoWithCtx(ctx, "[GetDeployPlanStoryList] testApprovalTaskIds: %v", testApprovalTaskIds)
	// 没有提测审批任务
	if len(testApprovalTaskIds) == 0 {
		return page.PageOf(req, res, 0), nil
	}

	// 根据提测审批任务id获取story run信息
	req.TaskRunIds = testApprovalTaskIds
	total, err := dps.storyRunRepo.CountStoryRun(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetDeployPlanStoryList] count story run error: %v, req: %+v", err, req)
		return nil, err
	}
	stories, err := dps.storyRunRepo.GetStoryRunBy(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetDeployPlanStoryList] get story run list error: %v, req: %+v", err, req)
		return nil, err
	}

	var statusList = make([]string, 0)
	if req.Status != "" {
		statusList = strings.Split(req.Status, ",")
	}
	statusMap := make(map[constants.DeployPlanRecordStatus]bool)
	for _, status := range statusList {
		statusMap[constants.DeployPlanRecordStatus(status)] = true
	}

	projectMap := make(map[int64]string)
	for _, story := range stories {
		storyItem := model.DeployPlanStoryListResp{
			ID:       story.StoryId,
			Name:     story.StoryName,
			Children: make([]model.DeployPlanStoryList, 0),
		}

		runs, err := dps.storyRunRepo.GetStoryRunApps(ctx, story.StoryId)
		if err != nil {
			log.ErrorWithCtx(ctx, "[GetDeployPlanStoryList] get story run apps error: %v, storyId: %d", err, story.StoryId)
			return nil, err
		}

		projectIds := make([]int64, 0)
		for _, run := range runs {
			pipelineRun := &pbpl.GetPRStatusResp{}
			if run.TaskRunId > 0 {
				// 通过提测审批任务id获取pipeline run信息
				if pr, ok := approvalTaskPRMap[run.TaskRunId]; ok {
					pipelineRun = pr
				} else {
					pipelineRun, err = dps.prSvr.GetPipelineRunByTaskRunId(ctx, &pbpl.GetPRTaskReq{Tid: run.TaskRunId})
					if err != nil {
						log.ErrorWithCtx(ctx, "[GetDeployPlanStoryList] get pipeline run by task run id error: %v, taskRunId: %d", err, run.TaskRunId)
						return nil, err
					}
					approvalTaskPRMap[run.TaskRunId] = pipelineRun
				}
			}
			appId := run.AppId
			var appStatus constants.DeployPlanRecordStatus
			if record, ok := appRecordMap[appId]; ok && record.PipelineRunID == pipelineRun.Id {
				appStatus = record.Status
			} else {
				appStatus = getPipelineRunDeployOrigin(pipelineRun.Stages)
			}
			// 过滤状态
			if len(statusList) > 0 && !statusMap[appStatus] {
				continue
			}
			var projectName string
			if project, ok := projectMap[run.ProjectId]; ok {
				projectName = project
			} else {
				projectIds = append(projectIds, run.ProjectId)
			}
			storyItem.Children = append(storyItem.Children, model.DeployPlanStoryList{
				DeployPlanApp: model.DeployPlanApp{
					ID:          run.AppId,
					Name:        run.AppName,
					ProjectID:   run.ProjectId,
					ProjectName: projectName,
					Status:      appStatus,
				},
				PipelineID:           pipelineRun.PipelineId,
				BuildNumber:          pipelineRun.BuildNumber,
				PipelineRunID:        pipelineRun.Id,
				TriggerBy:            pipelineRun.TriggerBy,
				TriggerByChineseName: pipelineRun.ChineseName,
				TriggerByEmployeeID:  pipelineRun.TriggerByEmployeeNo,
				StoryRunID:           run.Id,
				Tasks:                generateDeployTasks(pipelineRun.Stages),
			})
		}
		if len(storyItem.Children) == 0 {
			continue
		}
		log.InfoWithCtx(ctx, "[GetDeployPlanStoryList] projectIds: %v", projectIds)
		if len(projectIds) > 0 {
			// 获取提测审批任务关联的项目信息
			projects, err := dps.projectSvr.ListProjects(ctx, &pbapp.ListProjectsReq{Ids: projectIds})
			if err != nil {
				log.ErrorWithCtx(ctx, "[GetDeployPlanStoryList] get projects error: %v", err)
				return nil, err
			}
			for _, project := range projects.Projects {
				projectMap[project.Id] = project.Name
			}
			for i, child := range storyItem.Children {
				if projectName, ok := projectMap[child.ProjectID]; ok {
					storyItem.Children[i].ProjectName = projectName
				}
			}
		}

		res = append(res, storyItem)
	}

	return page.PageOf(req, res, total), nil
}

func generateDeployTasks(stages []*pbpl.GetPRStatusResp_Stage) []model.DeployProdTask {
	if stages == nil || len(stages) == 0 {
		return nil
	}
	var tasks []model.DeployProdTask
	var isStart bool
	for i := 0; i <= len(stages)-1; i++ {
		stage := stages[i]
		if stage.Type == constants.STAGE_DEPLOY_TEST_ENV.String() || isStart {
			for _, task := range stage.Tasks {
				tasks = append(tasks, model.DeployProdTask{
					Name:   task.Name,
					Status: task.Status,
				})
			}
			isStart = true
		}
	}
	return tasks
}

func getPipelineRunDeployOrigin(stages []*pbpl.GetPRStatusResp_Stage) constants.DeployPlanRecordStatus {
	status := constants.PlanRecordStatusUnrelated
	for _, stage := range stages {
		if stage.Type == constants.STAGE_DEPLOY_PROD_ENV.String() {
			if stage.Status == constants.SUCCESSFUL.String() {
				status = constants.PlanRecordStatusPublished
			} else if stage.Status == constants.RUNNING.String() {
				status = constants.PlanRecordStatusRelated
			}
		}
	}
	return status
}
