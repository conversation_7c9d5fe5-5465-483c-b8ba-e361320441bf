// Code generated by MockGen. DO NOT EDIT.
// Source: event_link_cd_log.go

// Package dao is a generated GoMock package.
package dao

import (
	context "context"
	reflect "reflect"

	models "52tt.com/cicd/services/migrate/internal/models"
	gomock "github.com/golang/mock/gomock"
)

// MockEventLinkCdLogRepos is a mock of EventLinkCdLogRepos interface.
type MockEventLinkCdLogRepos struct {
	ctrl     *gomock.Controller
	recorder *MockEventLinkCdLogReposMockRecorder
}

// MockEventLinkCdLogReposMockRecorder is the mock recorder for MockEventLinkCdLogRepos.
type MockEventLinkCdLogReposMockRecorder struct {
	mock *MockEventLinkCdLogRepos
}

// NewMockEventLinkCdLogRepos creates a new mock instance.
func NewMockEventLinkCdLogRepos(ctrl *gomock.Controller) *MockEventLinkCdLogRepos {
	mock := &MockEventLinkCdLogRepos{ctrl: ctrl}
	mock.recorder = &MockEventLinkCdLogReposMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventLinkCdLogRepos) EXPECT() *MockEventLinkCdLogReposMockRecorder {
	return m.recorder
}

// AddObj mocks base method.
func (m *MockEventLinkCdLogRepos) AddObj(ctx context.Context, obj models.EventLinkCdLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddObj", ctx, obj)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddObj indicates an expected call of AddObj.
func (mr *MockEventLinkCdLogReposMockRecorder) AddObj(ctx, obj interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddObj", reflect.TypeOf((*MockEventLinkCdLogRepos)(nil).AddObj), ctx, obj)
}

// FindAllEventLinkCdLogs mocks base method.
func (m *MockEventLinkCdLogRepos) FindAllEventLinkCdLogs(ctx context.Context, query models.EventLinkCdLogQuery) ([]models.EventLinkCdLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAllEventLinkCdLogs", ctx, query)
	ret0, _ := ret[0].([]models.EventLinkCdLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAllEventLinkCdLogs indicates an expected call of FindAllEventLinkCdLogs.
func (mr *MockEventLinkCdLogReposMockRecorder) FindAllEventLinkCdLogs(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAllEventLinkCdLogs", reflect.TypeOf((*MockEventLinkCdLogRepos)(nil).FindAllEventLinkCdLogs), ctx, query)
}

// FindEventLinkCdLogs mocks base method.
func (m *MockEventLinkCdLogRepos) FindEventLinkCdLogs(ctx context.Context, query models.EventLinkCdLogQuery) ([]models.EventLinkCdLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindEventLinkCdLogs", ctx, query)
	ret0, _ := ret[0].([]models.EventLinkCdLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindEventLinkCdLogs indicates an expected call of FindEventLinkCdLogs.
func (mr *MockEventLinkCdLogReposMockRecorder) FindEventLinkCdLogs(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindEventLinkCdLogs", reflect.TypeOf((*MockEventLinkCdLogRepos)(nil).FindEventLinkCdLogs), ctx, query)
}

// ListEventLinkCdLogsApps mocks base method.
func (m *MockEventLinkCdLogRepos) ListEventLinkCdLogsApps(ctx context.Context) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListEventLinkCdLogsApps", ctx)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListEventLinkCdLogsApps indicates an expected call of ListEventLinkCdLogsApps.
func (mr *MockEventLinkCdLogReposMockRecorder) ListEventLinkCdLogsApps(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListEventLinkCdLogsApps", reflect.TypeOf((*MockEventLinkCdLogRepos)(nil).ListEventLinkCdLogsApps), ctx)
}

// UpdEventLinkCdLog mocks base method.
func (m *MockEventLinkCdLogRepos) UpdEventLinkCdLog(ctx context.Context, obj models.EventLinkCdLog, cols ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, obj}
	for _, a := range cols {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdEventLinkCdLog", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdEventLinkCdLog indicates an expected call of UpdEventLinkCdLog.
func (mr *MockEventLinkCdLogReposMockRecorder) UpdEventLinkCdLog(ctx, obj interface{}, cols ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, obj}, cols...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdEventLinkCdLog", reflect.TypeOf((*MockEventLinkCdLogRepos)(nil).UpdEventLinkCdLog), varargs...)
}
