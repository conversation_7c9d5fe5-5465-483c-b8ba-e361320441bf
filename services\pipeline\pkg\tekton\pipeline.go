package tekton

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	pkgcontext "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/tools"
	"52tt.com/cicd/services/pipeline/internal/conf"
	"52tt.com/cicd/services/pipeline/internal/model"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/pipeline/internal/dao"
	pipeerr "52tt.com/cicd/services/pipeline/pkg/error"
	"github.com/google/uuid"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	"go.uber.org/zap"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// creaateLookupKey create a key for lookup Tekton Task Name and related PipelineTaskGenerator.
// taskType: constants.TASK. required.
// flags: optional. For now, just language
func createLookupKey(taskType string, flags []string) string {
	if len(flags) == 0 || flags[0] == "" {
		return taskType
	}
	return fmt.Sprintf("%s-%s", taskType, strings.Join(flags, "-"))
}

func (c *Client) getTaskGenerator(taskType, language string) (pipelineTaskGenerator, error) {
	key := createLookupKey(taskType, []string{language})
	g, exist := c.generators[key]
	if !exist {
		g, exist = c.generators[taskType]
		if !exist {
			return nil, fmt.Errorf("task %s pipelineTaskGenerator not implement", key)
		}
	}
	return g, nil
}

func (c *Client) getTektonTaskname(taskType, language string) (string, error) {
	key := createLookupKey(taskType, []string{language})
	name, exist := c.tektonTasksName[key]
	if !exist {
		name, exist = c.tektonTasksName[taskType]
		if !exist {
			return "", fmt.Errorf("task %s config.toml not defined", key)
		}
	}
	return name, nil
}

func (c *Client) generatePipelineName(template dao.Template, args model.PipelineRunArgs) string {
	// For now, just return an uuid length less then 64
	var pipelineName string
	if args.IsRetry {
		return uuid.New().String()
	}
	if template.TektonName != "" {
		pipelineName = template.TektonName
	} else {
		pipelineName = uuid.New().String()
	}
	return pipelineName
}

func (c *Client) generatePipelineFromPr(pr dao.PipelineRun, args model.PipelineRunArgs) (pipeline *v1beta1.Pipeline, err error) {
	pipeline = &v1beta1.Pipeline{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.generatePipelineName(*pr.Pipeline.Template, args),
			Namespace: c.namespace,
			Annotations: map[string]string{
				string(constants.ProjectIdKey):  strconv.FormatInt(pr.Pipeline.Template.ProjectId, 10),
				string(constants.TemplateIdKey): strconv.FormatInt(pr.Pipeline.Template.ID, 10),
			},
		},
		Spec: v1beta1.PipelineSpec{},
	}
	// create Tekton PipelineTask and Pipeline Params
	var pipelineParams []v1beta1.ParamSpec
	var tasks []v1beta1.PipelineTask
	var workspaces []v1beta1.PipelineWorkspaceDeclaration
	taskMap := make(map[constants.Task]*v1beta1.PipelineTask)
	paramsName := make(map[string]struct{})
	workspacesName := make(map[string]struct{})
	var lastTask *v1beta1.PipelineTask
	var (
		runAfter                                      []string
		BeforeFirstParallelTaskPipelineTaskTektonName []string // 并行任务前的任务 可能不存在  主要作用是填充并行任务的runAfter字段
		ParallelTaskPipelineTaskTektonNames           []string // 并行任务数组 主要作用是填充并行任务后一个任务的runAfter字段
	)
	ciConfigStages := conf.GetCiStagesConfig()
	for _, stage := range pr.Stages {
		isCiStage := tools.Any(ciConfigStages, func(r string) bool {
			return strings.EqualFold(r, stage.Type)
		})
		if !isCiStage {
			continue
		}
		for _, t := range stage.Tasks {
			if stage.Type == constants.STAGE_PARALLEL.String() {
				runAfter = BeforeFirstParallelTaskPipelineTaskTektonName
			} else if pr.IsTaskIdAfterFirstParallelTask(t.ID) {
				runAfter = ParallelTaskPipelineTaskTektonNames
			} else if lastTask != nil {
				runAfter = []string{lastTask.Name}
			}
			generator, err := c.getTaskGenerator(t.Type, pr.Pipeline.Template.Language)
			tektonTaskName, err := c.getTektonTaskname(t.Type, pr.Pipeline.Template.Language)
			if err != nil {
				return nil, err
			}

			pipelineTask, fields, err := generator.generatePipelineTaskAndFields(generatePipelineTaskArgs{
				prTask:          t,
				prStage:         stage,
				task:            *t.Task,
				template:        *pr.Pipeline.Template,
				runAfter:        runAfter,
				tektonTaskName:  tektonTaskName,
				tasks:           taskMap,
				modifyParams:    args.Args,
				fromPipelineRun: true,
			})
			if err != nil {
				return nil, err
			}
			// validate PipelineTask
			for _, w := range pipelineTask.Workspaces {
				if w.Name == "" {
					log.Errorf("pipeline_run %d pipeline_run_stage %d pipeline_run_task %d [%s] [%s] got empty TaskWorkspace", pr.ID, stage.ID, t.ID, t.Type, tektonTaskName)
					return nil, fmt.Errorf("task empty TaskWorksapce")
				}
			}
			lastTask = pipelineTask
			// 看任务是不是并行阶段的前一个任务，需要填充到所有并行任务的runAfter字段 例如：runAfter: [task1]
			if pr.IsTaskIdBeforeFirstParallelTask(t.ID) {
				BeforeFirstParallelTaskPipelineTaskTektonName = []string{pipelineTask.Name}
			}
			// 看任务是不是并行阶段的后一个任务，并行任务数组需要填充到后一个任务的runAfter字段 例如：runAfter: [并行任务1, 并行任务2]
			if stage.Type == constants.STAGE_PARALLEL.String() {
				ParallelTaskPipelineTaskTektonNames = append(ParallelTaskPipelineTaskTektonNames, pipelineTask.Name)
			}
			tasks = append(tasks, *pipelineTask)
			taskMap[t.GetType()] = pipelineTask

			// merge Params and Workspaces
			for _, param := range fields.Params {
				if _, exist := paramsName[param.Name]; !exist {
					paramsName[param.Name] = struct{}{}
					pipelineParams = append(pipelineParams, param)
				}
			}
			for _, workspace := range fields.Workspaces {
				if workspace.Name == "" {
					log.Errorf("pipeline_run %d pipeline_run_stage %d pipeline_run_task %d [%s] [%s] got empty PipelineWorkspace", pr.ID, stage.ID, t.ID, t.Type, tektonTaskName)
					return nil, fmt.Errorf("task empty PipelineWorkspace")
				}
				if _, exist := workspacesName[workspace.Name]; !exist {
					workspacesName[workspace.Name] = struct{}{}
					workspaces = append(workspaces, workspace)
				}
			}
		}
	}

	pipeline.Spec.Workspaces = workspaces
	pipeline.Spec.Params = pipelineParams
	pipeline.Spec.Tasks = tasks
	return pipeline, nil
}

func (c *Client) generatePipeline(ctx context.Context, template dao.Template, args model.PipelineRunArgs) (pipeline *v1beta1.Pipeline, err error) {
	pipeline = &v1beta1.Pipeline{
		ObjectMeta: metav1.ObjectMeta{
			Name:      c.generatePipelineName(template, args),
			Namespace: c.namespace,
			Annotations: map[string]string{
				string(constants.ProjectIdKey):  strconv.FormatInt(template.ProjectId, 10),
				string(constants.TemplateIdKey): strconv.FormatInt(template.ID, 10),
			},
		},
		Spec: v1beta1.PipelineSpec{},
	}
	// create Tekton PipelineTask and Pipeline Params
	var pipelineParams []v1beta1.ParamSpec
	var tasks []v1beta1.PipelineTask
	var workspaces []v1beta1.PipelineWorkspaceDeclaration
	taskMap := make(map[constants.Task]*v1beta1.PipelineTask)
	paramsName := make(map[string]struct{})
	workspacesName := make(map[string]struct{})
	var lastTask *v1beta1.PipelineTask
	var (
		runAfter                                      []string
		BeforeFirstParallelTaskPipelineTaskTektonName []string
		ParallelTaskPipelineTaskTektonNames           []string
	)
	ciConfigStages := conf.GetCiStagesConfig()
	for _, stageId := range template.GetSequence() {
		stage := template.GetStageById(ctx, stageId)
		if stage == nil {
			return nil, fmt.Errorf("stage [%d] not in template [%d]", stageId, template.ID)
		}
		isCiStage := tools.Any(ciConfigStages, func(r string) bool {
			return strings.EqualFold(r, stage.Type)
		})
		if !isCiStage {
			continue
		}
		for _, taskId := range stage.GetSequence() {
			if stage.Type == constants.STAGE_PARALLEL.String() {
				runAfter = BeforeFirstParallelTaskPipelineTaskTektonName
			} else if template.IsTaskIdAfterFirstParallelTask(taskId) {
				runAfter = ParallelTaskPipelineTaskTektonNames
			} else if lastTask != nil {
				runAfter = []string{lastTask.Name}
			}
			t := stage.GetTaskById(taskId)
			if t == nil {
				return nil, fmt.Errorf("task [%d] not in stage [%d]", taskId, stage.ID)
			}
			generator, err := c.getTaskGenerator(t.Type, template.Language)
			tektonTaskName, err := c.getTektonTaskname(t.Type, template.Language)
			if err != nil {
				return nil, err
			}
			pipelineTask, fields, err := generator.generatePipelineTaskAndFields(generatePipelineTaskArgs{
				task:           *t,
				stage:          *stage,
				template:       template,
				runAfter:       runAfter,
				tektonTaskName: tektonTaskName,
				tasks:          taskMap,
				modifyParams:   args.Args,
			})
			if err != nil {
				return nil, err
			}
			// validate PipelineTask
			for _, w := range pipelineTask.Workspaces {
				if w.Name == "" {
					log.Errorf("template %d stage %d task %d [%s] [%s] got empty TaskWorkspace", template.ID, stage.ID, t.ID, t.Type, tektonTaskName)
					return nil, fmt.Errorf("task empty TaskWorksapce")
				}
			}
			lastTask = pipelineTask
			// 看任务是不是并行阶段的前一个任务，需要填充到所有并行任务的runAfter字段 例如：runAfter: [task1]
			if template.IsTaskIdBeforeFirstParallelTask(taskId) {
				BeforeFirstParallelTaskPipelineTaskTektonName = []string{pipelineTask.Name}
			}
			// 看任务是不是并行阶段的后一个任务，并行任务数组需要填充到后一个任务的runAfter字段 例如：runAfter: [并行任务1, 并行任务2]
			if stage.Type == constants.STAGE_PARALLEL.String() {
				ParallelTaskPipelineTaskTektonNames = append(ParallelTaskPipelineTaskTektonNames, pipelineTask.Name)
			}
			tasks = append(tasks, *pipelineTask)
			taskMap[t.GetType()] = pipelineTask

			// merge Params and Workspaces
			for _, param := range fields.Params {
				if _, exist := paramsName[param.Name]; !exist {
					paramsName[param.Name] = struct{}{}
					pipelineParams = append(pipelineParams, param)
				}
			}
			for _, workspace := range fields.Workspaces {
				if workspace.Name == "" {
					log.Errorf("template %d stage %d task %d [%s] [%s] got empty PipelineWorkspace", template.ID, stage.ID, t.ID, t.Type, tektonTaskName)
					return nil, fmt.Errorf("task empty PipelineWorkspace")
				}
				if _, exist := workspacesName[workspace.Name]; !exist {
					workspacesName[workspace.Name] = struct{}{}
					workspaces = append(workspaces, workspace)
				}
			}
		}
	}

	pipeline.Spec.Workspaces = workspaces
	pipeline.Spec.Params = pipelineParams
	pipeline.Spec.Tasks = tasks
	return pipeline, nil
}

func (c *Client) CreatePipelineFromPr(ctx context.Context, pr *dao.PipelineRun, args model.PipelineRunArgs) (*v1beta1.Pipeline, error) {
	pipeline, err := c.generatePipelineFromPr(*pr, args)
	if err != nil {
		log.Errorf("create generate Tekton Pipeline fail %v", err)
		return nil, fmt.Errorf("%w %w", pipeerr.ErrTektonSyncFailed, err)
	}
	if pkgcontext.RequestID(ctx) != "" {
		pipeline.Annotations[constants.RequestIDKey.String()] = pkgcontext.RequestID(ctx)
	}
	log.Debugf("generate Tekton Pipeline", zap.Any("pipeline", *pipeline))
	pipeline, err = c.tektonClient.TektonV1beta1().Pipelines(c.namespace).Create(ctx, pipeline, metav1.CreateOptions{})
	if err != nil {
		log.Errorf("create Tekton Pipeline to k8s fail %v", err)
		return nil, fmt.Errorf("%w %w", pipeerr.ErrTektonSyncFailed, err)
	}
	log.Infof("create Tekton Pipeline Namespace[%s] Name[%s] success", pipeline.Namespace, pipeline.Name)
	return pipeline, nil
}

func (c *Client) CreatePipeline(ctx context.Context, template *dao.Template, args model.PipelineRunArgs) (*v1beta1.Pipeline, error) {
	pipeline, err := c.generatePipeline(ctx, *template, args)
	if err != nil {
		log.Errorf("create generate Tekton Pipeline fail %v", err)
		return nil, fmt.Errorf("%w %w", pipeerr.ErrTektonSyncFailed, err)
	}
	if pkgcontext.RequestID(ctx) != "" {
		pipeline.Annotations[constants.RequestIDKey.String()] = pkgcontext.RequestID(ctx)
	}
	log.Debugf("generate Tekton Pipeline", zap.Any("pipeline", *pipeline))
	pipeline, err = c.tektonClient.TektonV1beta1().Pipelines(c.namespace).Create(ctx, pipeline, metav1.CreateOptions{})
	if err != nil {
		log.Errorf("create Tekton Pipeline to k8s fail %v", err)
		return nil, fmt.Errorf("%w %w", pipeerr.ErrTektonSyncFailed, err)
	}
	log.Infof("create Tekton Pipeline Namespace[%s] Name[%s] success", pipeline.Namespace, pipeline.Name)
	return pipeline, nil
}

func (c *Client) UpdatePipeline(ctx context.Context, template *dao.Template, currentPileline v1beta1.Pipeline) (*v1beta1.Pipeline, error) {
	pipeline, err := c.generatePipeline(ctx, *template, model.PipelineRunArgs{})
	if err != nil {
		log.Errorf("generate Tekton Pipeline fail %v", err)
		return nil, fmt.Errorf("%w %w", pipeerr.ErrTektonSyncFailed, err)
	}
	pipeline.ObjectMeta.ResourceVersion = currentPileline.ObjectMeta.ResourceVersion
	pipeline, err = c.tektonClient.TektonV1beta1().Pipelines(c.namespace).Update(ctx, pipeline, metav1.UpdateOptions{})
	if err != nil {
		log.Errorf("update Tekton Pipeline to k8s fail %v", err)
		return nil, fmt.Errorf("%w %w", pipeerr.ErrTektonSyncFailed, err)
	}
	log.Infof("update Tekton Pipeline Namespace[%s] Name[%s] success", pipeline.Namespace, pipeline.Name)
	return pipeline, nil
}

func (c *Client) GetPipeline(ctx context.Context, namespace, name string) (*v1beta1.Pipeline, error) {
	pipeline, err := c.tektonClient.TektonV1beta1().Pipelines(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			log.Errorf("get Tekton Pipeline Namespace[%s] Name[%s] not found", namespace, name)
			return nil, pipeerr.ErrTektonPipelineNotFound
		}
		log.Errorf("get Tekton Pipeline Namespace[%s] Name[%s] fail %v", namespace, name, err)
		return nil, err
	}
	return pipeline, nil
}

type generatePipelineTaskArgs struct {
	task            dao.Task
	prTask          dao.PipelineRunTask
	stage           dao.Stage
	prStage         dao.PipelineRunStage
	template        dao.Template
	runAfter        []string
	tektonTaskName  string
	tasks           map[constants.Task]*v1beta1.PipelineTask // all generated PipelineTask
	modifyParams    map[string]string
	fromPipelineRun bool
}

type pipelineTaskGenerator interface {
	// generatePipelineTaskAndFields generate PipelineTask and some fields used by PipelineSpec, such as Workspaces, Results, Params and so on.
	generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error)
}

type pipeSpecFields struct {
	Workspaces []v1beta1.PipelineWorkspaceDeclaration `json:"workspaces,omitempty"`
	Params     []v1beta1.ParamSpec                    `json:"params,omitempty"`
}

type generatorBase struct {
	taskType constants.Task
	flags    []string // flags designed for lookup Tekton Task Name and related PipelineTaskGenerator. For now, just language.
}

func (g *generatorBase) key() string {
	return createLookupKey(string(g.taskType), g.flags)
}

func (g *generatorBase) getPipelineWorkspaceNameByTaskWorkspaceName(taskWorkspaceName string) string {
	// map[taskWorkspaceName]pipelineWorkspaceName
	workspaceMap := map[string]string{
		"source":                  "share-data",
		"output":                  "share-data",
		"cache":                   "cache",
		"buildCache":              "buildCache",
		"pkgCache":                "pkgCache",
		"sonarCache":              "sonarCache",
		"credential":              "credential",
		"sonar-credentials":       "sonar-credentials",
		"gitlab-http-auth-secret": "gitlab-http-auth-secret",
	}
	return workspaceMap[taskWorkspaceName]
}

func createBasePipelineTask(arg generatePipelineTaskArgs) *v1beta1.PipelineTask {
	var t *v1beta1.PipelineTask
	if arg.fromPipelineRun {
		t = &v1beta1.PipelineTask{
			Name: fmt.Sprintf("%s-prstage-%d-prtask-%d", arg.tektonTaskName, arg.prStage.ID, arg.prTask.ID),
			TaskRef: &v1beta1.TaskRef{
				Name: arg.tektonTaskName,
			},
		}
	} else {
		t = &v1beta1.PipelineTask{
			Name: fmt.Sprintf("%s-stage-%d-task-%d", arg.tektonTaskName, arg.stage.ID, arg.task.ID),
			TaskRef: &v1beta1.TaskRef{
				Name: arg.tektonTaskName,
			},
		}
	}

	if arg.runAfter != nil && len(arg.runAfter) > 0 {
		t.RunAfter = arg.runAfter
	}
	return t
}

type pullCodeGenerator struct {
	generatorBase
}

func (g *pullCodeGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	// infra: git-clone.yaml
	pTask = createBasePipelineTask(arg)
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "output", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("output")}, // Name --> Task Namespace Name; Workspace --> PipelineSpec.Workspaces
		{Name: "basic-auth", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("gitlab-http-auth-secret")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "url", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.repo-url)"}},
		{Name: "revision", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.revision)"}},
		{Name: "submodules", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.submodules)"}},
		{Name: "needMerge", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.needMerge)"}},
		{Name: "sourceBranch", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.sourceBranch)"}},
		{Name: "depth", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.depth)"}},
		{Name: "remote", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.remote)"}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		v1beta1.ParamSpec{
			Name: "repo-url",
			Type: v1beta1.ParamTypeString, Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""},
		},
		v1beta1.ParamSpec{
			Name:    "revision",
			Type:    v1beta1.ParamTypeString,
			Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""},
		},
		v1beta1.ParamSpec{
			Name:    "submodules",
			Type:    v1beta1.ParamTypeString,
			Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""},
		},
		v1beta1.ParamSpec{
			Name:    "sourceBranch",
			Type:    v1beta1.ParamTypeString,
			Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""},
		},
		v1beta1.ParamSpec{
			Name:    "needMerge",
			Type:    v1beta1.ParamTypeString,
			Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""},
		},
		v1beta1.ParamSpec{
			Name:    "depth",
			Type:    v1beta1.ParamTypeString,
			Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "1"},
		},
		v1beta1.ParamSpec{
			Name:    "remote",
			Type:    v1beta1.ParamTypeString,
			Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""},
		},
	}
	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("output")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("gitlab-http-auth-secret")},
	}

	/*
		    In case other task will use this Result.
		    results:
			   - name: commit
			     description: The precise commit SHA that was fetched by this Task.
			   - name: url
			     description: The precise URL that was fetched by this Task.
			   - name: committer-date
			     description: The epoch timestamp of the commit that was fetched by this Task.
	*/
	return
}

type golangUnittestGenerator struct {
	generatorBase
}

func (g *golangUnittestGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: "pkgCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
		{Name: "buildCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("buildCache")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "testCommand", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.testCommand)"}},
		{Name: "image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.goTestImage)"}},
	}

	fields = new(pipeSpecFields)
	// pipeline spec fields
	fields.Params = v1beta1.ParamSpecs{
		{Name: "testCommand", Type: v1beta1.ParamTypeString},
		{Name: "goTestImage", Type: v1beta1.ParamTypeString, Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "docker.io/library/golang:latest"}},
	}
	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("buildCache")},
	}
	return
}

type golangBuildGenerator struct {
	generatorBase
}

func (g *golangBuildGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: "pkgCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
		{Name: "buildCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("buildCache")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "buildCommand", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.buildCommand)"}},
		{Name: "image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.golang-image)"}},
	}

	fields = new(pipeSpecFields)
	// pipeline spec fields
	fields.Params = v1beta1.ParamSpecs{
		{Name: "buildCommand", Type: v1beta1.ParamTypeString, Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "buildCommand"}},
		{Name: "golang-image", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "docker.io/library/golang:latest"}},
	}
	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("buildCache")},
	}
	return
}

type javaUnitTestGenerator struct {
	generatorBase
}

func (g *javaUnitTestGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: "pkgCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "version", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.jdk-version)"}},
		{Name: "command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.maven-test-command)"}},
		{Name: "image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.maven-image)"}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "jdk-version", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "3.8.6-jdk-8"}},
		{Name: "maven-test-command"},
		{Name: "maven-image", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "cr.ttyuyin.com/devops/tekton/jdk1.8-maven3.6-yw-nexus:latest"}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	return
}

type javaBuildGenerator struct {
	generatorBase
}

func (g *javaBuildGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: "pkgCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "version", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.jdk-version)"}},
		{Name: "command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.maven-build-command)"}},
		{Name: "image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.java-image)"}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "jdk-version", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "3.8.6-jdk-8"}},
		{Name: "maven-build-command"},
		{Name: "java-image", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "cr.ttyuyin.com/devops/tekton/jdk1.8-maven3.6-yw-nexus:latest"}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	return
}

type nodeUnitTestGenerator struct {
	generatorBase
}

func (g *nodeUnitTestGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: "cache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("cache")},
		{Name: "pkgCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.node-test-command)"}},
		{Name: "image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.node-test-image)"}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "node-test-command"},
		{Name: "node-test-image", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "docker.io/library/node:18"}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("cache")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	return
}

type nodeBuildGenerator struct {
	generatorBase
}

func (g *nodeBuildGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: "cache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("cache")},
		{Name: "pkgCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.node-build-command)"}},
		{Name: "image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.node-build-image)"}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "node-build-command"},
		{Name: "node-build-image", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "docker.io/library/node:18"}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("cache")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	return
}

type cppBuildGenerator struct {
	generatorBase
}

func (g *cppBuildGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "cppCommand", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.cppBuildCommand)"}},
		{Name: "cppImage", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.cppBuildImage)"}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "cppBuildCommand"},
		{Name: "cppBuildImage", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "cr.ttyuyin.com/devops/ubuntu-cpp:latest"}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
	}
	return
}

type cppUnitTestGenerator struct {
	generatorBase
}

func (g *cppUnitTestGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "cppCommand", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.cppTestCommand)"}},
		{Name: "cppImage", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.cppTestImage)"}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "cppTestCommand"},
		{Name: "cppTestImage", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "cr.ttyuyin.com/devops/ubuntu-cpp:latest"}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
	}
	return
}

type pythonBuildGenerator struct {
	generatorBase
}

func (g *pythonBuildGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: "pkgCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.python-build-command)"}},
		{Name: "image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.python-build-image)"}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "python-build-command"},
		{Name: "python-build-image", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "cr.ttyuyin.com/public/python:3.9"}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	return
}

type pythonUnitTestGenerator struct {
	generatorBase
}

func (g *pythonUnitTestGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: "pkgCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "command", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.python-test-command)"}},
		{Name: "image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.python-test-image)"}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "python-test-command"},
		{Name: "python-test-image", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "cr.ttyuyin.com/public/python:3.9"}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	return
}

type customShellGenerator struct {
	generatorBase
}

func (g *customShellGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)

	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "output", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("output")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "version", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.shell-version)"}},
		{Name: "contents", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: fmt.Sprintf("$(params.shell-contents-%d)", arg.task.ID)}},
		{Name: "image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: fmt.Sprintf("$(params.shell-image-%d)", arg.task.ID)}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "shell-version", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "5.1"}},
		{Name: fmt.Sprintf("shell-contents-%d", arg.task.ID), Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""}},
		{Name: fmt.Sprintf("shell-image-%d", arg.task.ID), Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "docker.io/library/bash:5.1"}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("output")},
	}
	/*
		    results:
			   - name: filename
			     description: The precise filename that was fetched by this Task.
			   - name: filepath
			     description: The precise filepath that was fetched by this Task.
	*/
	return
}

type checkStyleGenerator struct {
	generatorBase
}

func (g *checkStyleGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)

	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: "pkgCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
		{Name: "buildCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("buildCache")},
	}
	pTask.Params = []v1beta1.Param{
		{Name: "checkStyleLanguageTag", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.checkStyleLanguageTag)"}},
		{Name: "checkStyleRule", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.checkStyleRule)"}},
		{Name: "checkStyleCommand", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.checkStyleCommand)"}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "checkStyleLanguageTag", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "go1.19"}},
		{Name: "checkStyleRule", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""}},
		{Name: "checkStyleCommand", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("buildCache")},
	}
	/*
		   - default: ''
		     name: checkStyleRule
		     type: string
		   - default: ''
			 name: checkStyleCommand
			 type: string
	*/
	return
}

type scaScanGenerator struct {
	generatorBase
}

func (g *scaScanGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)

	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
	}

	pTask.Params = []v1beta1.Param{
		{Name: "APP_NAME", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.APP_NAME)"}},
		{Name: "CMDB_ID", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.CMDB_ID)"}},
		{Name: "BUILD_NUMBER", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.BUILD_NUMBER)"}},
		{Name: "PROJECT_NAME", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.PROJECT_NAME)"}},
		{Name: "IMAGE_ADDRESS", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.IMAGE_ADDRESS)"}},
		{Name: "BUILD_PATH", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.BUILD_PATH)"}},
	}
	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "APP_NAME", Type: v1beta1.ParamTypeString},
		{Name: "CMDB_ID", Type: v1beta1.ParamTypeString},
		{Name: "BUILD_NUMBER", Type: v1beta1.ParamTypeString},
		{Name: "PROJECT_NAME", Type: v1beta1.ParamTypeString},
		{Name: "IMAGE_ADDRESS", Type: v1beta1.ParamTypeString, Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "cr.ttyuyin.com/security/sca-cli:latest"}},
		{Name: "BUILD_PATH", Type: v1beta1.ParamTypeString},
	}
	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
	}
	return
}

type sonarScanGenerator struct {
	generatorBase
}

func (g *sonarScanGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)

	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: "sonar-credentials", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("sonar-credentials")},
		{Name: "sonarCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("sonarCache")},
	}

	pTask.Params = []v1beta1.Param{
		// task params
		{Name: "SONAR_PROJECT_KEY", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.SONAR_PROJECT_KEY)"}},
		// biz params
		{Name: "SONAR_EXCLUSIONS", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.SONAR_EXCLUSIONS)"}},
		{Name: "SONAR_TEST_INCLUSIONS", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.SONAR_TEST_INCLUSIONS)"}},
		{Name: "SONAR_FLAGS", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.SONAR_FLAGS)"}},
		{Name: "SONAR_QUALITY_GATEWAY", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.SONAR_QUALITY_GATEWAY)"}},
		{Name: "SONAR_QUALITY_BUG", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.SONAR_QUALITY_BUG)"}},
		{Name: "SONAR_QUALITY_VUL", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.SONAR_QUALITY_VUL)"}},
		{Name: "SONAR_QUALITY_SMELL", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.SONAR_QUALITY_SMELL)"}},
		{Name: "SONAR_QUALITY_COVERAGE", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.SONAR_QUALITY_COVERAGE)"}},
		{Name: "LANGUAGE", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.LANGUAGE)"}},
		{Name: "QUALITY_PLATFORM_URL", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.QUALITY_PLATFORM_URL)"}},
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		// task params
		{Name: "SONAR_PROJECT_KEY", Type: v1beta1.ParamTypeString}, // appName+pipelinerunID
		// biz params
		{Name: "SONAR_EXCLUSIONS", Type: v1beta1.ParamTypeString},
		{Name: "SONAR_TEST_INCLUSIONS", Type: v1beta1.ParamTypeString},
		{Name: "SONAR_FLAGS", Type: v1beta1.ParamTypeString},
		{Name: "SONAR_QUALITY_GATEWAY", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "false"}},
		{Name: "SONAR_QUALITY_BUG", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "0"}},
		{Name: "SONAR_QUALITY_VUL", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "0"}},
		{Name: "SONAR_QUALITY_SMELL", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "0"}},
		{Name: "SONAR_QUALITY_COVERAGE", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "0"}},
		{Name: "LANGUAGE", Type: v1beta1.ParamTypeString},
		// http://*************:8080 是 sonar 扫描结果 JSON 数据的接口，目前只有一套环境。所以这里 hardcode 了。
		{Name: "QUALITY_PLATFORM_URL", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "http://*************:8080"}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("sonar-credentials")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("sonarCache")},
	}

	/*
	     results:
	   - name: bug-num
	     description: The bugs num of this scan
	   - name: vulnerability-num
	     description: The vulnerability_num of this scan
	   - name: code-smell-num
	     description: The code_smell num of this scan
	   - name: coverage
	     description: The coverage num of this scan
	   - name: fail-reason
	     description: The failed reason
	*/
	return
}

type buildPushImageGenerator struct {
	generatorBase
}

func (g *buildPushImageGenerator) generatePipelineTaskAndFields(arg generatePipelineTaskArgs) (pTask *v1beta1.PipelineTask, fields *pipeSpecFields, err error) {
	pTask = createBasePipelineTask(arg)
	pullCodeTaskName := ""
	pTask.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "source", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: "dockerconfig", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("credential")},
		{Name: "pkgCache", Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}
	var pushImage model.GeneratePushImage
	if arg.fromPipelineRun {
		if err := json.Unmarshal(arg.prTask.Config, &pushImage); err != nil {
			log.Errorf("get Tekton buildPushImageGenerator prTaskId[%d] err:[%v]", arg.prTask.ID, err.Error())
		}
	} else {
		if err := json.Unmarshal(arg.task.Config, &pushImage); err != nil {
			log.Errorf("get Tekton buildPushImageGenerator taskId[%d] err:[%v]", arg.task.ID, err.Error())
		}
	}
	pTask.Workspaces = append(pTask.Workspaces, v1beta1.WorkspacePipelineTaskBinding{Name: "cache",
		Workspace: g.getPipelineWorkspaceNameByTaskWorkspaceName("cache")})
	pTask.Params = []v1beta1.Param{
		//{Name: "IMAGE", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: fmt.Sprintf("$(params.registry-url)/$(params.app-name):$(tasks.%s.results.commit)", pullCodeTaskName)}},
		{Name: "DOCKERFILE", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.DOCKERFILE)"}},
		{Name: "CONTEXT", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.CONTEXT)"}},
		//array example https://github.com/tektoncd/pipeline/blob/main/examples/v1beta1/pipelineruns/beta/pipelinerun-param-array-indexing.yaml
		{Name: "EXTRA_ARGS", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.EXTRA_ARGS[*])"}},
		{Name: "GOPROXY", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.GOPROXY)"}},
		{Name: "CACHE", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.BUILD_CACHE)"}},
		{Name: "DOCKERFILE_CONTENT", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.DOCKERFILE_CONTENT)"}},
		{Name: "EnableOCI", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "$(params.EnableOCI)"}},
	}

	if arg.modifyParams != nil && arg.modifyParams["commit"] != "" {
		pTask.Params = append(pTask.Params, v1beta1.Param{Name: "IMAGE", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: fmt.Sprintf("$(params.registry-url)/$(params.PROJECT_ID)/$(params.app-name):V$(params.timeStamp)-%s", arg.modifyParams["commit"])}})
	} else {
		pullCodeTaskName = arg.tasks[constants.TASK_PULL_CODE].Name
		pTask.Params = append(pTask.Params, v1beta1.Param{Name: "IMAGE", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: fmt.Sprintf("$(params.registry-url)/$(params.PROJECT_ID)/$(params.app-name):V$(params.timeStamp)-$(tasks.%s.results.commit)", pullCodeTaskName)}})
	}

	fields = new(pipeSpecFields)
	fields.Params = v1beta1.ParamSpecs{
		{Name: "registry-url", Type: v1beta1.ParamTypeString, Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""}},
		{Name: "app-name", Type: v1beta1.ParamTypeString, Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""}},
		{Name: "timeStamp", Type: v1beta1.ParamTypeString, Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""}},
		{Name: "PROJECT_ID", Type: v1beta1.ParamTypeString, Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""}},
		{Name: "DOCKERFILE", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "./Dockerfile"}},
		{Name: "CONTEXT", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "./"}},
		{Name: "EXTRA_ARGS", Type: v1beta1.ParamTypeArray, Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeArray, ArrayVal: []string{}}},
		{Name: "GOPROXY", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "http://yw-nexus.ttyuyin.com:8081/repository/group-go/"}},
		{Name: "BUILD_CACHE", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "true"}},
		{Name: "DOCKERFILE_CONTENT", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ""}},
		{Name: "EnableOCI", Default: &v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "true"}},
	}

	fields.Workspaces = []v1beta1.PipelineWorkspaceDeclaration{
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("source")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("credential")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("cache")},
		{Name: g.getPipelineWorkspaceNameByTaskWorkspaceName("pkgCache")},
	}

	/*
	     results:
	   - name: IMAGE_DIGEST
	     description: Digest of the image just built.
	   - name: IMAGE_URL
	     description: URL of the image just built.
	*/
	return
}
