package tekton

import (
	"testing"

	"52tt.com/cicd/services/pipeline/internal/dao"
	"52tt.com/cicd/services/pipeline/pkg/tekton/utils"
	"github.com/stretchr/testify/assert"
)

func TestApplyScriptParams(t *testing.T) {
	run := dao.PipelineRun{
		Pipeline: &dao.Pipeline{
			AppName:   "myapp",
			BuildPath: "/path/to/build",
		},
		BuildNumber:  123,
		RepoAddress:  "https://github.com/myorg/myrepo.git",
		Branch:       "main",
		SourceBranch: "feature-branch",
	}

	script := "$buildPath $repoAddr $cacheDir $serviceName $currentBranch $sourceBranch"
	expected := "/path/to/build https://github.com/myorg/myrepo.git /cache/myapp/ myapp main feature-branch"
	result := utils.ApplyScriptParams(script, run)

	assert.Equal(t, expected, result)

	multiParamScripte := "$buildPath $buildPath test-$repo- $repoAddr $cacheDir $serviceName $currentBranch $sourceBranch"
	expected = "/path/to/build /path/to/build test-$repo- https://github.com/myorg/myrepo.git /cache/myapp/ myapp main feature-branch"
	result = utils.ApplyScriptParams(multiParamScripte, run)

	assert.Equal(t, expected, result)

}
