// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.4
// source: quwan/cloud/constack/v1alpha/kubeconfig.proto

package constack

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetConfigRequest
type GetConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// username, e.g.: tt-admin
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// cluster, e.g.: k8s-tc-bj-1-yunwei
	Cluster   string  `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace *string `protobuf:"bytes,3,opt,name=namespace,proto3,oneof" json:"namespace,omitempty"`
}

func (x *GetConfigRequest) Reset() {
	*x = GetConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigRequest) ProtoMessage() {}

func (x *GetConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigRequest.ProtoReflect.Descriptor instead.
func (*GetConfigRequest) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescGZIP(), []int{0}
}

func (x *GetConfigRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *GetConfigRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *GetConfigRequest) GetNamespace() string {
	if x != nil && x.Namespace != nil {
		return *x.Namespace
	}
	return ""
}

// GetConfigResponse
type GetConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// kubeconfig, e.g.: apiVersion: v1....
	Kubeconfig string `protobuf:"bytes,1,opt,name=kubeconfig,proto3" json:"kubeconfig,omitempty"`
}

func (x *GetConfigResponse) Reset() {
	*x = GetConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigResponse) ProtoMessage() {}

func (x *GetConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigResponse.ProtoReflect.Descriptor instead.
func (*GetConfigResponse) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescGZIP(), []int{1}
}

func (x *GetConfigResponse) GetKubeconfig() string {
	if x != nil {
		return x.Kubeconfig
	}
	return ""
}

type ClusterReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群所属环境
	Env string `protobuf:"bytes,1,opt,name=env,proto3" json:"env,omitempty"`
	// 云商
	Cloud string `protobuf:"bytes,2,opt,name=cloud,proto3" json:"cloud,omitempty"`
	// 地域
	RegionId string `protobuf:"bytes,3,opt,name=regionId,proto3" json:"regionId,omitempty"`
	IsReady  string `protobuf:"bytes,4,opt,name=isReady,proto3" json:"isReady,omitempty"`
}

func (x *ClusterReq) Reset() {
	*x = ClusterReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClusterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterReq) ProtoMessage() {}

func (x *ClusterReq) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterReq.ProtoReflect.Descriptor instead.
func (*ClusterReq) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescGZIP(), []int{2}
}

func (x *ClusterReq) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *ClusterReq) GetCloud() string {
	if x != nil {
		return x.Cloud
	}
	return ""
}

func (x *ClusterReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *ClusterReq) GetIsReady() string {
	if x != nil {
		return x.IsReady
	}
	return ""
}

type ClusterResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64          `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Objs  []*ClusterInfo `protobuf:"bytes,2,rep,name=objs,proto3" json:"objs,omitempty"`
}

func (x *ClusterResp) Reset() {
	*x = ClusterResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClusterResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterResp) ProtoMessage() {}

func (x *ClusterResp) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterResp.ProtoReflect.Descriptor instead.
func (*ClusterResp) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescGZIP(), []int{3}
}

func (x *ClusterResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ClusterResp) GetObjs() []*ClusterInfo {
	if x != nil {
		return x.Objs
	}
	return nil
}

type ClusterInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 集群描述
	Desc string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	// 云商
	Cloud string `protobuf:"bytes,3,opt,name=cloud,proto3" json:"cloud,omitempty"`
	// kubeconfig是否可用
	IsReady int32 `protobuf:"varint,4,opt,name=isReady,proto3" json:"isReady,omitempty"`
	// 地域
	RegionId string `protobuf:"bytes,5,opt,name=regionId,proto3" json:"regionId,omitempty"`
	// 云商ID
	CloudId string `protobuf:"bytes,6,opt,name=cloudId,proto3" json:"cloudId,omitempty"`
	// 集群ID
	ClusterId int32 `protobuf:"varint,7,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
	// 集群所属环境
	Env string `protobuf:"bytes,8,opt,name=env,proto3" json:"env,omitempty"`
	// 集群类型
	ApiServer string `protobuf:"bytes,9,opt,name=apiServer,proto3" json:"apiServer,omitempty"`
}

func (x *ClusterInfo) Reset() {
	*x = ClusterInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClusterInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterInfo) ProtoMessage() {}

func (x *ClusterInfo) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterInfo.ProtoReflect.Descriptor instead.
func (*ClusterInfo) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescGZIP(), []int{4}
}

func (x *ClusterInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ClusterInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ClusterInfo) GetCloud() string {
	if x != nil {
		return x.Cloud
	}
	return ""
}

func (x *ClusterInfo) GetIsReady() int32 {
	if x != nil {
		return x.IsReady
	}
	return 0
}

func (x *ClusterInfo) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *ClusterInfo) GetCloudId() string {
	if x != nil {
		return x.CloudId
	}
	return ""
}

func (x *ClusterInfo) GetClusterId() int32 {
	if x != nil {
		return x.ClusterId
	}
	return 0
}

func (x *ClusterInfo) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *ClusterInfo) GetApiServer() string {
	if x != nil {
		return x.ApiServer
	}
	return ""
}

type NamespaceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名称
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 集群ID
	ClusterId int32 `protobuf:"varint,2,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
	// 业务线名称
	Business string `protobuf:"bytes,3,opt,name=business,proto3" json:"business,omitempty"`
	// 开发团队
	DevGroups string `protobuf:"bytes,4,opt,name=devGroups,proto3" json:"devGroups,omitempty"`
}

func (x *NamespaceReq) Reset() {
	*x = NamespaceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceReq) ProtoMessage() {}

func (x *NamespaceReq) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceReq.ProtoReflect.Descriptor instead.
func (*NamespaceReq) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescGZIP(), []int{5}
}

func (x *NamespaceReq) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *NamespaceReq) GetClusterId() int32 {
	if x != nil {
		return x.ClusterId
	}
	return 0
}

func (x *NamespaceReq) GetBusiness() string {
	if x != nil {
		return x.Business
	}
	return ""
}

func (x *NamespaceReq) GetDevGroups() string {
	if x != nil {
		return x.DevGroups
	}
	return ""
}

type NamespaceResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	// 命名空间所属集群详细信息
	ClusterInfo *ClusterInfo     `protobuf:"bytes,2,opt,name=clusterInfo,proto3" json:"clusterInfo,omitempty"`
	Objs        []*NamespaceInfo `protobuf:"bytes,3,rep,name=objs,proto3" json:"objs,omitempty"`
}

func (x *NamespaceResp) Reset() {
	*x = NamespaceResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceResp) ProtoMessage() {}

func (x *NamespaceResp) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceResp.ProtoReflect.Descriptor instead.
func (*NamespaceResp) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescGZIP(), []int{6}
}

func (x *NamespaceResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *NamespaceResp) GetClusterInfo() *ClusterInfo {
	if x != nil {
		return x.ClusterInfo
	}
	return nil
}

func (x *NamespaceResp) GetObjs() []*NamespaceInfo {
	if x != nil {
		return x.Objs
	}
	return nil
}

type NamespaceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名称
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 命名空间所属业务线
	Business string `protobuf:"bytes,3,opt,name=business,proto3" json:"business,omitempty"`
	// UUID
	Uuid string `protobuf:"bytes,4,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// 开发团队
	DevGroups string `protobuf:"bytes,5,opt,name=devGroups,proto3" json:"devGroups,omitempty"`
	// 集群标签
	Labels map[string]string `protobuf:"bytes,6,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *NamespaceInfo) Reset() {
	*x = NamespaceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceInfo) ProtoMessage() {}

func (x *NamespaceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceInfo.ProtoReflect.Descriptor instead.
func (*NamespaceInfo) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescGZIP(), []int{7}
}

func (x *NamespaceInfo) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *NamespaceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamespaceInfo) GetBusiness() string {
	if x != nil {
		return x.Business
	}
	return ""
}

func (x *NamespaceInfo) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *NamespaceInfo) GetDevGroups() string {
	if x != nil {
		return x.DevGroups
	}
	return ""
}

func (x *NamespaceInfo) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

var File_quwan_cloud_constack_v1alpha_kubeconfig_proto protoreflect.FileDescriptor

var file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2f, 0x6b,
	0x75, 0x62, 0x65, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1c, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x22, 0x79, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0x33, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x6b, 0x75, 0x62, 0x65, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6b, 0x75, 0x62, 0x65, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x6a, 0x0a,
	0x0a, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x65,
	0x6e, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x69, 0x73, 0x52, 0x65, 0x61, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x69, 0x73, 0x52, 0x65, 0x61, 0x64, 0x79, 0x22, 0x62, 0x0a, 0x0b, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3d,
	0x0a, 0x04, 0x6f, 0x62, 0x6a, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x71,
	0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x43, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6f, 0x62, 0x6a, 0x73, 0x22, 0xe9, 0x01,
	0x0a, 0x0b, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x69,
	0x73, 0x52, 0x65, 0x61, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x69, 0x73,
	0x52, 0x65, 0x61, 0x64, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x70, 0x69, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x70, 0x69, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0x80, 0x01, 0x0a, 0x0c, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x1c,
	0x0a, 0x09, 0x64, 0x65, 0x76, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0xb3, 0x01, 0x0a,
	0x0d, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x4b, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x71, 0x75, 0x77, 0x61,
	0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x3f, 0x0a, 0x04, 0x6f, 0x62, 0x6a, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6f, 0x62,
	0x6a, 0x73, 0x22, 0x97, 0x02, 0x0a, 0x0d, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x12, 0x4f, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xce, 0x02, 0x0a,
	0x11, 0x4b, 0x75, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x6e, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x2e, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2f, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x64, 0x0a, 0x0b, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x12, 0x28, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x71, 0x75,
	0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x06, 0x46, 0x69, 0x6e, 0x64,
	0x4e, 0x53, 0x12, 0x2a, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x2e, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x2b,
	0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x47, 0x5a,
	0x45, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x71, 0x75, 0x77,
	0x61, 0x6e, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x63, 0x6f, 0x6e,
	0x73, 0x74, 0x61, 0x63, 0x6b, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x3b, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescOnce sync.Once
	file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescData = file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDesc
)

func file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescGZIP() []byte {
	file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescOnce.Do(func() {
		file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescData = protoimpl.X.CompressGZIP(file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescData)
	})
	return file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDescData
}

var file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_quwan_cloud_constack_v1alpha_kubeconfig_proto_goTypes = []any{
	(*GetConfigRequest)(nil),  // 0: quwan.cloud.constack.v1alpha.GetConfigRequest
	(*GetConfigResponse)(nil), // 1: quwan.cloud.constack.v1alpha.GetConfigResponse
	(*ClusterReq)(nil),        // 2: quwan.cloud.constack.v1alpha.ClusterReq
	(*ClusterResp)(nil),       // 3: quwan.cloud.constack.v1alpha.ClusterResp
	(*ClusterInfo)(nil),       // 4: quwan.cloud.constack.v1alpha.ClusterInfo
	(*NamespaceReq)(nil),      // 5: quwan.cloud.constack.v1alpha.namespaceReq
	(*NamespaceResp)(nil),     // 6: quwan.cloud.constack.v1alpha.namespaceResp
	(*NamespaceInfo)(nil),     // 7: quwan.cloud.constack.v1alpha.namespaceInfo
	nil,                       // 8: quwan.cloud.constack.v1alpha.namespaceInfo.LabelsEntry
}
var file_quwan_cloud_constack_v1alpha_kubeconfig_proto_depIdxs = []int32{
	4, // 0: quwan.cloud.constack.v1alpha.ClusterResp.objs:type_name -> quwan.cloud.constack.v1alpha.ClusterInfo
	4, // 1: quwan.cloud.constack.v1alpha.namespaceResp.clusterInfo:type_name -> quwan.cloud.constack.v1alpha.ClusterInfo
	7, // 2: quwan.cloud.constack.v1alpha.namespaceResp.objs:type_name -> quwan.cloud.constack.v1alpha.namespaceInfo
	8, // 3: quwan.cloud.constack.v1alpha.namespaceInfo.labels:type_name -> quwan.cloud.constack.v1alpha.namespaceInfo.LabelsEntry
	0, // 4: quwan.cloud.constack.v1alpha.KubeConfigService.GetConfig:input_type -> quwan.cloud.constack.v1alpha.GetConfigRequest
	2, // 5: quwan.cloud.constack.v1alpha.KubeConfigService.FindCluster:input_type -> quwan.cloud.constack.v1alpha.ClusterReq
	5, // 6: quwan.cloud.constack.v1alpha.KubeConfigService.FindNS:input_type -> quwan.cloud.constack.v1alpha.namespaceReq
	1, // 7: quwan.cloud.constack.v1alpha.KubeConfigService.GetConfig:output_type -> quwan.cloud.constack.v1alpha.GetConfigResponse
	3, // 8: quwan.cloud.constack.v1alpha.KubeConfigService.FindCluster:output_type -> quwan.cloud.constack.v1alpha.ClusterResp
	6, // 9: quwan.cloud.constack.v1alpha.KubeConfigService.FindNS:output_type -> quwan.cloud.constack.v1alpha.namespaceResp
	7, // [7:10] is the sub-list for method output_type
	4, // [4:7] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_quwan_cloud_constack_v1alpha_kubeconfig_proto_init() }
func file_quwan_cloud_constack_v1alpha_kubeconfig_proto_init() {
	if File_quwan_cloud_constack_v1alpha_kubeconfig_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GetConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ClusterReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ClusterResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*ClusterInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*NamespaceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*NamespaceResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*NamespaceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_quwan_cloud_constack_v1alpha_kubeconfig_proto_goTypes,
		DependencyIndexes: file_quwan_cloud_constack_v1alpha_kubeconfig_proto_depIdxs,
		MessageInfos:      file_quwan_cloud_constack_v1alpha_kubeconfig_proto_msgTypes,
	}.Build()
	File_quwan_cloud_constack_v1alpha_kubeconfig_proto = out.File
	file_quwan_cloud_constack_v1alpha_kubeconfig_proto_rawDesc = nil
	file_quwan_cloud_constack_v1alpha_kubeconfig_proto_goTypes = nil
	file_quwan_cloud_constack_v1alpha_kubeconfig_proto_depIdxs = nil
}
