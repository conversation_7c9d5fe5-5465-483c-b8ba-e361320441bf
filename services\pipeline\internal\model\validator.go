package model

import (
	"errors"
	"fmt"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/goccy/go-json"
	"k8s.io/apimachinery/pkg/util/validation"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/tools"
	checker "52tt.com/cicd/pkg/validator"
	"52tt.com/cicd/services/pipeline/internal/conf"
	pipelineErr "52tt.com/cicd/services/pipeline/pkg/error"
)

var (
	// ErrSubV2EmptySenv 子环境 2.0 必须指定环境标记名称 (senv)
	ErrSubV2EmptySenv = errors.New("子环境 2.0 必须指定环境标记名称 (senv)")
	// ErrSubV2IllegalSenvValue 子环境 2.0 的环境标记名称 (senv) 不正确, 一个生效的环境标记名称必须为空字符串或者由字母、数字、'-'、'_'、'.' 组成, 并且必须以字母或数字开头和结尾 (例如: 'MyValue', 或者 'my_value', 或者 '12345')
	ErrSubV2IllegalSenvValue = errors.New("子环境 2.0 的环境标记名称 (senv) 不正确, 一个生效的环境标记名称必须为空字符串或者由字母、数字、'-'、'_'、'.' 组成, 并且必须以字母或数字开头和结尾 (例如: 'MyValue', 或者 'my_value', 或者 '12345')")
)

type PullCode struct {
	AppId         int64    `json:"appId,omitempty"`
	RepoAddress   string   `json:"repoAddress,omitempty"`
	PullSubModule bool     `json:"pullSubModule"`
	Extends       []string `json:"extends,omitempty"`
}

type AutomationCompile struct {
	BuildPath    string `json:"buildPath"`
	TargetBranch string `json:"targetBranch"`
	BuildCommand string `json:"buildCommand" validate:"required"`
	ImageId      int64  `json:"imageId"`
	ImageAddress string `json:"imageAddress"`
	GpuBuild     bool   `json:"gpuBuild"`
}

type CustomShell struct {
	ExecShell    string `json:"execShell" validate:"required"`
	ImageId      int64  `json:"imageId"`
	ImageAddress string `json:"imageAddress"`
}

type CheckStyle struct {
	CheckStyleCommand string `json:"checkStyleCommand" validate:"required"`
	CheckStyleRule    string `json:"CheckStyleRule"`
}

type UnitTest struct {
	UnitTestCommand string `json:"unitTestCommand" validate:"required"`
	ImageId         int64  `json:"imageId"`
	ImageAddress    string `json:"imageAddress"`
}

type SCASCAN struct {
	ImageId      int64  `json:"imageId"`
	ImageAddress string `json:"imageAddress"`
}

type GeneratePushImage struct {
	CacheBaseImage           bool                    `json:"cacheBaseImage"`
	BuildWay                 constants.BuildImageWay `json:"buildWay" validate:"required,oneof=GET_DOCKER_FILE_FROM_REPO CUSTOM_DOCKER_FILE"`
	FilePath                 string                  `json:"filePath" validate:"required_if=BuildWay GET_DOCKER_FILE_FROM_REPO,max=200"`
	BuildParams              []map[string]string     `json:"buildParams"`
	DockerFile               string                  `json:"dockerFile" validate:"required_if=BuildWay CUSTOM_DOCKER_FILE"`
	PushOverseasRegistryFlag bool                    `json:"pushOverseasRegistryFlag"`
	EnableOCI                *bool                   `json:"enableOCI"`
}

type MultiEnv struct {
	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
	ConfigId  int64  `json:"configId"`
	Senv      string `json:"senv"`
	SubEnvTag string `json:"subEnvTag"`
	// IsOrigin 是否基准命名空间
	IsOrigin bool `json:"isOrigin"`
}

// AutomationDeploy 执行部署需要的信息
type AutomationDeploy struct {
	TriggerMode string `json:"triggerMode" validate:"required,oneof=manual auto"`
	DeployEnv   string `json:"deployEnv"`
	// 基准 / 子环境 / 子环境 2.0 环境标记, 对应 constants.EnvTargetType, 应该使用该类型会更好
	EnvTarget         string     `json:"envTarget"`
	IsCreateSubEnv    bool       `json:"isCreateSubEnv"`
	ConfigMap         []string   `json:"configMap"`
	Secret            []string   `json:"secret"`
	ServiceAccount    []string   `json:"serviceAccount"`
	Cluster           string     `json:"cluster"`
	Namespace         string     `json:"namespace"`
	SubNamespace      string     `json:"subNamespace"`
	TrafficMark       string     `json:"trafficMark"`
	ConfigId          int64      `json:"configId"`
	ApprovalTaskRunId int64      `json:"approvalTaskRunId"`
	MultiEnv          []MultiEnv `json:"multiEnv"`
	// 子环境 2.0 的环境名, 详细可查阅 https://q9jvw0u5f5.feishu.cn/wiki/K3Pvwqiv7i82SQk4WtScIlaCnCd
	Senv             string                    `json:"senv"`
	IsPass           string                    `json:"isPass"`
	IsUseSubEnvGroup bool                      `json:"isUseSubEnvGroup"`
	SubEnvGroupType  constants.EnumSubEnvGroup `json:"subEnvGroupType" validate:"required_if=IsUseSubEnvGroup true,oneof=0 1 2"` // 子环境分组，0:无分组/1:联调自测/2:测试验收
	ModifySubEnv     string                    `json:"modifySubEnv"`
	TaskTimeout
	TaskRunCanaryConfig
}

type EnvImageSync struct {
	Cluster   string `json:"cluster" validate:"required"`
	Namespace string `json:"namespace" validate:"required"`
	ConfigId  int64  `json:"configId"` // 任务重试的场景下，这个值不可用。需要根据Cluster和Namespace去查询最新的值。
	TaskTimeout
}

type SonarScan struct {
	ExcludeRule         string              `json:"excludeRule"`
	TestFileIncludeRule string              `json:"testFileIncludeRule"`
	ScanParam           string              `json:"scanParam"`
	TerminatePipeline   bool                `json:"terminatePipeline"`
	ScanThreshold       []ScanThresholdItem `json:"scanThreshold" validate:"required_if=TerminatePipeline true,dive"`
}

type Checkstyle struct {
	CheckStyleCommand string `json:"checkStyleCommand" validate:"required"`
	CheckStyleRule    string `json:"checkStyleRule"`
}

type TestAcceptance struct {
	UploadTestReport bool `json:"uploadTestReport"`
}

type Approval struct {
	RelatedApprovalFlowId      int64 `json:"relatedApprovalFlowId" validate:"required"`
	IsConditionApprovalEnabled bool  `json:"isConditionApprovalEnabled"`
	OriginTestApprovalFlowId   int64 `json:"originTestApprovalFlowId"`
}

type ScanThresholdItem struct {
	Name  constants.SonarScanThresholdMetric `json:"name" validate:"required,oneof=BUG VULNERABILITY BAD_SMELL TEST_COVERAGE"`
	Value *float64                           `json:"value" validate:"gte=0"`
}

type ApiAutomationTest struct {
	ProjectId string `json:"projectId" validate:"required"`
	PlanId    string `json:"planId" validate:"required"`
}

type DeployStaging struct {
	TriggerMode string     `json:"triggerMode" validate:"required,oneof=manual auto"`
	EnvTarget   string     `json:"envTarget"`
	MultiEnv    []MultiEnv `json:"multiEnv"`
	TaskTimeout
}

type DeployCanary struct {
	TriggerMode string     `json:"triggerMode" validate:"required,oneof=manual auto"`
	EnvTarget   string     `json:"envTarget"`
	MultiEnv    []MultiEnv `json:"multiEnv"`
	TaskTimeout
}

type TaskTimeout struct {
	Timeout int64 `json:"timeout"` // 部署超时时长，10 <= timeout <= 60
}

type OfflineCanary struct {
}

type PauseTask struct{}

func (s ScanThresholdItem) Int64() int64 {
	if s.Value == nil {
		return 0
	}

	return int64(*s.Value)
}

func (s ScanThresholdItem) Float64() float64 {
	if s.Value == nil {
		return 0
	}

	return *s.Value
}

func (s ScanThresholdItem) ValueFormated() string {
	if s.Name == constants.TEST_COVERAGE {
		return fmt.Sprintf("%.2f", s.Float64())
	} else {
		// return int string
		return fmt.Sprintf("%.0f", s.Float64())
	}
}

type TemplateFieldError struct {
	FieldName string
	Tag       string
	Param     string
}

func TemplateValidate(rules conf.PipelineRules, template Template) []TemplateFieldError {
	var availableStages []string
	var currentStageRule conf.Stage
	var templateFieldErrors []TemplateFieldError
	for _, stage := range template.Stages {
		currentStageRule = tools.Get(rules.Stages, func(stageConfig conf.Stage) bool {
			return stageConfig.Name == stage.Type
		})
		if currentStageRule.Name == "" {
			templateFieldErrors = append(templateFieldErrors, TemplateFieldError{
				FieldName: stage.Name,
				Tag:       "stage-illegal",
			})
		} else {
			stageValidationErrs := StageValidate(availableStages, stage)
			if stageValidationErrs != nil {
				templateFieldErrors = append(templateFieldErrors, *stageValidationErrs)
			}

			templateFieldErrors = append(templateFieldErrors, TaskValidate(currentStageRule.Tasks, stage, template)...)
			fieldErrs := TasksConfigValidate(template.Type, stage.Tasks)
			templateFieldErrors = append(templateFieldErrors, fieldErrs...)
		}
		availableStages = currentStageRule.AvailableStages
	}
	return templateFieldErrors
}

func TasksConfigValidate(templateType constants.TemplateType, tasks []Task) []TemplateFieldError {
	var templateFieldErrors []TemplateFieldError
	for _, task := range tasks {

		if isTaskSkipValidation(constants.Task(task.Type), templateType) {
			break
		}
		configValue, err := transferTask(task)
		if err != nil {
			templateFieldErrors = append(templateFieldErrors, TemplateFieldError{FieldName: task.Name, Tag: "config-format-err"})
			break
		}
		if configValue == nil {
			templateFieldErrors = append(templateFieldErrors, TemplateFieldError{FieldName: task.Name, Tag: "illegal-task-err"})
			break
		}
		if configValue != nil {
			configValueErrors := ConfigValueValidate(configValue)
			if configValueErrors != nil {
				templateFieldErrors = append(templateFieldErrors, TemplateFieldError{FieldName: task.Name, Tag: "config-value-err", Param: configValueErrors.Error()})
			}
		}
		if constants.Task(task.Type).IsDeployTask() {
			err = checkAutomationDeployConfig(configValue)
			if err != nil {
				templateFieldErrors = append(templateFieldErrors, TemplateFieldError{FieldName: task.Name, Tag: "config-value-err", Param: err.Error()})
			}
		}
	}
	return templateFieldErrors
}

func TemplateValidator(rules conf.PipelineRules) validator.StructLevelFunc {
	return func(sl validator.StructLevel) {
		template := sl.Current().Interface().(Template)
		validErrors := TemplateValidate(rules, template)
		for _, fieldError := range validErrors {
			sl.ReportError(fieldError.FieldName, fieldError.FieldName, fieldError.FieldName, fieldError.Tag, fieldError.Param)
		}
	}
}

func StageValidate(availableStages []string, stage Stage) *TemplateFieldError {
	isFirstStage := len(availableStages) == 0
	if isFirstStage {
		if !strings.EqualFold(constants.STAGE_COMPILE_BUILD.String(), stage.Type) {
			return &TemplateFieldError{FieldName: stage.Name, Tag: "first-stage-err"}
		}
	} else {
		isLegalStage := tools.Any(availableStages, func(availableStage string) bool {
			return strings.EqualFold(availableStage, stage.Type)
		})
		if !isLegalStage {
			return &TemplateFieldError{FieldName: stage.Name, Tag: "stage-order-err"}
		}
	}
	if stage.Type == constants.STAGE_DEPLOY_PROD_ENV.String() {
		return DeployProdEnvValidate(stage)
	}
	return nil
}

func DeployProdEnvValidate(stage Stage) *TemplateFieldError {
	deployStagingTaskCount := 0
	deployCanaryTaskCount := 0
	OfflineCanaryTaskCount := 0
	for _, task := range stage.Tasks {
		if task.Type == constants.TASK_DEPLOY_STAGING.String() {
			deployStagingTaskCount++
		}
		if task.Type == constants.TASK_DEPLOY_CANARY.String() {
			deployCanaryTaskCount++
		}
		if task.Type == constants.TASK_OFFLINE_CANARY.String() {
			OfflineCanaryTaskCount++
		}
	}
	if deployStagingTaskCount > 1 || deployCanaryTaskCount > 1 || OfflineCanaryTaskCount > 1 {
		return &TemplateFieldError{FieldName: stage.Name, Tag: "canary-task-count-err"}
	}
	if OfflineCanaryTaskCount == 1 && deployCanaryTaskCount == 0 {
		return &TemplateFieldError{FieldName: stage.Name, Tag: "lack-deploy-canary-task-err"}
	}
	if OfflineCanaryTaskCount == 1 && stage.Tasks[len(stage.Tasks)-1].Type != constants.TASK_OFFLINE_CANARY.String() {
		return &TemplateFieldError{FieldName: stage.Name, Tag: "offline-canary-task-must-end-err"}
	}
	return nil
}

func TaskValidate(taskRules []conf.Task, stage Stage, template Template) []TemplateFieldError {
	var errs []TemplateFieldError
	for _, taskRule := range taskRules {
		tasks := tools.Filter(stage.Tasks, func(task Task) bool {
			return strings.EqualFold(taskRule.Name, task.Type)
		})
		if taskRule.Fixed {
			if len(tasks) < 1 {
				errs = append(errs, TemplateFieldError{FieldName: stage.Name, Tag: "lack-task-err"})
			}
		}
		if taskRule.Max > 0 && len(tasks) > taskRule.Max {
			errs = append(errs, TemplateFieldError{FieldName: stage.Name, Tag: "task-oversize-err"})
		}
	}
	if isSonarScanTaskOrderError(stage) {
		errs = append(errs, TemplateFieldError{FieldName: stage.Name, Tag: "sonar-scan-task-order-err"})
	}
	stageType := constants.Stage(stage.Type)
	if stageType == constants.STAGE_DEPLOY_TEST_ENV ||
		stageType == constants.STAGE_DEPLOY_PROD_ENV || stageType == constants.STAGE_DEPLOY_GRAY_ENV {
		//if len(stage.Tasks) == 2 && stage.Tasks[0].Type == string(constants.TASK_AUTOMATION_DEPLOY) && stage.Tasks[1].IsApprovalTask() {
		//	errors = append(errors, TemplateFieldError{FieldName: stage.Name, Tag: "deploy-task-order-err"})
		//}
		approvalIndex := -1
		deployIndex := -1
		for index, task := range stage.Tasks {
			if constants.Task(task.Type).IsBelongTo(
				constants.TASK_AUTOMATION_DEPLOY,
				constants.TASK_DEPLOY_ORIGIN,
				constants.TASK_DEPLOY_SUB,
			) {
				deployIndex = index
			} else if task.IsApprovalTask() {
				approvalIndex = index
			}
		}
		if approvalIndex > deployIndex {
			errs = append(errs, TemplateFieldError{FieldName: stage.Name, Tag: "deploy-task-order-err"})
		}
	}
	if stageType == constants.STAGE_SECURITY_SCAN {
		for _, task := range stage.Tasks {
			if constants.Task(task.Type) == constants.TASK_SCA_SCAN && template.Language != constants.JAVA.String() {
				errs = append(errs, TemplateFieldError{FieldName: stage.Name, Tag: "security-scan-task-language-err"})
			}
		}
	}
	return errs
}

func transferTask(task Task) (interface{}, error) {
	configJson, parseConfigErr := json.Marshal(task.Config)
	if parseConfigErr != nil {
		return nil, parseConfigErr
	}
	var data interface{}
	switch constants.Task(task.Type) {
	case constants.TASK_PULL_CODE:
		data = new(PullCode)
		break
	case constants.TASK_AUTOMATION_COMPILE:
		data = new(AutomationCompile)
		break
	case constants.TASK_CUSTOM_SHELL:
		data = new(CustomShell)
		break
	case constants.TASK_UNIT_TEST:
		data = new(UnitTest)
		break
	case constants.TASK_GENERATE_PUSH_IMAGE:
		data = new(GeneratePushImage)
		break
	case constants.TASK_AUTOMATION_DEPLOY, constants.TASK_AUTOMATION_DEPLOY_SENV:
		data = new(AutomationDeploy)
		break
	case constants.TASK_DEPLOY_ORIGIN, constants.TASK_DEPLOY_SUB:
		// todo 待确认
		data = new(AutomationDeploy)
	case constants.TASK_DEPLOY_STAGING:
		data = new(DeployStaging)
		break
	case constants.TASK_DEPLOY_CANARY:
		data = new(DeployCanary)
		break
	case constants.TASK_OFFLINE_CANARY:
		data = new(OfflineCanary)
		break
	case constants.TASK_SONAR_SCAN:
		data = new(SonarScan)
		break
	case constants.TASK_CHECKSTYLE:
		data = new(Checkstyle)
		break
	case constants.TASK_SCA_SCAN:
		data = new(SCASCAN)
		break
	case constants.TASK_TEST_ACCEPTANCE:
		data = new(TestAcceptance)
		break
	case constants.TASK_API_AUTOMATION_TEST:
		data = new(ApiAutomationTest)
		break
	case constants.TASK_UPGRADE_APPROVAL, constants.TASK_GRAY_UPGRADE_APPROVAL, constants.TASK_TEST_APPROVAL:
		data = new(Approval)
		break
	case constants.TASK_TEST_ENV_IMAGE_SYNC, constants.TASK_DEV_ENV_IMAGE_SYNC:
		data = new(EnvImageSync)
		break
	case constants.TASK_PAUSE:
		data = new(PauseTask)
		break
	default:
		log.Errorf("task type %s is not legal", task.Type)
		break

	}
	if data == nil {
		return nil, errors.New("task type is not legal")
	}
	err := json.Unmarshal(configJson, &data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func ConfigValueValidate(configValue interface{}) error {
	return checker.Check(configValue)
}

func PipelineConfigValidate(templateType constants.TemplateType, tasks []Task) error {
	var errorInfo []string

	fieldErrs := TasksConfigValidate(templateType, tasks)
	for _, err := range fieldErrs {
		errorInfo = append(errorInfo, fmt.Sprintf("任务%s配置校验错误,%v", err.FieldName, err.Param))
	}
	message := strings.Join(errorInfo, ";")
	if message != "" {
		return &pipelineErr.PipelineValidateErr{Message: message}
	}
	return nil
}

func RegisterTemplateTranslator() {
	checker.RegisterCustomTranslator("stage-illegal", "阶段类型不合法")
	checker.RegisterCustomTranslator("first-stage-err", "第一个阶段须为编译构建阶段")
	checker.RegisterCustomTranslator("stage-order-err", "阶段不连续")
	checker.RegisterCustomTranslator("lack-task-err", "缺少必须的任务")
	checker.RegisterCustomTranslator("task-oversize-err", "超过该类型任务的最大上限")
	checker.RegisterCustomTranslator("config-format-err", "任务参数配置格式有误")
	checker.RegisterCustomTranslator("illegal-task-err", "任务类型不合法")
	checker.RegisterCustomTranslator("config-value-err", "任务配置内容有误")
	checker.RegisterCustomTranslator("sonar-scan-task-order-err", "代码风格扫描任务不可在Sonar扫描任务之后")
	checker.RegisterCustomTranslator("deploy-task-order-err", "审批任务不可在部署任务之后")
	checker.RegisterCustomTranslator("security-scan-task-language-err", "安全扫描当前仅支持java语言")
	checker.RegisterCustomTranslator("canary-task-count-err", "当前阶段部署金丝雀相关任务数量不正确")
	checker.RegisterCustomTranslator("lack-deploy-canary-task-err", "当前阶段缺少部署金丝雀任务")
	checker.RegisterCustomTranslator("offline-canary-task-must-end-err", "下线子环境任务必须是最后一个任务")
}

func checkAutomationDeployConfig(configValue interface{}) error {
	if configValue, ok := configValue.(*AutomationDeploy); ok {
		envTarget := constants.EnvTargetType(configValue.EnvTarget)
		if constants.EnvType(configValue.DeployEnv) != constants.TESTING && constants.EnvType(configValue.DeployEnv) != constants.DEV &&
			constants.EnvType(configValue.DeployEnv) != constants.PREVIEW && constants.EnvType(configValue.DeployEnv) != constants.PRODUCTION {
			return errors.New("发布环境类型错误")
		}
		//if constants.EnvType(configValue.DeployEnv) == constants.PRODUCTION && constants.EnvTargetType(configValue.EnvTarget) != constants.ORIGIN {
		//	return errors.New("生产环境的环境类型只能为基准环境")
		//}
		if !envTarget.IsValid() {
			return errors.New("请输入正确的环境类型")
		}
		if len(configValue.MultiEnv) == 0 && (configValue.Cluster == "" || configValue.Namespace == "") {
			return errors.New("集群和命名空间不能为空")
		}
		if constants.EnvTargetType(configValue.EnvTarget) == constants.SUB && configValue.IsCreateSubEnv == false && configValue.TrafficMark == "" {
			return errors.New("子环境的流量标记不能为空")
		}
		if len(configValue.MultiEnv) > 0 {
			for _, env := range configValue.MultiEnv {
				if env.Cluster == "" || env.Namespace == "" {
					return errors.New("子任务的集群或者命名空间不能为空")
				}
			}
		}
		// 子环境 2.0 校验规则
		// 1. 如果是子环境 2.0 && 创建子环境 则 senv 和命名空间不能为空
		if envTarget == constants.SUB_V2 {
			if len(configValue.MultiEnv) == 0 && strings.TrimSpace(configValue.Senv) == "" {
				return ErrSubV2EmptySenv
			}
			senvLabels := make([]string, 0, len(configValue.MultiEnv)+1)
			if len(configValue.MultiEnv) > 0 {
				for _, env := range configValue.MultiEnv {
					senvLabels = append(senvLabels, env.Senv)
				}
			}
			if strings.TrimSpace(configValue.Senv) != "" {
				senvLabels = append(senvLabels, configValue.Senv)
			}
			for _, v := range senvLabels {
				// 2. 标识字段 senv 需要符合 K8s 的 label 命名规范 - 直接使用 K8s 的 sdk 对 value 值进行校验
				if errs := validation.IsValidLabelValue(v); len(errs) > 0 {
					// 该错误可参考 k8s.io/apimachinery/pkg/util/validation/validation.go:157
					return ErrSubV2IllegalSenvValue
				}
			}
		}
	}
	return nil
}

func isTaskSkipValidation(taskType constants.Task, templateType constants.TemplateType) bool {
	return templateType == constants.SYSTEM && (taskType.IsDeployTask() || taskType.IsImageSyncTask() || taskType.IsApprovalTask())
}

func isSonarScanTaskOrderError(stage Stage) bool {
	return constants.Stage(stage.Type) == constants.STAGE_STATIC_CODE_SCAN && len(stage.Tasks) == 2 &&
		constants.Task(stage.Tasks[0].Type) == constants.TASK_SONAR_SCAN && constants.Task(stage.Tasks[1].Type) == constants.TASK_CHECKSTYLE
}
