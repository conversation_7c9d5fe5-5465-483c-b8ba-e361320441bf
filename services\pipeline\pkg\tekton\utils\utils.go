package utils

import (
	"fmt"
	"strings"

	"52tt.com/cicd/services/pipeline/internal/dao"
)

func StringPointer(s string) *string {
	return &s
}

func ApplyScriptParams(script string, run dao.PipelineRun) string {
	scriptParams := map[string]string{
		"$buildPath":     run.Pipeline.BuildPath,
		"$repoAddr":      run.RepoAddress,
		"$cacheDir":      fmt.Sprintf("/cache/%s/", run.Pipeline.AppName),
		"$serviceName":   run.Pipeline.AppName,
		"$currentBranch": run.Branch,
		"$sourceBranch":  run.SourceBranch,
		"$committedID":   run.CommittedID,
	}
	for param, value := range scriptParams {
		script = strings.Replace(script, param, value, -1)
	}
	return script
}

func Replace(script string, key, value string) (newscript string) {
	newscript = strings.Replace(script, key, value, -1)
	return
}
