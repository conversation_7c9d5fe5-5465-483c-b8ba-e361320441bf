package rest

import (
	"context"
	"reflect"

	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/mcp/internal/service/upd_ser"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mitchellh/mapstructure"
)

func (ctl *ttCloudMCPController) ToolsScaleWorkload() mcp.Tool {
	argsType := reflect.TypeOf(upd_ser.ScalePodReplicasArgs{})
	argsSchema := jsonSchemaReflector.ReflectFromType(argsType)
	raw, _ := argsSchema.MarshalJSON()

	return mcp.NewToolWithRawSchema("scale_app_replicas", DescpOfScaleWorkload, raw)
}

func (ctl *ttCloudMCPController) ScaleWorkload(ctx context.Context, request mcp.CallToolRequest) (res *mcp.CallToolResult, errOut error) {
	var req upd_ser.ScalePodReplicasArgs
	err := mapstructure.Decode(request.Params.Arguments, &req)
	if err != nil {
		res = mcp.NewToolResultError("upd_ser.ScalePodReplicasArgs Decode Err")
		return
	}

	err = upd_ser.UpdateAgg.ScalePod(ctx, req)
	if err != nil {
		log.Errorf("ttCloudMCPController ScaleWorkload err: %v", err)
		res = mcp.NewToolResultError(err.Error())
		return
	}
	res = mcp.NewToolResultText("Success")

	return
}

const (
	DescpOfScaleWorkload = `
功能：
    变更(只能扩容,不可缩容)应用(或服务)在k8s中的部署资源的副本数;
    部署资源有两种类型:Deployment或StatefulSet;
    应用(或服务)的名称即为对应Deployment或StatefulSet的名称;
	只能扩容，不能缩容。
输入参数
    集群名称（必须）：目标集群标识
    命名空间（必须）：指定Namespace
    工作负载名称（必须）：需精确匹配Deployment/StatefulSet名称
    工作负载类型（必须）：Deployment 或 StatefulSet
    副本数量(必须)：目标副本数，整数且≥0（StatefulSet建议≥1）
核心特性
    单次仅处理一个工作负载，
    自动触发滚动更新（Deployment）或顺序扩缩容（StatefulSet）
典型场景
    应对流量高峰（快速扩容）
    成本优化（低峰期缩容）
    维护窗口（缩容至0后升级）
	`
)
