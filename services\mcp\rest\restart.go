package rest

import (
	"context"

	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/mcp/internal/service/restart"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mitchellh/mapstructure"
)

func (ctl *ttCloudMCPController) ToolsRestart() mcp.Tool {
	return mcp.NewTool("restart_app",
		mcp.WithDescription(DescpOfRestartWorkload),
		mcp.WithString("clusterName", mcp.Required(), mcp.Description("集群名称")),
		mcp.WithString("namespace", mcp.Required(), mcp.Description("命名空间(NS)名称")),
		mcp.WithString("name", mcp.Required(), mcp.Description("要重启的工作负载名称")),
		mcp.WithString("kind", mcp.Required(), mcp.Description("工作负载的类型,Deployment或StatefulSet"), mcp.Enum("Deployment", "StatefulSet")),
	)
}

func (ctl *ttCloudMCPController) RestartWorkload(ctx context.Context, request mcp.CallToolRequest) (res *mcp.CallToolResult, errOut error) {
	var req restart.RestartWorkloadArgs
	err := mapstructure.Decode(request.Params.Arguments, &req)
	if err != nil {
		res = mcp.NewToolResultError("name must be a string")
		return
	}

	err = restart.RestartAgg.Restart(&req)
	if err != nil {
		log.Errorf("ttCloudMCPController RestartWorkload err: %v", err)
		res = mcp.NewToolResultError(err.Error())
		return
	}

	res = mcp.NewToolResultText("Success")
	// res = mcp.NewToolResultError("专门的错误")
	return
}

const (
	DescpOfRestartWorkload = `
功能：
    安全重启重启应用（或服务）在k8s中的部署资源(只有Deployment/StatefulSet两种）；
    应用（或服务）的名称即为对应deployment和service的名称；
输入参数
    集群名称（必须）：目标集群标识
    命名空间（必须）：指定Namespace
    工作负载名称（必须）：需精确匹配Deployment/StatefulSet名称
    工作负载类型（必须）：Deployment 或 StatefulSet
核心特性
    滚动重启：通过kubectl rollout restart实现零停机更新，StatefulSet按Pod序号顺序重启（保障有状态服务稳定性）
    服务影响：Deployment：短暂流量中断（取决于副本数和滚动策略），StatefulSet：按序重启可能导致服务降级
典型场景
    配置更新后触发重建（ConfigMap/Secret变更）
    强制恢复异常Pod（如CrashLoopBackOff状态）
	`
)
