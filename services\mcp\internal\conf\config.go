package conf

import (
	"os"

	"52tt.com/cicd/pkg/config"
)

var MCPSerConfig *Config

type MCP struct {
	BaseURL string `mapstructure:"baseURL" json:"baseURL"`
}

type Cloud struct {
	Host             string `json:"host"`
	Token            string `json:"token"`
	GrpcTarget       string `mapstructure:"grpc_target" json:"grpc_target"`
	DeployGrpcTarget string `mapstructure:"deploy_grpc_target" json:"deploy_grpc_target"`
}

type Config struct {
	config.Default
	MCP   MCP   `mapstructure:"mcp" json:"mcp"`
	Cloud Cloud `mapstructure:"cloud"`
}

func LoadConfig(path string) (*Config, error) {
	_, err := os.Stat(path)
	if err != nil {
		if os.IsNotExist(err) {
			path = "../conf/"
		}
	}
	var c Config
	if err := config.NewConfig(path, &c); err != nil {
		return nil, err
	}

	MCPSerConfig = &c

	return &c, nil
}
