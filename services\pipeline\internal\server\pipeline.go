package server

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/log"
	pbapp "52tt.com/cicd/protocol/app"
	pbpipeline "52tt.com/cicd/protocol/pipeline"
	"52tt.com/cicd/services/pipeline/internal/dao"
	"52tt.com/cicd/services/pipeline/internal/model"
	"52tt.com/cicd/services/pipeline/internal/service"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/structpb"
	"gorm.io/gorm"
)

var _ pbpipeline.PipelineServiceServer = (*PipelineServer)(nil)

func NewPipelineServer(pRepo dao.PipelineRepository, tmplRepo dao.TemplateRepository,
	appCli pbapp.AppServiceClient, pipeService service.PipelineService,
	pipelineRunRepo dao.PipelineRunRepository, tmplService service.TemplateService) *PipelineServer {
	return &PipelineServer{
		pipelineRepo:    pRepo,
		tmplRepo:        tmplRepo,
		appClient:       appCli,
		pipelineService: pipeService,
		pipelineRunRepo: pipelineRunRepo,
		tmplService:     tmplService,
	}
}

type PipelineServer struct {
	pbpipeline.UnimplementedPipelineServiceServer
	appClient       pbapp.AppServiceClient
	tmplRepo        dao.TemplateRepository
	pipelineRepo    dao.PipelineRepository
	pipelineService service.PipelineService
	pipelineRunRepo dao.PipelineRunRepository
	tmplService     service.TemplateService
}

func (ps *PipelineServer) NewPipeline(ctx context.Context, pipeline *pbpipeline.Pipeline) (*pbpipeline.PipelineResult, error) {
	//TODO implement me
	panic("implement me")
}

func (ps *PipelineServer) UpdatePipelineAppMsg(ctx context.Context, pipeline *pbpipeline.PipelineAppReq) (*pbpipeline.PipelineAppResp, error) {
	p := &dao.Pipeline{
		AppID:     pipeline.AppId,
		AppName:   pipeline.AppName,
		BuildPath: pipeline.BuildPath,
		RepoAddr:  pipeline.RepoAddr,
		Language:  fmt.Sprintf("%s(%s)", pipeline.Language, pipeline.LanguageVersion),
	}
	err := ps.pipelineRepo.UpdatePipelineAppMsgByAppId(ctx, p)
	if err != nil {
		return nil, err
	}
	resp := &pbpipeline.PipelineAppResp{}
	return resp, nil
}

func (ps *PipelineServer) GetPipelineConfig(ctx context.Context, req *pbpipeline.GetPipelineConfigReq) (*pbpipeline.GetPipelineConfigResp, error) {
	p, err := ps.pipelineRepo.GetPipeline(ctx, req.GetId())
	if err != nil {
		log.Errorf("获取流水线[%d]数据发生异常: %v", req.GetId(), err)
		return nil, err
	}
	if p == nil {
		msg := fmt.Sprintf("获取流水线[%d]数据为空，无法展示详情", req.GetId())
		log.Errorf(msg)
		// todo errors.New support http code and grpc code
		return nil, errors.New(msg)
	}

	tmpl, err := ps.tmplRepo.GetTemplateById(ctx, p.TemplateID)
	if err != nil {
		log.Errorf("获取流水线[%d]模板[%d]数据发生异常: %v", req.GetId(), p.TemplateID, err)
		return nil, err
	}
	if tmpl == nil {
		msg := fmt.Sprintf("获取流水线[%d]模板[%d]数据为空，无法展示详情", req.GetId(), p.TemplateID)
		log.Errorf(msg)
		return nil, errors.New(msg)
	}

	pipelineCfg := make(model.PipelineConfig)
	err = json.Unmarshal(p.Config, &pipelineCfg)
	if err != nil {
		log.Errorf("流水线[%s(%d)]合并用户配置发生异常: %v", p.Name, p.ID, err)
		return nil, err
	}

	tmplPipelineCfg := service.TemplateToPipelineConfig(ctx, tmpl)
	fullCfg, err := service.MergePipelineConfig(ctx, tmplPipelineCfg, pipelineCfg)
	if err != nil {
		log.Errorf("流水线[%s(%d)]合并用户配置发生异常: %v", p.Name, p.ID, err)
		return nil, err
	}

	// 有需要再优化
	stagesPoint := make(map[int64]int)
	for i, id := range tmpl.GetSequence() {
		stagesPoint[id] = i
	}
	stages := make([]*pbpipeline.GetPipelineConfigResp_Stage, len(tmpl.Stages))
	for _, stage := range tmpl.Stages {
		tasksPoint := make(map[int64]int)
		for i, id := range stage.GetSequence() {
			tasksPoint[id] = i
		}

		tasks := make([]*pbpipeline.GetPipelineConfigResp_Task, len(stage.Tasks))
		for _, task := range stage.Tasks {
			tCfg := fullCfg[task.ID]
			cfg, _ := json.Marshal(tCfg)
			//兼容部署生产环境多云部署
			if task.Type == string(constants.TASK_AUTOMATION_DEPLOY) {
				cfg, err = ps.tmplService.CompatibleProductionDeploy(ctx, cfg)
				if err != nil {
					return nil, err
				}
			}
			pbCfg := &structpb.Struct{}
			err = protojson.Unmarshal(cfg, pbCfg)
			if err != nil {
				log.Errorf("流水线[%s(%d)]转换用户配置为pb发生异常: %v", p.Name, p.ID, err)
				return nil, err
			}

			// 先实现后优化
			if task.Type == string(constants.TASK_PULL_CODE) || task.Type == string(constants.TASK_AUTOMATION_COMPILE) {
				app, err := ps.appClient.GetApp(context.Background(), &pbapp.AppParam{Id: p.AppID})
				if err != nil {
					log.Errorf("流水线[%s(%d)]合并用户配置前置，获取app[%d]信息发生异常: %v", p.Name, p.ID, p.AppID, err)
					return nil, fmt.Errorf("流水线[%s(%d)]阶段配置，获取app[%d]信息发生异常", p.Name, p.ID, p.AppID)
				}

				if task.Type == string(constants.TASK_PULL_CODE) {
					pbCfg.Fields["appName"] = structpb.NewStringValue(app.Name)
					pbCfg.Fields["appRepoAddr"] = structpb.NewStringValue(app.RepoAddr)
					pbCfg.Fields["appId"] = structpb.NewNumberValue(float64(app.Id))
				} else {
					pbCfg.Fields["targetBranch"] = structpb.NewStringValue(p.TargetBranchContent)
					pbCfg.Fields["buildPath"] = structpb.NewStringValue(app.BuildPath)
				}
			}

			taskIdx := tasksPoint[task.ID]
			tasks[taskIdx] = &pbpipeline.GetPipelineConfigResp_Task{
				Id:     task.ID,
				Name:   task.Name,
				Config: pbCfg,
				Type:   task.Type,
			}
		}

		stageIdx := stagesPoint[stage.ID]
		stages[stageIdx] = &pbpipeline.GetPipelineConfigResp_Stage{
			Id:    stage.ID,
			Name:  stage.Name,
			Tasks: tasks,
			Type:  stage.Type,
		}
	}

	resp := &pbpipeline.GetPipelineConfigResp{
		Id:     p.ID,
		Stages: stages,
	}
	return resp, nil
}

func (ps *PipelineServer) GetPipelineByAppId(ctx context.Context, pipeline *pbpipeline.Pipeline) (*pbpipeline.PipelineArray, error) {
	pipelines, err := ps.pipelineService.GetPipelineByAppId(ctx, pipeline.AppId)
	if err != nil {
		log.Errorf("根据appId[%d]查询流水线失败: %v", pipeline.Id, err)
		return nil, err
	}
	var res []*pbpipeline.PipelineResult
	for _, pipe := range pipelines {
		pipelineResult := &pbpipeline.PipelineResult{
			Id:         pipe.ID,
			TemplateId: pipe.TemplateId,
			Name:       pipe.Name,
			Type:       pipe.Type,
		}
		res = append(res, pipelineResult)
	}
	return &pbpipeline.PipelineArray{Pipelines: res}, nil
}

func (ps *PipelineServer) GetTaskById(ctx context.Context, task *pbpipeline.Task) (*pbpipeline.Task, error) {
	taskInfo, err := ps.pipelineRepo.GetTaskById(ctx, task.Id)
	if err != nil {
		log.Errorf("根据ID[%d]查询任务信息失败: %v", task.Id, err)
		return nil, err
	}
	if taskInfo == nil {
		return nil, fmt.Errorf("ID为[%d]的任务不存在", task.Id)
	}
	return &pbpipeline.Task{Id: taskInfo.ID, Config: taskInfo.Config}, nil
}
func (ps *PipelineServer) UpdatePipelineTaskConfig(ctx context.Context, updateReq *pbpipeline.UpdatePipelineTaskConfigReq) (*emptypb.Empty, error) {
	updateConfigParams := &model.UpdatePipelineConfigParams{TaskId: updateReq.TaskId, PipelineRunId: updateReq.PipelineRunId,
		TaskRunId: updateReq.TaskRunId, UpdatedPipelineConfig: updateReq.UpdatedPipelineConfig,
		UpdatedTaskRunConfig: updateReq.UpdatedTaskRunConfig}
	err := ps.pipelineService.UpdatePipelineConfig(ctx, updateConfigParams)
	if err != nil {
		log.Errorf("更新taskRun[%d],PipelineRun[%d]以及流水线配置发生异常: %v", updateReq.TaskRunId, updateReq.PipelineRunId, err)
		return new(emptypb.Empty), err
	}
	return new(emptypb.Empty), nil
}

func (ps *PipelineServer) TxUpdatePipelineRunTaskConfigById(ctx context.Context, id int64, config model.TaskRunMultiCloudEnvConfig) error {
	// find task run by id
	taskRun, err := ps.pipelineRunRepo.FindTaskRunById(ctx, id)
	if err != nil {
		log.Errorf("[UpdatePipelineRunTaskConfigById]根据ID[%d]查询任务信息失败: %v", id, err)
		return err
	}
	if taskRun.ID == 0 {
		return fmt.Errorf("[UpdatePipelineRunTaskConfigById]ID为[%d]的任务不存在", id)
	}
	// update task run config
	var taskRunConfig model.TaskRunMultiCloudEnvConfig
	if err := json.Unmarshal(taskRun.Config, &taskRunConfig); err != nil {
		log.Errorf("[UpdatePipelineRunTaskConfigById]序列化配置信息错误, taskRunId %d: %v", id, err)
		return fmt.Errorf("工单更新流水线生产多云部署配置，序列化解析出错：%v", err)
	}
	taskRunConfig.Cluster = config.Cluster
	taskRunConfig.Namespace = config.Namespace
	taskRunConfig.Senv = config.Senv
	taskRunConfig.ConfigId = config.ConfigId
	taskRunConfig.EnvTarget = config.EnvTarget
	taskRunConfig.IsCreateSubEnv = config.IsCreateSubEnv
	// update task run config
	updateConfigData, err := json.Marshal(taskRunConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "[UpdatePipelineRunTaskConfigById]更新流水线多云部署配置失败, taskRunId %d: %v", id, err)
		return err
	}
	taskRun.Config = updateConfigData
	if err := ps.pipelineRunRepo.UpdatePipelineRunTaskCols(ctx, taskRun, "config"); err != nil {
		log.ErrorWithCtx(ctx, "[UpdatePipelineRunTaskConfigById]更新流水线多云部署配置失败, taskRunId %d: %v", id, err)
		return err
	}
	return nil
}

func (ps *PipelineServer) UpdatePipelineTaskMultiCloudConfig(ctx context.Context, req *pbpipeline.UpdatePipelineTaskMultiCloudConfigReq) (*emptypb.Empty, error) {
	var deployConfig model.TaskRunMultiCloudConfig
	if err := json.Unmarshal(req.UpdatedTaskRunMultiCloudConfig, &deployConfig); err != nil {
		log.ErrorWithCtx(ctx, "[UpdatePipelineTaskMultiCloudConfig]序列化配置信息错误, pipelineRunId %d,stageRunId %d: %v", req.PipelineRunId, req.StageRunId, err)
		return new(emptypb.Empty), fmt.Errorf("工单更新流水线生产多云部署配置，序列化解析出错：%v", err)
	}
	log.InfoWithCtx(ctx, "[UpdatePipelineTaskMultiCloudConfig]更新流水线多云部署配置, pipelineRunId %d,stageRunId %d: %v", req.PipelineRunId, req.StageRunId, string(req.UpdatedTaskRunMultiCloudConfig))
	taskRuns, err := ps.pipelineRunRepo.FindTaskRunByStageRunId(ctx, req.StageRunId)
	if err != nil {
		log.ErrorWithCtx(ctx, "[UpdatePipelineTaskMultiCloudConfig]更新流水线多云部署配置失败, pipelineRunId %d,stageRunId %d: %v", req.PipelineRunId, req.StageRunId, err)
		return new(emptypb.Empty), err
	}
	// 金丝雀配置，记录金丝雀策略和发布计划id
	canaryConfig := &model.TaskRunCanaryConfig{
		CanaryPolicy:       deployConfig.CanaryPolicy,
		CanaryDeployPlanID: deployConfig.CanaryDeployPlanID,
	}
	// 所有部署任务 环境都支持删除新增 先删除再新增
	updateErr := db.Transaction(func(tx *gorm.DB) error {
		txCtx := db.CtxWithTX(ctx, tx)
		for _, taskRun := range taskRuns {
			taskType := constants.OfTaskType(taskRun.Type)
			switch taskType {
			case constants.TASK_AUTOMATION_DEPLOY:
				err = ps.syncSubTasks(txCtx, taskRun, deployConfig.MultiEnv)
				if err != nil {
					log.ErrorWithCtx(txCtx, "MultiEnv更新流水线多云部署基准配置失败, pipelineRunId %d,taskRunId %d, err: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
				if err := ps.pipelineRunRepo.UpdatePipelineRunTaskCanaryConfig(txCtx, taskRun.ID, canaryConfig); err != nil {
					log.ErrorWithCtx(ctx, "生产自动化部署任务更新流水线多云部署金丝雀配置失败, pipelineRunId %d, taskRunId %d: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
			case constants.TASK_DEPLOY_ORIGIN:
				isPass := len(deployConfig.BaseMultiEnv) <= 0
				if err = ps.pipelineRunRepo.UpdatePipelineRunTaskConfigIsPass(txCtx, taskRun.ID, isPass); err != nil {
					log.ErrorWithCtx(ctx, "更新流水线多云部署基准config isPass配置失败, pipelineRunId %d,taskRunId %d: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
				err = ps.syncSubTasks(txCtx, taskRun, deployConfig.BaseMultiEnv)
				if err != nil {
					log.ErrorWithCtx(ctx, "BaseMultiEnv去更新流水线部署基准配置失败, pipelineRunId %d,taskRunId %d, err: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
			case constants.TASK_DEPLOY_SUB:
				isPass := len(deployConfig.SubMultiEnv) <= 0
				if err = ps.pipelineRunRepo.UpdatePipelineRunTaskConfigIsPass(txCtx, taskRun.ID, isPass); err != nil {
					log.ErrorWithCtx(ctx, "更新流水线多云部署子环境config isPass配置失败, pipelineRunId %d,taskRunId %d: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
				if isPass {
					continue
				}
				// 工单可以添加删除子环境
				err = ps.syncSubTasks(txCtx, taskRun, deployConfig.SubMultiEnv)
				if err != nil {
					log.ErrorWithCtx(ctx, "SubMultiEnv更新流水线多云部署子环境配置失败, pipelineRunId %d,taskRunId %d, err: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
			case constants.TASK_DEPLOY_STAGING:
				err = ps.syncSubTasks(txCtx, taskRun, deployConfig.StagingMultiEnv)
				if err != nil {
					log.ErrorWithCtx(ctx, "StagingMultiEnv更新流水线多云部署子环境配置失败, pipelineRunId %d,taskRunId %d, err: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
				isPass := len(deployConfig.StagingMultiEnv) <= 0
				if err := ps.pipelineRunRepo.UpdatePipelineRunTaskConfigIsPass(txCtx, taskRun.ID, isPass); err != nil {
					log.ErrorWithCtx(ctx, "更新流水线多云部署预发布config isPass配置失败, pipelineRunId %d,taskRunId %d: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
			case constants.TASK_DEPLOY_CANARY:
				err = ps.syncSubTasks(txCtx, taskRun, deployConfig.CanaryMultiEnv)
				if err != nil {
					log.ErrorWithCtx(ctx, "CanaryMultiEnv更新流水线多云部署子环境配置失败, pipelineRunId %d,taskRunId %d, err: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
				isPass := len(deployConfig.CanaryMultiEnv) <= 0
				if err = ps.pipelineRunRepo.UpdatePipelineRunTaskConfigIsPass(txCtx, taskRun.ID, isPass); err != nil {
					log.ErrorWithCtx(ctx, "更新流水线多云部署金丝雀config isPass配置失败, pipelineRunId %d,taskRunId %d: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
				if err = ps.pipelineRunRepo.UpdatePipelineRunTaskCanaryConfig(txCtx, taskRun.ID, canaryConfig); err != nil {
					log.ErrorWithCtx(ctx, "更新流水线多云部署金丝雀配置失败, pipelineRunId %d, taskRunId %d: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
			case constants.TASK_OFFLINE_CANARY:
				if err = ps.pipelineRunRepo.UpdatePipelineRunTaskCanaryConfig(txCtx, taskRun.ID, canaryConfig); err != nil {
					log.ErrorWithCtx(ctx, "更新流水线多云部署金丝雀配置失败, pipelineRunId %d, taskRunId %d: %v", req.PipelineRunId, taskRun.ID, err)
					return err
				}
			}
		}
		return nil
	})
	if updateErr != nil {
		log.ErrorWithCtx(ctx, "[UpdatePipelineTaskMultiCloudConfig]更新流水线多云部署配置失败, pipelineRunId %d,stageRunId %d: %v", req.PipelineRunId, req.StageRunId, updateErr)
		return new(emptypb.Empty), updateErr
	}
	return new(emptypb.Empty), nil
}

func (ps *PipelineServer) syncSubTasks(ctx context.Context, taskRun dao.PipelineRunTask, ticketSunTasksConfig []model.TaskRunMultiCloudEnvConfig) error {
	if len(ticketSunTasksConfig) > 0 {
		search := model.SubTaskSearch{
			TaskRunId:   taskRun.ID,
			EnabledList: []int64{1},
		}
		subTasks, err := ps.pipelineRunRepo.FindSubTasksByMultiParams(ctx, search)
		if err != nil {
			log.ErrorWithCtx(ctx, "list sub tasks by main task err: %v", err)
			return err
		}

		// 工单可以添加删除子环境
		// 记录需要删除的子任务ID
		delSubTaskIds := make([]int64, 0)
		// 记录需要创建的子任务
		createSubTasks := make([]dao.PipelineRunSubtask, 0)
		// 记录需要更新的子任务
		updateSubTaskConfigIdMap := make(map[int64]model.TaskRunMultiCloudEnvConfig, 0)
		// 工单提交的子环境配置
		ticketSubTaskMap := make(map[string]model.TaskRunMultiCloudEnvConfig)
		for _, ticketSubTask := range ticketSunTasksConfig {
			ticketSubTaskMap[fmt.Sprintf("%s-%s-%s", ticketSubTask.Cluster, ticketSubTask.Namespace, ticketSubTask.Senv)] = ticketSubTask
		}
		for _, subTask := range subTasks {
			var subTaskConfig model.TaskRunMultiCloudEnvConfig
			if err = json.Unmarshal(subTask.Config, &subTaskConfig); err != nil {
				log.ErrorWithCtx(ctx, "[syncSubTasks] unmarshal sub task config err: %v", err)
				return err
			}
			key := fmt.Sprintf("%s-%s-%s", subTaskConfig.Cluster, subTaskConfig.Namespace, subTaskConfig.Senv)
			if _, ok := ticketSubTaskMap[key]; !ok {
				delSubTaskIds = append(delSubTaskIds, subTask.ID)
			} else {
				if subTaskConfig.ConfigId != ticketSubTaskMap[key].ConfigId {
					updateSubTaskConfigIdMap[subTask.ID] = ticketSubTaskMap[key]
				}
				delete(ticketSubTaskMap, key)
			}
		}
		for _, ticketSubTask := range ticketSubTaskMap {
			subTask := dao.PipelineRunSubtask{
				PipelineRunID:     taskRun.PipelineRunId,
				PipelineRunTaskId: taskRun.ID,
				Config:            []byte{},
				Enabled:           true,
				Name:              taskRun.Name,
				Type:              taskRun.Type,
				Status:            constants.PENDING,
			}
			subTask.Config, err = json.Marshal(ticketSubTask)
			if err != nil {
				log.ErrorWithCtx(ctx, "[syncSubTasks] marshal sub task config err: %v", err)
				return err
			}
			createSubTasks = append(createSubTasks, subTask)
		}

		fn := func(tx *gorm.DB) error {
			txCtx := db.CtxWithTX(ctx, tx)
			// 更新
			for subTaskId, subTaskConfig := range updateSubTaskConfigIdMap {
				if err = ps.pipelineRunRepo.UpdatePipelineRunSubtaskConfigId(txCtx, subTaskId, subTaskConfig.ConfigId); err != nil {
					log.ErrorWithCtx(ctx, "[syncSubTasks] update sub task config err: %v", err)
					return err
				}
			}
			// 删除
			if len(delSubTaskIds) > 0 {
				if err = ps.pipelineRunRepo.BatchDeletePipelineRunSubtaskByIds(txCtx, delSubTaskIds); err != nil {
					log.ErrorWithCtx(ctx, "[syncSubTasks] batch delete sub task err: %v", err)
					return err
				}
			}
			// 创建
			if len(createSubTasks) > 0 {
				if err = ps.pipelineRepo.CreateMultiCloudTaskRun(txCtx, createSubTasks); err != nil {
					log.ErrorWithCtx(ctx, "[syncSubTasks] batch create sub task err: %v", err)
					return err
				}
			}
			return nil
		}
		if err = db.Transaction(fn); err != nil {
			log.ErrorWithCtx(ctx, "sync sub tasks err: %v", err)
			return err
		}
	}
	return nil
}

func (ps *PipelineServer) GetPipelineCountByAppIds(ctx context.Context, req *pbpipeline.PipelineCountReq) (*pbpipeline.PipelineCountResp, error) {
	results, err := ps.pipelineRepo.CountPipelineByAppIds(ctx, req.AppIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据appIds[%v]获取流水线数量失败: %v", req.AppIds, err)
		return nil, err
	}
	pipelineMap := make(map[int64]int64)
	for _, result := range results {
		pipelineMap[result.AppId] = result.Count
	}
	resp := &pbpipeline.PipelineCountResp{
		CountMap: pipelineMap,
	}
	return resp, nil
}

func (ps *PipelineServer) BatchUpdatePipelineAppMsg(ctx context.Context, req *pbpipeline.PipelineAppsReq) (*pbpipeline.PipelineAppResp, error) {
	resp := &pbpipeline.PipelineAppResp{}
	pipelines, err := ps.pipelineRepo.GetPipelineByAppIds(ctx, req.AppIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据appIds[%v]获取流水线数据，发生异常: %v", req.AppIds, err)
		return resp, err
	}
	daoPipelines := make([]dao.Pipeline, 0, len(pipelines))
	now := time.Now()
	for _, p := range pipelines {
		p.RepoAddr = req.RepoAddr
		p.UpdatedAt = now
		daoPipelines = append(daoPipelines, p)
	}

	if err = ps.pipelineRepo.BatchUpdatePipelineCols(ctx, daoPipelines, "repo_addr"); err != nil {
		log.ErrorWithCtx(ctx, "批量更新流水线应用数据，发生异常: %v", err)
		return resp, err
	}

	return resp, nil
}

func (ps *PipelineServer) HandlePrepareingPipeline(ctx context.Context, req *pbpipeline.PrepareingReq) (*pbpipeline.PrepareingResp, error) {
	nctx, cancel := context.WithTimeout(context.Background(), time.Minute*1)
	defer cancel()
	err := ps.pipelineService.HandlePrepareingPipelineRun(nctx)
	return &pbpipeline.PrepareingResp{}, err
}

func (ps *PipelineServer) RunPipeline(ctx context.Context, req *pbpipeline.RunPipelingReq) (res *emptypb.Empty, err error) {
	pipeline, err := ps.pipelineService.GetAppPipelineByName(ctx, req.AppId, req.PipelineName)
	if err != nil {
		log.ErrorWithCtx(ctx, "获取流水线失败: %v", err)
		return
	}
	arg := model.PipelineRunArgs{
		Branch:               req.Branch,
		TriggerBy:            req.UserId,
		TriggerByChineseName: req.ChineseName,
		TriggerByEmployeeNo:  req.EmployeeNo,
		Description:          req.Description,
	}
	_, err = ps.pipelineService.ManualRunPipeline(ctx, pipeline.ID, arg)
	if err != nil {
		log.ErrorWithCtx(ctx, "手动触发流水线失败: %v", err)
		return
	}
	return
}

func (ps *PipelineServer) DelPipeline(ctx context.Context, req *pbpipeline.DelPipelineReq) (*emptypb.Empty, error) {
	if err := ps.pipelineService.DelPipelines(ctx, model.PipelineDelParam{
		AppId: req.AppId,
		PrjId: req.PrjId,
	}); err != nil {
		log.ErrorWithCtx(ctx, "删除流水线失败: %v", err)
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
