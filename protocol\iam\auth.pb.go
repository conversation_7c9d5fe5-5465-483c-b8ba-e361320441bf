// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.4
// source: protocol/iam/auth.proto

package iam

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetOwnUserinfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectID int64 `protobuf:"varint,1,opt,name=projectID,proto3" json:"projectID,omitempty"`
	AppID     int64 `protobuf:"varint,2,opt,name=appID,proto3" json:"appID,omitempty"`
}

func (x *GetOwnUserinfoReq) Reset() {
	*x = GetOwnUserinfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOwnUserinfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOwnUserinfoReq) ProtoMessage() {}

func (x *GetOwnUserinfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOwnUserinfoReq.ProtoReflect.Descriptor instead.
func (*GetOwnUserinfoReq) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{0}
}

func (x *GetOwnUserinfoReq) GetProjectID() int64 {
	if x != nil {
		return x.ProjectID
	}
	return 0
}

func (x *GetOwnUserinfoReq) GetAppID() int64 {
	if x != nil {
		return x.AppID
	}
	return 0
}

type GetOwnUserinfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo       *GetOwnUserinfoRespOwnUserInfo   `protobuf:"bytes,1,opt,name=userInfo,proto3" json:"userInfo,omitempty"`
	CurrentProject *GetOwnUserinfoRespSelectProject `protobuf:"bytes,2,opt,name=currentProject,proto3" json:"currentProject,omitempty"`
	HasDataPerms   bool                             `protobuf:"varint,3,opt,name=hasDataPerms,proto3" json:"hasDataPerms,omitempty"` // 是否有数据权限
}

func (x *GetOwnUserinfoResp) Reset() {
	*x = GetOwnUserinfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOwnUserinfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOwnUserinfoResp) ProtoMessage() {}

func (x *GetOwnUserinfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOwnUserinfoResp.ProtoReflect.Descriptor instead.
func (*GetOwnUserinfoResp) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{1}
}

func (x *GetOwnUserinfoResp) GetUserInfo() *GetOwnUserinfoRespOwnUserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *GetOwnUserinfoResp) GetCurrentProject() *GetOwnUserinfoRespSelectProject {
	if x != nil {
		return x.CurrentProject
	}
	return nil
}

func (x *GetOwnUserinfoResp) GetHasDataPerms() bool {
	if x != nil {
		return x.HasDataPerms
	}
	return false
}

type LoginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: from:"ticket"
	Ticket string `protobuf:"bytes,1,opt,name=ticket,proto3" json:"ticket,omitempty" from:"ticket"`
	Target string `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
}

func (x *LoginReq) Reset() {
	*x = LoginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReq) ProtoMessage() {}

func (x *LoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReq.ProtoReflect.Descriptor instead.
func (*LoginReq) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{2}
}

func (x *LoginReq) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

func (x *LoginReq) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

type LoginResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token   string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Expires int64  `protobuf:"varint,2,opt,name=expires,proto3" json:"expires,omitempty"`
}

func (x *LoginResp) Reset() {
	*x = LoginResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResp) ProtoMessage() {}

func (x *LoginResp) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResp.ProtoReflect.Descriptor instead.
func (*LoginResp) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{3}
}

func (x *LoginResp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *LoginResp) GetExpires() int64 {
	if x != nil {
		return x.Expires
	}
	return 0
}

type LogoutReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *LogoutReq) Reset() {
	*x = LogoutReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogoutReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutReq) ProtoMessage() {}

func (x *LogoutReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutReq.ProtoReflect.Descriptor instead.
func (*LogoutReq) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{4}
}

func (x *LogoutReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type LogoutResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *LogoutResp) Reset() {
	*x = LogoutResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogoutResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutResp) ProtoMessage() {}

func (x *LogoutResp) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutResp.ProtoReflect.Descriptor instead.
func (*LogoutResp) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{5}
}

func (x *LogoutResp) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type CheckTokenIsBanReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *CheckTokenIsBanReq) Reset() {
	*x = CheckTokenIsBanReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckTokenIsBanReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTokenIsBanReq) ProtoMessage() {}

func (x *CheckTokenIsBanReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTokenIsBanReq.ProtoReflect.Descriptor instead.
func (*CheckTokenIsBanReq) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{6}
}

func (x *CheckTokenIsBanReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type CheckTokenIsBanResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsBan bool `protobuf:"varint,1,opt,name=is_ban,json=isBan,proto3" json:"is_ban,omitempty"`
}

func (x *CheckTokenIsBanResp) Reset() {
	*x = CheckTokenIsBanResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckTokenIsBanResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTokenIsBanResp) ProtoMessage() {}

func (x *CheckTokenIsBanResp) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTokenIsBanResp.ProtoReflect.Descriptor instead.
func (*CheckTokenIsBanResp) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{7}
}

func (x *CheckTokenIsBanResp) GetIsBan() bool {
	if x != nil {
		return x.IsBan
	}
	return false
}

type CheckAuthenticateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *CheckAuthenticateReq) Reset() {
	*x = CheckAuthenticateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAuthenticateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAuthenticateReq) ProtoMessage() {}

func (x *CheckAuthenticateReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAuthenticateReq.ProtoReflect.Descriptor instead.
func (*CheckAuthenticateReq) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{8}
}

func (x *CheckAuthenticateReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type GenUserTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName string `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	Expire   int64  `protobuf:"varint,2,opt,name=expire,proto3" json:"expire,omitempty"`
}

func (x *GenUserTokenReq) Reset() {
	*x = GenUserTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenUserTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenUserTokenReq) ProtoMessage() {}

func (x *GenUserTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenUserTokenReq.ProtoReflect.Descriptor instead.
func (*GenUserTokenReq) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{9}
}

func (x *GenUserTokenReq) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *GenUserTokenReq) GetExpire() int64 {
	if x != nil {
		return x.Expire
	}
	return 0
}

type GetOwnUserinfoRespSelectProject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Role string `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *GetOwnUserinfoRespSelectProject) Reset() {
	*x = GetOwnUserinfoRespSelectProject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOwnUserinfoRespSelectProject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOwnUserinfoRespSelectProject) ProtoMessage() {}

func (x *GetOwnUserinfoRespSelectProject) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOwnUserinfoRespSelectProject.ProtoReflect.Descriptor instead.
func (*GetOwnUserinfoRespSelectProject) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetOwnUserinfoRespSelectProject) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetOwnUserinfoRespSelectProject) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

type GetOwnUserinfoRespOwnUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	// @gotags: json:"chineseName"
	ChineseName string `protobuf:"bytes,3,opt,name=chinese_name,json=chineseName,proto3" json:"chineseName"`
	Email       string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	// @gotags: json:"employeeNo"
	EmployeeNo string `protobuf:"bytes,5,opt,name=employee_no,json=employeeNo,proto3" json:"employeeNo"`
	// @gotags: json:"role"
	Role string `protobuf:"bytes,6,opt,name=role,proto3" json:"role"`
}

func (x *GetOwnUserinfoRespOwnUserInfo) Reset() {
	*x = GetOwnUserinfoRespOwnUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_iam_auth_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOwnUserinfoRespOwnUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOwnUserinfoRespOwnUserInfo) ProtoMessage() {}

func (x *GetOwnUserinfoRespOwnUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_iam_auth_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOwnUserinfoRespOwnUserInfo.ProtoReflect.Descriptor instead.
func (*GetOwnUserinfoRespOwnUserInfo) Descriptor() ([]byte, []int) {
	return file_protocol_iam_auth_proto_rawDescGZIP(), []int{1, 1}
}

func (x *GetOwnUserinfoRespOwnUserInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetOwnUserinfoRespOwnUserInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *GetOwnUserinfoRespOwnUserInfo) GetChineseName() string {
	if x != nil {
		return x.ChineseName
	}
	return ""
}

func (x *GetOwnUserinfoRespOwnUserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetOwnUserinfoRespOwnUserInfo) GetEmployeeNo() string {
	if x != nil {
		return x.EmployeeNo
	}
	return ""
}

func (x *GetOwnUserinfoRespOwnUserInfo) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

var File_protocol_iam_auth_proto protoreflect.FileDescriptor

var file_protocol_iam_auth_proto_rawDesc = []byte{
	0x0a, 0x17, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x69, 0x61, 0x6d, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x47, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x4f, 0x77, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x44, 0x22, 0xa7, 0x03, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4f, 0x77, 0x6e, 0x55,
	0x73, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x69, 0x61, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x77, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x6f, 0x77, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x0e,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x77,
	0x6e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x68,
	0x61, 0x73, 0x44, 0x61, 0x74, 0x61, 0x50, 0x65, 0x72, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0c, 0x68, 0x61, 0x73, 0x44, 0x61, 0x74, 0x61, 0x50, 0x65, 0x72, 0x6d, 0x73, 0x1a,
	0x33, 0x0a, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x72, 0x6f, 0x6c, 0x65, 0x1a, 0xa7, 0x01, 0x0a, 0x0b, 0x6f, 0x77, 0x6e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0x3a,
	0x0a, 0x08, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x22, 0x3b, 0x0a, 0x09, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x22, 0x21, 0x0a, 0x09, 0x4c, 0x6f, 0x67, 0x6f, 0x75,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x1e, 0x0a, 0x0a, 0x4c, 0x6f,
	0x67, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x2a, 0x0a, 0x12, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x73, 0x42, 0x61, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x2c, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x73, 0x42, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x73, 0x5f, 0x62, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69,
	0x73, 0x42, 0x61, 0x6e, 0x22, 0x2c, 0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x75, 0x74,
	0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x22, 0x46, 0x0a, 0x0f, 0x47, 0x65, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x32, 0xf1, 0x02, 0x0a, 0x0b, 0x41,
	0x75, 0x74, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x4f, 0x77, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x77, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x77,
	0x6e, 0x55, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x28, 0x0a, 0x05, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x0d, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x0e, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x2b, 0x0a, 0x06, 0x4c, 0x6f, 0x67,
	0x6f, 0x75, 0x74, 0x12, 0x0e, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x0f, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x73, 0x42, 0x61, 0x6e, 0x12, 0x17, 0x2e, 0x69, 0x61, 0x6d, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x73, 0x42, 0x61, 0x6e, 0x52,
	0x65, 0x71, 0x1a, 0x18, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x49, 0x73, 0x42, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x46,
	0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x12, 0x19, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41,
	0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x36, 0x0a, 0x0c, 0x47, 0x65, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x47, 0x65, 0x6e,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x0e, 0x2e, 0x69,
	0x61, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x0e,
	0x5a, 0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x69, 0x61, 0x6d, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protocol_iam_auth_proto_rawDescOnce sync.Once
	file_protocol_iam_auth_proto_rawDescData = file_protocol_iam_auth_proto_rawDesc
)

func file_protocol_iam_auth_proto_rawDescGZIP() []byte {
	file_protocol_iam_auth_proto_rawDescOnce.Do(func() {
		file_protocol_iam_auth_proto_rawDescData = protoimpl.X.CompressGZIP(file_protocol_iam_auth_proto_rawDescData)
	})
	return file_protocol_iam_auth_proto_rawDescData
}

var file_protocol_iam_auth_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_protocol_iam_auth_proto_goTypes = []any{
	(*GetOwnUserinfoReq)(nil),               // 0: iam.GetOwnUserinfoReq
	(*GetOwnUserinfoResp)(nil),              // 1: iam.GetOwnUserinfoResp
	(*LoginReq)(nil),                        // 2: iam.LoginReq
	(*LoginResp)(nil),                       // 3: iam.LoginResp
	(*LogoutReq)(nil),                       // 4: iam.LogoutReq
	(*LogoutResp)(nil),                      // 5: iam.LogoutResp
	(*CheckTokenIsBanReq)(nil),              // 6: iam.CheckTokenIsBanReq
	(*CheckTokenIsBanResp)(nil),             // 7: iam.CheckTokenIsBanResp
	(*CheckAuthenticateReq)(nil),            // 8: iam.CheckAuthenticateReq
	(*GenUserTokenReq)(nil),                 // 9: iam.GenUserTokenReq
	(*GetOwnUserinfoRespSelectProject)(nil), // 10: iam.GetOwnUserinfoResp.selectProject
	(*GetOwnUserinfoRespOwnUserInfo)(nil),   // 11: iam.GetOwnUserinfoResp.ownUserInfo
	(*emptypb.Empty)(nil),                   // 12: google.protobuf.Empty
}
var file_protocol_iam_auth_proto_depIdxs = []int32{
	11, // 0: iam.GetOwnUserinfoResp.userInfo:type_name -> iam.GetOwnUserinfoResp.ownUserInfo
	10, // 1: iam.GetOwnUserinfoResp.currentProject:type_name -> iam.GetOwnUserinfoResp.selectProject
	0,  // 2: iam.AuthService.GetOwnUserinfo:input_type -> iam.GetOwnUserinfoReq
	2,  // 3: iam.AuthService.Login:input_type -> iam.LoginReq
	4,  // 4: iam.AuthService.Logout:input_type -> iam.LogoutReq
	6,  // 5: iam.AuthService.CheckTokenIsBan:input_type -> iam.CheckTokenIsBanReq
	8,  // 6: iam.AuthService.CheckAuthenticate:input_type -> iam.CheckAuthenticateReq
	9,  // 7: iam.AuthService.GenUserToken:input_type -> iam.GenUserTokenReq
	1,  // 8: iam.AuthService.GetOwnUserinfo:output_type -> iam.GetOwnUserinfoResp
	3,  // 9: iam.AuthService.Login:output_type -> iam.LoginResp
	5,  // 10: iam.AuthService.Logout:output_type -> iam.LogoutResp
	7,  // 11: iam.AuthService.CheckTokenIsBan:output_type -> iam.CheckTokenIsBanResp
	12, // 12: iam.AuthService.CheckAuthenticate:output_type -> google.protobuf.Empty
	3,  // 13: iam.AuthService.GenUserToken:output_type -> iam.LoginResp
	8,  // [8:14] is the sub-list for method output_type
	2,  // [2:8] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_protocol_iam_auth_proto_init() }
func file_protocol_iam_auth_proto_init() {
	if File_protocol_iam_auth_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protocol_iam_auth_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GetOwnUserinfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_iam_auth_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetOwnUserinfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_iam_auth_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*LoginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_iam_auth_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*LoginResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_iam_auth_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*LogoutReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_iam_auth_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*LogoutResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_iam_auth_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*CheckTokenIsBanReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_iam_auth_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*CheckTokenIsBanResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_iam_auth_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*CheckAuthenticateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_iam_auth_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GenUserTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_iam_auth_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*GetOwnUserinfoRespSelectProject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_iam_auth_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*GetOwnUserinfoRespOwnUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protocol_iam_auth_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protocol_iam_auth_proto_goTypes,
		DependencyIndexes: file_protocol_iam_auth_proto_depIdxs,
		MessageInfos:      file_protocol_iam_auth_proto_msgTypes,
	}.Build()
	File_protocol_iam_auth_proto = out.File
	file_protocol_iam_auth_proto_rawDesc = nil
	file_protocol_iam_auth_proto_goTypes = nil
	file_protocol_iam_auth_proto_depIdxs = nil
}
