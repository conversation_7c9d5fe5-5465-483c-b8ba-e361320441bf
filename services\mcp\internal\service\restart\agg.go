package restart

import (
	"context"
	"fmt"

	cloudagg "52tt.com/cicd/pkg/cloud/aggregate"
	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
)

// 各种 可以支持重启 资源的重启，服务  pod 等等

var RestartAgg = &restartAgg{}

type restartAgg struct {
	cloudagg cloudagg.AggClient
}

func InitAgg(cloudagg cloudagg.AggClient) {
	RestartAgg = &restartAgg{
		cloudagg: cloudagg,
	}
	return
}

func (agg *restartAgg) Restart(args *RestartWorkloadArgs) (err error) {
	workLoadKind := constack.WorkloadKind_WORKLOAD_KIND_UNKNOWN
	switch args.Kind {
	case "Deployment":
		workLoadKind = constack.WorkloadKind_DEPLOYMENT
	case "StatefulSet":
		workLoadKind = constack.WorkloadKind_STATEFUL_SET
	default:
		err = fmt.Errorf("kind %s not support", args.Kind)
		return
	}

	_, err = agg.cloudagg.Restart(context.Background(), &constack.RestartRequest{
		Cluster:   args.ClusterName,
		Namespace: args.Namespace,
		Name:      args.Name,
		Kind:      workLoadKind,
	})

	return

}
