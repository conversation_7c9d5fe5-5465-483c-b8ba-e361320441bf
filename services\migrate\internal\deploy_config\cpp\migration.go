package cpp

import (
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/protocol/deploy"
	"52tt.com/cicd/services/migrate/internal/deploy_config"
	"52tt.com/cicd/services/migrate/internal/deploy_config/common"
	db "52tt.com/cicd/services/migrate/pkg/database"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

func ExportAllConfig(envs []string, services []string, isUpdate bool, targetProjectName string) error {
	for _, env := range envs {
		err := ExportConfig(env, services, isUpdate, targetProjectName)
		if err != nil {
			return err
		}
	}
	return nil
}

func ExportConfig(env string, services []string, isUpdate bool, targetProjectName string) error {
	k8sMap := map[string][]int{
		db.Dev:  {},
		db.Test: {140},
		db.Prod: {22, 77, 172},
	}
	var configs []deploy_config.UnitDeployConfig
	deployConfigs, err := deploy_config.GetProjectDeployConfig(env, k8sMap)
	if err != nil {
		return err
	}
	for _, dc := range deployConfigs {
		if dc.Unit.App.Team.Name != "TT后台(cpp)" || dc.Unit.Status != "online" {
			continue
		}
		if len(services) > 0 && !isContainUnit(dc.Unit.Name, services) {
			continue
		}
		configs = append(configs, deploy_config.UnitDeployConfig{
			Resource:       dc.Resource,
			Values:         dc.Values,
			Default:        dc.Default,
			K8sName:        dc.K8sEnv.AssetsK8sCluster.Name,
			NameSpace:      dc.K8sEnv.NameSpace,
			K8sDescription: dc.K8sEnv.AssetsK8sCluster.Description,
			K8sPrivateId:   dc.K8sEnv.AssetsK8sCluster.PrivateId,
			UnitName:       dc.Unit.Name,
			ObjectId:       dc.Unit.ObjectId,
			TeamName:       dc.Unit.App.Team.Name,
			ChartName:      dc.Chart.Name,
			ChartUrl:       dc.Chart.Url,
			ChartVersion:   dc.Chart.Version,
			ChartType:      dc.Chart.CharType,
			UnitId:         dc.UnitId,
			K8sEnvId:       dc.K8sEnvId,
		})
	}
	//err = deploy_config.CreateConfig(configs)
	err = PullConfig(configs, targetProjectName, isUpdate)
	if err != nil {
		return err
	}
	return nil
}

func PullConfig(configs []deploy_config.UnitDeployConfig, targetProjectName string, isUpdate bool) error {
	dbEnv := db.Prod // 同步环境
	//dbEnv := db.Test

	hpaMap := make(map[string]HpaChart)
	for _, conf := range configs {
		var tempHpa HpaChart
		if conf.Resource == "HPA" || conf.Resource == "MULTI-HPA" {
			json.Unmarshal(conf.Values, &tempHpa)
			log.Debugf("unit name: %v", conf.UnitName)
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			hpaMap[unitKey] = tempHpa
		}
	}
	log.Debugf("hpaMap: %v", hpaMap)

	count := 0
	var successApps []string
	for _, conf := range configs {
		if conf.ChartName == "svrkit-server" {
			var err error
			// 处理数据导入转换逻辑
			log.Debugf("unitId: %v, unitName: %v", conf.UnitId, conf.UnitName)
			var valueConfig WorkloadChart
			_ = json.Unmarshal(conf.Default, &valueConfig)
			_ = json.Unmarshal(conf.Values, &valueConfig)

			appBaseConfig, err := HandAppBaseConfig(valueConfig)
			if err != nil {
				return err
			}
			appAdvancedConfig, err := HandAppAdvancedConfig(valueConfig, conf.UnitName)
			if err != nil {
				return err
			}
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			traitConfig, err := HandTraitConfig(valueConfig, unitKey, hpaMap)
			if err != nil {
				return err
			}

			// 数据库操作：查询app、查询/创建deploy_metadata、创建deploy_config、更新app
			// 1.查询app
			newTeamName := conf.TeamName
			if targetProjectName != "" {
				newTeamName = targetProjectName
			}
			appId, err := common.FindAppIdBy(dbEnv, newTeamName, conf.UnitName)
			if err != nil {
				return err
			}
			if appId == 0 {
				log.Debugf("app name: %v is not exist", conf.UnitName)
				continue
			}

			// 2.查询/创建deploy_metadata
			metadata := deploy_config.DeployMetadata{
				Env:       getEnvEnum(conf.K8sName),
				EnvTarget: 1, //默认基准环境
				Cluster:   getClusterName(conf.K8sName),
				Namespace: conf.NameSpace,
				AppID:     appId,
				AppName:   conf.UnitName,
				ConfigID:  0,
			}
			err = deploy_config.GetDeployConfigLastVersion(dbEnv, &metadata)
			if err != nil {
				return err
			}
			if metadata.Config != nil && metadata.Config.CreatedBy != 71 {
				log.Infof("has existed config, unitId: %v, unitName: %v, createdBy: %v", conf.UnitId, conf.UnitName, metadata.Config.CreatedByChineseName)
			}
			// 只验证数据转换是否可行，不更新
			if !isUpdate {
				log.Infof("ignore update, unitId: %v, unitName: %v", conf.UnitId, conf.UnitName)
				continue
			}
			version := 1
			if metadata.ID != 0 {
				if metadata.Config != nil {
					version = metadata.Config.Version + 1
					//continue
				}
				// 只更新指定用户的数据
				if metadata.Config.CreatedBy != 71 {
					log.Infof("has existed config ignore update, unitId: %v, unitName: %v, createdBy: %v", conf.UnitId, conf.UnitName, metadata.Config.CreatedByChineseName)
					continue
				}
			} else {
				err = deploy_config.CreateDeployMetadata(dbEnv, &metadata)
				if err != nil {
					return err
				}
			}

			// 3.创建deploy_config
			deployConfig := deploy_config.DeployConfig{
				AppBasicConfig:       appBaseConfig,
				AppAdvancedConfig:    appAdvancedConfig,
				TraitConfig:          traitConfig,
				Version:              version,
				CreatedBy:            71, //创建用户id
				CreatedByChineseName: "陈伟良",
				CreatedByEmployeeNo:  "T2517",
				TemplateID:           getTemplateId(int64(metadata.Env), targetProjectName),
				MetadataID:           metadata.ID,
			}
			deployConfig.ConfigType = 1
			err = deploy_config.CreateDeployConfig(dbEnv, &deployConfig)
			if err != nil {
				return err
			}
			metadata.ConfigID = deployConfig.ID
			err = deploy_config.UpdateDeployMetadata(dbEnv, &metadata)
			if err != nil {
				return err
			}

			if err != nil {
				return err
			}

			successApps = append(successApps, conf.UnitName)
			count++
		}
	}
	log.Infof("svrkit-server-finish:%d", count)
	log.Infof("svrkit-server-finish-list: %+v", successApps)
	return nil
}

func HandAppBaseConfig(config WorkloadChart) ([]byte, error) {
	res := deploy.AppBasicConfig{
		NetworkType:  "",
		NetworkPorts: []*deploy.AppBasicConfig_Port{},
		Annotations:  []*deploy.Pair{},
		Envs:         []*deploy.Pair{},
		Commands:     []string{},
		Configs:      []*deploy.AppBasicConfig_Config{},
	}
	// 网络配置-服务协议和端口
	if config.Service.Enabled {
		res.NetworkType = config.Service.Type
		// 服务端口
		serviceName := config.Service.Name
		if len(serviceName) >= 15 {
			serviceName = fmt.Sprintf("service-%d", 0)
		}
		res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
			Name:         serviceName,
			InternalPort: config.Service.TargetPort,
			ExternalPort: config.Service.Port,
		})
		// multi-port
		if config.Service.MultiPorts.Enabled {
			count := 1
			for _, port := range config.Service.MultiPorts.Items {
				serviceName = port.Name
				if len(serviceName) >= 15 {
					serviceName = fmt.Sprintf("service-%d", count)
				}
				res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
					Name:         serviceName,
					InternalPort: port.TargetPort,
					ExternalPort: port.Port,
				})
				count++
			}
		}
		// 开启监控
		if config.Service.StartReportMetrics {
			var targetPort int64 = 8078
			if config.Discoveryagent.Enable {
				targetPort = 18080
			}
			res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
				Name:         "http-admin",
				InternalPort: targetPort,
				ExternalPort: 8078,
			})
		}

		// 服务注解
		for key, value := range config.Service.Annotations {
			res.Annotations = append(res.Annotations, &deploy.Pair{Key: key, Value: handleInterfaceToString(value)})
		}
	}
	// 环境变量
	location := 0
	envLocation := make(map[string]int)
	envs := make([]*deploy.Pair, 0)
	// 全局环境变量
	for _, env := range config.Global.Env {
		if env.Name != "" && env.Value != "" {
			envLocation[env.Name] = location
			envs = append(envs, &deploy.Pair{Key: env.Name, Value: env.Value})
			location++
		}
	}
	// 服务环境变量
	if config.TempNamespace.Deploy.Env.Enabled {
		for _, env := range config.Deploy.Env {
			if env.Name != "" && env.Value != "" {
				if _, ok := envLocation[env.Name]; ok {
					envs[envLocation[env.Name]].Value = env.Value
				} else {
					envs = append(envs, &deploy.Pair{Key: env.Name, Value: env.Value})
				}
			}
		}
	}
	res.Envs = envs
	// 命令
	for _, command := range config.Deploy.Command {
		res.Commands = append(res.Commands, command)
	}
	// 配置文件
	for fileName, fileContent := range config.ConfigFiles {
		res.Configs = append(res.Configs, &deploy.AppBasicConfig_Config{DirPath: "/home/<USER>/etc/server/", FileName: fileName, Content: fileContent})
	}
	// 二进制配置文件
	if config.TempNamespace.BinaryFiles.Enabled {
		for fileName, fileContent := range config.BinaryFiles {
			res.Configs = append(res.Configs, &deploy.AppBasicConfig_Config{DirPath: "/home/<USER>/etc/server/", FileName: fileName, Content: fileContent})
		}
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func handleInterfaceToString(val interface{}) string {
	t := reflect.TypeOf(val)
	switch t.Kind() {
	case reflect.Float64, reflect.Float32:
		return strconv.FormatFloat(val.(float64), 'f', -1, 64)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return strconv.FormatInt(val.(int64), 10)
	case reflect.Bool:
		return strconv.FormatBool(val.(bool))
	default:
		return val.(string)
	}
}

func builderHandler() *deploy.HealthCheck_HealthCheckHandler {
	return &deploy.HealthCheck_HealthCheckHandler{
		Type: "",
		Exec: &deploy.ExecAction{
			Command: []string{},
		},
		TcpSocket: &deploy.TCPSocketAction{},
		HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
		Grpc:      &deploy.GRPCAction{},
		Probe: &deploy.HealthCheck_Probe{
			InitialDelaySeconds: 0,
			PeriodSeconds:       10,
			TimeoutSeconds:      1,
			SuccessThreshold:    1,
			FailureThreshold:    3,
		},
	}
}

func HandAppAdvancedConfig(config WorkloadChart, unitName string) ([]byte, error) {
	res := deploy.AppAdvancedConfig{
		Labels:      []*deploy.Pair{},
		Annotations: []*deploy.Pair{},
		HostAliases: []*deploy.HostAlias{},
		HealthCheck: &deploy.HealthCheck{
			ReadinessProbe: builderHandler(),
			LivenessProbe:  builderHandler(),
			StartupProbe:   builderHandler(),
			Types:          []string{},
		},
		ServiceAccountName: "default",
		Lifecycle: &deploy.Lifecycle{
			PostStart: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			PreStop: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			Types: []string{},
		},
		MountConfig: &deploy.MountConfig{
			Volumes:      []*deploy.Volume{},
			VolumeMounts: []*deploy.VolumeMount{},
		},
	}

	expectedLabel := map[string]bool{
		"uuid":       true,
		"cluster_id": true,
		"env":        true,
		"lang":       true,
		"type":       true,
	}
	// label 标签
	// 默认标签
	res.Labels = append(res.Labels, &deploy.Pair{Key: "app-type", Value: "cppsvr"})
	res.Labels = append(res.Labels, &deploy.Pair{Key: "role", Value: "service"})
	for key, value := range config.Deploy.Labels {
		if _, ok := expectedLabel[key]; ok {
			continue
		}
		res.Labels = append(res.Labels, &deploy.Pair{Key: key, Value: value})
	}
	for key, value := range config.Labels {
		if _, ok := expectedLabel[key]; ok {
			continue
		}
		res.Labels = append(res.Labels, &deploy.Pair{Key: key, Value: value})
	}
	// 注解
	// 是否关闭 istio
	if config.IstioDisabled {
		res.Annotations = append(res.Annotations, &deploy.Pair{Key: "sidecar.istio.io/inject", Value: "false"})
	}

	location := 0
	annotationLocation := make(map[string]int)
	annotations := make([]*deploy.Pair, 0)
	for k, v := range config.Global.Annotations {
		// 监控相关
		if k == "telemetry.mesh.quwan.io/customMetricsPath" ||
			k == "telemetry.mesh.quwan.io/customMetricsPort" ||
			k == "telemetry.mesh.quwan.io/customMetricsScrape" ||
			k == "telemetry.mesh.quwan.io/customMetricsContainer" {
			continue
		}
		// sidecar
		if k == "sidecar.istio.io/proxyCPU" ||
			k == "sidecar.istio.io/proxyMemory" ||
			k == "sidecar.istio.io/proxyCPULimit" ||
			k == "sidecar.istio.io/proxyMemoryLimit" {
			continue
		}
		annotationLocation[k] = location
		annotations = append(annotations, &deploy.Pair{Key: k, Value: handleInterfaceToString(v)})
		location++
	}
	for k, v := range config.Deploy.Annotations {
		// 监控相关
		if k == "telemetry.mesh.quwan.io/customMetricsPath" ||
			k == "telemetry.mesh.quwan.io/customMetricsPort" ||
			k == "telemetry.mesh.quwan.io/customMetricsScrape" ||
			k == "telemetry.mesh.quwan.io/customMetricsContainer" {
			continue
		}
		// sidecar
		if k == "sidecar.istio.io/proxyCPU" ||
			k == "sidecar.istio.io/proxyMemory" ||
			k == "sidecar.istio.io/proxyCPULimit" ||
			k == "sidecar.istio.io/proxyMemoryLimit" {
			continue
		}
		// value覆盖默认值
		if _, ok := annotationLocation[k]; ok {
			annotations[annotationLocation[k]].Value = handleInterfaceToString(v)
		} else {
			annotations = append(annotations, &deploy.Pair{Key: k, Value: handleInterfaceToString(v)})
		}
	}
	res.Annotations = annotations

	// 主机别名

	// 就绪探针
	if config.Probe.ReadinessEnabled {
		res.HealthCheck.Types = append(res.HealthCheck.Types, "readinessProbe")
		if len(config.Probe.ReadinessProbe.Exec.Command) > 0 {
			res.HealthCheck.ReadinessProbe.Type = "exec"
			res.HealthCheck.ReadinessProbe.Exec = &deploy.ExecAction{}
			for _, cmd := range config.Probe.ReadinessProbe.Exec.Command {
				res.HealthCheck.ReadinessProbe.Exec.Command = append(res.HealthCheck.ReadinessProbe.Exec.Command, cmd)
			}
		} else if config.Probe.ReadinessProbe.TCPSocket.Port > 0 {
			res.HealthCheck.ReadinessProbe.Type = "tcpSocket"
			res.HealthCheck.ReadinessProbe.TcpSocket = &deploy.TCPSocketAction{
				Port: config.Probe.ReadinessProbe.TCPSocket.Port,
			}
		} else if config.Probe.ReadinessProbe.HTTPGet.Port > 0 {
			res.HealthCheck.ReadinessProbe.Type = "httpGet"
			res.HealthCheck.ReadinessProbe.HttpGet = &deploy.HTTPGetAction{
				Path: config.Probe.ReadinessProbe.HTTPGet.Path,
				Port: config.Probe.ReadinessProbe.HTTPGet.Port,
			}
			for _, header := range config.Probe.ReadinessProbe.HTTPGet.HTTPHeaders {
				res.HealthCheck.ReadinessProbe.HttpGet.Headers = append(res.HealthCheck.ReadinessProbe.HttpGet.Headers,
					&deploy.Pair{Key: header.Name, Value: header.Value})
			}
		}

		res.HealthCheck.ReadinessProbe.Probe = &deploy.HealthCheck_Probe{}
		// 探测间隔
		if config.Probe.ReadinessProbe.PeriodSeconds > 0 {
			res.HealthCheck.ReadinessProbe.Probe.PeriodSeconds = config.Probe.ReadinessProbe.PeriodSeconds
		} else {
			res.HealthCheck.ReadinessProbe.Probe.PeriodSeconds = 10
		}
		// 启动延时
		if config.Probe.ReadinessProbe.InitialDelaySeconds > 0 {
			res.HealthCheck.ReadinessProbe.Probe.InitialDelaySeconds = config.Probe.ReadinessProbe.InitialDelaySeconds
		} else {
			res.HealthCheck.ReadinessProbe.Probe.InitialDelaySeconds = 0
		}
		// 超时时长
		if config.Probe.ReadinessProbe.TimeoutSeconds > 0 {
			res.HealthCheck.ReadinessProbe.Probe.TimeoutSeconds = config.Probe.ReadinessProbe.TimeoutSeconds
		} else {
			res.HealthCheck.ReadinessProbe.Probe.TimeoutSeconds = 1
		}
		// 成功阈值
		if config.Probe.ReadinessProbe.SuccessThreshold > 0 {
			res.HealthCheck.ReadinessProbe.Probe.SuccessThreshold = config.Probe.ReadinessProbe.SuccessThreshold
		} else {
			res.HealthCheck.ReadinessProbe.Probe.SuccessThreshold = 1
		}
		// 失败阈值
		if config.Probe.ReadinessProbe.FailureThreshold > 0 {
			res.HealthCheck.ReadinessProbe.Probe.FailureThreshold = config.Probe.ReadinessProbe.FailureThreshold
		} else {
			res.HealthCheck.ReadinessProbe.Probe.FailureThreshold = 3
		}
	}
	// 存活探针
	if config.Probe.LivenessEnabled {
		res.HealthCheck.Types = append(res.HealthCheck.Types, "livenessProbe")
		if len(config.Probe.LivenessProbe.Exec.Command) > 0 {
			res.HealthCheck.LivenessProbe.Type = "exec"
			res.HealthCheck.LivenessProbe.Exec = &deploy.ExecAction{}
			for _, cmd := range config.Probe.LivenessProbe.Exec.Command {
				res.HealthCheck.LivenessProbe.Exec.Command = append(res.HealthCheck.LivenessProbe.Exec.Command, cmd)
			}
		} else if config.Probe.LivenessProbe.TCPSocket.Port > 0 {
			res.HealthCheck.LivenessProbe.Type = "tcpSocket"
			res.HealthCheck.LivenessProbe.TcpSocket = &deploy.TCPSocketAction{
				Port: config.Probe.LivenessProbe.TCPSocket.Port,
			}
		} else if config.Probe.LivenessProbe.HTTPGet.Port > 0 {
			res.HealthCheck.LivenessProbe.Type = "httpGet"
			res.HealthCheck.LivenessProbe.HttpGet = &deploy.HTTPGetAction{
				Path: config.Probe.LivenessProbe.HTTPGet.Path,
				Port: config.Probe.LivenessProbe.HTTPGet.Port,
			}
			for _, header := range config.Probe.LivenessProbe.HTTPGet.HTTPHeaders {
				res.HealthCheck.LivenessProbe.HttpGet.Headers = append(res.HealthCheck.LivenessProbe.HttpGet.Headers,
					&deploy.Pair{Key: header.Name, Value: header.Value})
			}
		}

		res.HealthCheck.LivenessProbe.Probe = &deploy.HealthCheck_Probe{}
		// 探测间隔
		if config.Probe.LivenessProbe.PeriodSeconds > 0 {
			res.HealthCheck.LivenessProbe.Probe.PeriodSeconds = config.Probe.LivenessProbe.PeriodSeconds
		} else {
			res.HealthCheck.LivenessProbe.Probe.PeriodSeconds = 10
		}
		// 启动延时
		if config.Probe.LivenessProbe.InitialDelaySeconds > 0 {
			res.HealthCheck.LivenessProbe.Probe.InitialDelaySeconds = config.Probe.LivenessProbe.InitialDelaySeconds
		} else {
			res.HealthCheck.LivenessProbe.Probe.InitialDelaySeconds = 0
		}
		// 超时时长
		if config.Probe.LivenessProbe.TimeoutSeconds > 0 {
			res.HealthCheck.LivenessProbe.Probe.TimeoutSeconds = config.Probe.LivenessProbe.TimeoutSeconds
		} else {
			res.HealthCheck.LivenessProbe.Probe.TimeoutSeconds = 1
		}
		// 成功阈值
		if config.Probe.LivenessProbe.SuccessThreshold > 0 {
			res.HealthCheck.LivenessProbe.Probe.SuccessThreshold = config.Probe.LivenessProbe.SuccessThreshold
		} else {
			res.HealthCheck.LivenessProbe.Probe.SuccessThreshold = 1
		}
		// 失败阈值
		if config.Probe.LivenessProbe.FailureThreshold > 0 {
			res.HealthCheck.LivenessProbe.Probe.FailureThreshold = config.Probe.LivenessProbe.FailureThreshold
		} else {
			res.HealthCheck.LivenessProbe.Probe.FailureThreshold = 3
		}
	}
	// 挂载配置
	res.MountConfig = &deploy.MountConfig{
		Volumes:      []*deploy.Volume{},
		VolumeMounts: []*deploy.VolumeMount{},
	}
	// 默认挂载文件
	res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
		Type:       "emptyDir",
		Medium:     "default",
		VolumeName: "home-godman-corefile",
	})
	res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
		VolumeName: "home-godman-corefile",
		MountPoint: "/home/<USER>/corefile",
	})

	volumesList := make([]Volumes, 0)
	volumeMountsList := make([]VolumeMounts, 0)
	// global 挂载文件
	for _, volume := range config.Global.Volumes {
		volumesList = append(volumesList, volume)
	}
	for _, volume := range config.Global.VolumeMounts {
		volumeMountsList = append(volumeMountsList, volume)
	}
	// 挂载配置文件
	if config.TempNamespace.Volumes.Enabled {
		for _, volume := range config.Volumes {
			volumesList = append(volumesList, volume)

		}
	}
	if config.TempNamespace.VolumeMounts.Enabled {
		for _, volume := range config.VolumeMounts {
			volumeMountsList = append(volumeMountsList, volume)
		}
	}
	// 挂载文件
	for _, volume := range volumesList {
		if volume.ConfigMap.Name != "" {
			res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
				Type:       "configMap",
				VolumeName: volume.Name,
				RefName:    volume.ConfigMap.Name,
			})
			continue
		} else if volume.HostPath.Path != "" {
			res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
				Type:       "hostPath",
				VolumeName: volume.Name,
				ReadOnly:   true,
				RefName:    strings.TrimSuffix(volume.HostPath.Path, "/"),
			})
		} else if volume.PersistentVolumeClaim.ClaimName != "" {
			res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
				Type:       "PVC",
				ReadOnly:   false,
				VolumeName: volume.Name,
				RefName:    volume.PersistentVolumeClaim.ClaimName,
			})
		} else if volume.Secret.SecretName != "" {
			res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
				Type:       "secret",
				VolumeName: volume.Name,
				RefName:    volume.Secret.SecretName,
			})
		} else if volume.Name != "" {
			res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
				Type:       "emptyDir",
				Medium:     "default",
				VolumeName: volume.Name,
			})
		}
	}
	// 挂载目录
	for _, volume := range volumeMountsList {
		if volume.Name != "" {
			res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
				VolumeName: volume.Name,
				MountPoint: volume.MountPath,
				SubPath:    volume.SubPath,
			})
		}
	}

	// 服务账号
	if config.Deploy.SelfOwnServiceAccount {
		res.ServiceAccountName = fmt.Sprintf("%v-sa", unitName)
	}

	// lifecycle

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

type resources struct {
	Requests struct {
		CPU    float32
		Memory float32
	}
	Limits struct {
		CPU    float32
		Memory float32
	}
}

func handleResource(resource Resources) resources {
	var res resources
	cpuReq := resource.Requests.CPU
	if strings.Contains(cpuReq, "m") || strings.Contains(cpuReq, "M") {
		//单位为m
		reqCpu, _ := strconv.ParseFloat(cpuReq[0:len(cpuReq)-1], 32)
		res.Requests.CPU = float32(reqCpu / 1000)
	} else {
		//单位为核
		reqCpu, _ := strconv.ParseFloat(cpuReq, 32)
		res.Requests.CPU = float32(reqCpu)
	}

	memReq, _ := strconv.ParseFloat(resource.Requests.Memory[0:len(resource.Requests.Memory)-2], 32)
	if strings.Contains(resource.Requests.Memory, "Gi") {
		res.Requests.Memory = float32(memReq * 1024)
	} else {
		res.Requests.Memory = float32(memReq)
	}

	cpuLim := resource.Limits.CPU
	if strings.Contains(cpuLim, "m") || strings.Contains(cpuLim, "M") {
		//单位为m
		limCpu, _ := strconv.ParseFloat(cpuLim[0:len(cpuLim)-1], 32)
		res.Limits.CPU = float32(limCpu / 1000)
	} else {
		//单位为核
		limCpu, _ := strconv.ParseFloat(cpuLim, 32)
		res.Limits.CPU = float32(limCpu)
	}

	limMem, _ := strconv.ParseFloat(resource.Limits.Memory[0:len(resource.Limits.Memory)-2], 32)
	if strings.Contains(resource.Limits.Memory, "Gi") {
		res.Limits.Memory = float32(limMem * 1024)
	} else {
		res.Limits.Memory = float32(limMem)
	}

	return res
}

func HandTraitConfig(config WorkloadChart, unitKey string, hpaMap map[string]HpaChart) ([]byte, error) {
	var res deploy.TraitConfig
	// 资源限制
	res.ResourceConstraints = &deploy.ResourceConstraints{Resources: &deploy.Resources{}, Sidecar: &deploy.ResourceConstraints_Sidecar{}}
	baseResources := handleResource(config.Resources)
	res.ResourceConstraints.Resources.RequestCpu = baseResources.Requests.CPU
	res.ResourceConstraints.Resources.RequestMemory = baseResources.Requests.Memory
	res.ResourceConstraints.Resources.LimitCpu = baseResources.Limits.CPU
	res.ResourceConstraints.Resources.LimitMemory = baseResources.Limits.Memory

	// sidecar注入
	annotationsMap := make(map[string]string)
	for k, v := range config.Global.Annotations {
		annotationsMap[k] = handleInterfaceToString(v)
	}
	for k, v := range config.Deploy.Annotations {
		annotationsMap[k] = handleInterfaceToString(v)
	}
	var reqCpu, reqMem1, limCpu, limMem1 float64
	var has bool
	for key, _ := range annotationsMap {
		if key == "sidecar.istio.io/proxyCPU" || key == "sidecar.istio.io/proxyMemory" ||
			key == "sidecar.istio.io/proxyCPULimit" || key == "sidecar.istio.io/proxyMemoryLimit" {
			has = true
		}
	}
	//cpu
	if value, ok := annotationsMap["sidecar.istio.io/proxyCPU"]; ok {
		if strings.Contains(value, "m") {
			cpu, _ := strconv.ParseFloat(value[0:len(value)-1], 32)
			reqCpu = cpu / 1000
		} else {
			reqCpu, _ = strconv.ParseFloat(value, 32)
		}
	} else if has {
		reqCpu = 0.1
	}
	//memory
	if value, ok := annotationsMap["sidecar.istio.io/proxyMemory"]; ok {
		reqMem1, _ = strconv.ParseFloat(value[0:len(value)-2], 32)
		if strings.Contains(value, "Gi") {
			reqMem1 = reqMem1 * 1024
		}
	} else if has {
		reqMem1 = 256
	}
	//cpuLimit
	if value, ok := annotationsMap["sidecar.istio.io/proxyCPULimit"]; ok {
		if strings.Contains(value, "m") {
			cpu, _ := strconv.ParseFloat(value[0:len(value)-1], 32)
			limCpu = cpu / 1000
		} else {
			limCpu, _ = strconv.ParseFloat(value, 32)
		}
	} else if has {
		limCpu = 1
	}
	//memoryLimit
	if value, ok := annotationsMap["sidecar.istio.io/proxyMemoryLimit"]; ok {
		limMem1, _ = strconv.ParseFloat(value[0:len(value)-2], 32)
		if strings.Contains(value, "Gi") {
			limMem1 = limMem1 * 1024
		}
	} else if has {
		limMem1 = 1024
	}
	if has {
		res.ResourceConstraints.Sidecar = &deploy.ResourceConstraints_Sidecar{}
		res.ResourceConstraints.Sidecar.Enabled = true
		res.ResourceConstraints.Sidecar.RequestCpu = float32(reqCpu)
		res.ResourceConstraints.Sidecar.RequestMemory = float32(reqMem1)
		res.ResourceConstraints.Sidecar.LimitCpu = float32(limCpu)
		res.ResourceConstraints.Sidecar.LimitMemory = float32(limMem1)
	}

	// 伸缩配置
	res.ScalingConfig = &deploy.ScalingConfig{
		Hpa:      &deploy.ScalingConfig_HPA{Types: []string{}},
		MultiHpa: &deploy.ScalingConfig_MultiHPA{Types: []string{}, Cron: []*deploy.ScalingConfig_MultiHPA_Cron{}},
	}
	replicas, _ := strconv.Atoi(config.Replicas)
	if replicas > 0 {
		res.ScalingConfig.Replicas = int32(replicas)
	} else {
		res.ScalingConfig.Replicas = 1
	}
	res.ScalingConfig.Type = "replicas"
	if value, isExist := hpaMap[unitKey]; isExist && (value.Autoscale.CPU.Enabled || value.Autoscale.Memory.Enabled) {
		res.ScalingConfig.MultiHpa = &deploy.ScalingConfig_MultiHPA{
			Min:   value.Autoscale.MinReplicaCount,
			Max:   value.Autoscale.MaxReplicaCount,
			Types: make([]string, 0),
		}
		if value.Autoscale.CPU.Enabled {
			res.ScalingConfig.MultiHpa.CpuUtilization = value.Autoscale.CPU.Value
			res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "cpu")
		}
		if value.Autoscale.Memory.Enabled {
			res.ScalingConfig.MultiHpa.MemoryUtilization = value.Autoscale.Memory.Value
			res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "memory")
		}
		res.ScalingConfig.Type = "multiHPA"
		if value.Autoscale.Cron.Enabled {
			for _, cron := range value.Autoscale.CronList {
				res.ScalingConfig.MultiHpa.Cron = append(res.ScalingConfig.MultiHpa.Cron, &deploy.ScalingConfig_MultiHPA_Cron{
					Timezone:        cron.Timezone,
					Start:           cron.Start,
					End:             cron.End,
					DesiredReplicas: cron.DesiredReplicas,
				})
			}
		}
	}
	// 监控指标
	if value, ok := annotationsMap["telemetry.mesh.quwan.io/customMetricsScrape"]; ok && value == "true" {
		res.MonitorMetrics = &deploy.MonitorMetrics{}
		var (
			portStr       string
			metricsPath   string
			containerName string
		)
		for k, v := range annotationsMap {
			if k == "telemetry.mesh.quwan.io/customMetricsPath" {
				metricsPath = v
			}
			if k == "telemetry.mesh.quwan.io/customMetricsPort" {
				portStr = v
			}
			if k == "telemetry.mesh.quwan.io/customMetricsContainer" {
				containerName = v
			}
		}
		metricsPort, _ := strconv.Atoi(portStr)
		if metricsPort != 0 && metricsPath != "" {
			res.MonitorMetrics = &deploy.MonitorMetrics{
				Enabled:       true,
				Port:          int32(metricsPort),
				MetricsPath:   metricsPath,
				ContainerName: containerName,
			}
		}
	}

	// 升级策略
	res.UpgradeStrategy = &deploy.UpgradeStrategy{
		Enabled:        false,
		MaxSurge:       25,
		MaxUnavailable: 25,
	}

	// 高级配置-advancedConfig
	res.AdvancedConfig = &deploy.TraitAdvancedConfig{
		SchedulingStrategy: []*deploy.SchedulingStrategy{},
		RateLimiting:       &deploy.RateLimiting{},
		CircuitBreaker:     &deploy.CircuitBreaker{},
		InitContainers:     []*deploy.InitContainer{},
		MultiContainers:    []*deploy.MultiContainer{},
		CustomScheduler:    &deploy.CustomScheduler{},
		ServiceMonitor:     &deploy.ServiceMonitor{},
	}

	// 亲和性
	if config.TempNamespace.Affinity.Enabled && len(config.Affinity.PodAntiAffinity.PreferredDuringSchedulingIgnoredDuringExecution) > 0 {
		antiAffinity := config.Affinity.PodAntiAffinity.PreferredDuringSchedulingIgnoredDuringExecution[0]
		rules := []*deploy.SchedulingStrategy_Rule{
			{
				Key:      antiAffinity.PodAffinityTerm.LabelSelector.MatchExpressions[0].Key,
				Operator: "In",
				Value:    antiAffinity.PodAffinityTerm.LabelSelector.MatchExpressions[0].Values[0],
			},
		}
		res.AdvancedConfig.SchedulingStrategy = append(res.AdvancedConfig.SchedulingStrategy, &deploy.SchedulingStrategy{
			Type:         "podAntiAffinity",
			Priority:     "Preferred",
			Topology_Key: antiAffinity.PodAffinityTerm.TopologyKey,
			Weight:       int32(antiAffinity.Weight),
			Rules:        rules,
		})
	}

	// initContainers
	// 默认日志container
	res.AdvancedConfig.InitContainers = append(res.AdvancedConfig.InitContainers, &deploy.InitContainer{
		ImageName:         fmt.Sprintf("%v:%v", config.Log.Image.Repo, config.Log.Image.Tag),
		ContainerName:     "log-create-shm",
		Commands:          []string{"/log-side-car", "create"},
		InitMountPath:     "",
		AppMountPath:      "",
		Envs:              []*deploy.Pair{},
		VolumeMounts:      []*deploy.VolumeMount{},
		MountName:         "",
		ResourceFieldEnvs: []*deploy.ResourceFieldEnv{},
	})

	// multiContainers
	// 第一个container 可观测
	container1 := initMultiContainer()
	if config.Discoveryagent.Enable {
		// discoveryagent
		container1.ContainerName = "discovery-agent"
		container1.Commands = []string{"/home/<USER>/sbin/discoveryagent", "-i", "/home/<USER>/etc/discoveryagent/discoveryagent.json", "-logDir", "/home/<USER>/log"}
		container1.HealthCheck.Types = append(container1.HealthCheck.Types, "readinessProbe")
		container1.HealthCheck.ReadinessProbe.Type = "tcpSocket"
		container1.HealthCheck.ReadinessProbe.TcpSocket = &deploy.TCPSocketAction{Port: 18080}
		container1.HealthCheck.ReadinessProbe.Probe = &deploy.HealthCheck_Probe{
			InitialDelaySeconds: 5,
			PeriodSeconds:       10,
			TimeoutSeconds:      1,
			SuccessThreshold:    1,
			FailureThreshold:    3,
		}
	} else {
		// observer
		container1.ContainerName = "observer"
		container1.Commands = []string{"sleep", "infinity"}
	}
	container1.ImageName = fmt.Sprintf("%v/%v:%v", config.Global.Hub, config.Deploy.Image.Name, config.Deploy.Image.Tag)
	container1.Envs = append(container1.Envs, &deploy.Pair{Key: "GOMAXPROCS", Value: "1"})
	for _, env := range config.Discoveryagent.Env {
		container1.Envs = append(container1.Envs, &deploy.Pair{Key: env.Name, Value: env.Value})
	}
	multiResources := handleResource(config.Discoveryagent.Resources)
	container1.Resources.RequestCpu = multiResources.Requests.CPU
	container1.Resources.RequestMemory = multiResources.Requests.Memory
	container1.Resources.LimitCpu = multiResources.Limits.CPU
	container1.Resources.LimitMemory = multiResources.Limits.Memory
	container1.VolumeMounts = append(container1.VolumeMounts, &deploy.VolumeMount{
		VolumeName: "discovery-runtime",
		MountPoint: "/home/<USER>/run/",
	})
	container1.VolumeMounts = append(container1.VolumeMounts, &deploy.VolumeMount{
		VolumeName: "discovery-config",
		MountPoint: "/home/<USER>/etc/discoveryagent",
	})
	container1.VolumeMounts = append(container1.VolumeMounts, &deploy.VolumeMount{
		VolumeName: "host-time",
		MountPoint: "/etc/localtime",
	})
	container1.VolumeMounts = append(container1.VolumeMounts, &deploy.VolumeMount{
		VolumeName: "home-godman-corefile",
		MountPoint: "/home/<USER>/corefile",
	})
	for _, volume := range config.Discoveryagent.VolumeMounts {
		if volume.Name == "" || volume.MountPath == "" {
			continue
		}
		container1.VolumeMounts = append(container1.VolumeMounts, &deploy.VolumeMount{
			VolumeName: volume.Name,
			MountPoint: volume.MountPath,
		})
	}
	res.AdvancedConfig.MultiContainers = append(res.AdvancedConfig.MultiContainers, &container1)
	// 第二个container 日志
	if config.Log.Enabled && config.TempNamespace.Log.Image.Enabled && config.TempNamespace.Log.Resources.Enabled {
		container2 := initMultiContainer()
		container2.ContainerName = "log"
		container2.ImageName = fmt.Sprintf("%v:%v", config.Log.Image.Repo, config.Log.Image.Tag)
		container2.Commands = []string{"/log-side-car", "reader"}
		multiResources = handleResource(config.Log.Resources)
		container2.Resources.RequestCpu = multiResources.Requests.CPU
		container2.Resources.RequestMemory = multiResources.Requests.Memory
		container2.Resources.LimitCpu = multiResources.Limits.CPU
		container2.Resources.LimitMemory = multiResources.Limits.Memory
		container2.VolumeMounts = append(container2.VolumeMounts, &deploy.VolumeMount{
			VolumeName: "host-time",
			MountPoint: "/etc/localtime",
		})
		res.AdvancedConfig.MultiContainers = append(res.AdvancedConfig.MultiContainers, &container2)
	}
	// 第三个container 异步队列
	if config.AsyncQueue.Enabled {
		log.Infof("asyncQueue: %+v", config.AsyncQueue)
		container3 := initMultiContainer()
		container3.ContainerName = "async-queue"
		container3.ImageName = fmt.Sprintf("%v/%v:%v", config.Global.Hub, config.AsyncQueue.Scheduler.Image.Name, config.AsyncQueue.Scheduler.Image.Tag)
		for _, c := range config.AsyncQueue.Scheduler.Command {
			container3.Commands = append(container3.Commands, c)
		}
		for _, env := range config.Global.Env {
			if env.Name == "" || env.Value == "" {
				continue
			}
			container3.Envs = append(container3.Envs, &deploy.Pair{Key: env.Name, Value: env.Value})
		}
		multiResources = handleResource(config.AsyncQueue.Scheduler.Resources)
		container3.Resources.RequestCpu = multiResources.Requests.CPU
		container3.Resources.RequestMemory = multiResources.Requests.Memory
		container3.Resources.LimitCpu = multiResources.Limits.CPU
		container3.Resources.LimitMemory = multiResources.Limits.Memory

		//if len(config.ConfigFiles) > 0 {
		//	container3.VolumeMounts = append(container3.VolumeMounts, &deploy.VolumeMount{
		//		VolumeName: "config",
		//		MountPoint: "/home/<USER>/etc/server/",
		//	})
		//}
		volumeMountsMap := make(map[string]VolumeMounts)
		volumeMountsList := make([]VolumeMounts, 0)
		// global 挂载文件
		for _, volume := range config.Global.VolumeMounts {
			if _, ok := volumeMountsMap[volume.Name]; !ok {
				volumeMountsMap[volume.Name] = volume
				volumeMountsList = append(volumeMountsList, volume)
			}
		}
		// 挂载配置文件
		if config.TempNamespace.VolumeMounts.Enabled {
			for _, volume := range config.VolumeMounts {
				if _, ok := volumeMountsMap[volume.Name]; !ok {
					volumeMountsMap[volume.Name] = volume
					volumeMountsList = append(volumeMountsList, volume)
				}
			}
		}
		// 挂载目录
		for _, volume := range volumeMountsList {
			if volume.Name != "" {
				container3.VolumeMounts = append(container3.VolumeMounts, &deploy.VolumeMount{
					VolumeName: volume.Name,
					MountPoint: volume.MountPath,
					SubPath:    volume.SubPath,
				})
			}
		}
	}

	// serviceMonitor
	if config.Service.StartReportMetrics {
		res.AdvancedConfig.ServiceMonitor.Enabled = true
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func initMultiContainer() deploy.MultiContainer {
	return deploy.MultiContainer{
		ImageName:     "",
		ContainerName: "",
		Commands:      []string{},
		Resources:     &deploy.Resources{},
		Envs:          []*deploy.Pair{},
		VolumeMounts:  []*deploy.VolumeMount{},
		HealthCheck: &deploy.HealthCheck{
			ReadinessProbe: builderHandler(),
			LivenessProbe:  builderHandler(),
			StartupProbe:   builderHandler(),
			Types:          []string{},
		},
		GpuResources:      &deploy.GPUResource{},
		ResourceFieldEnvs: []*deploy.ResourceFieldEnv{},
	}
}

func getClusterName(name string) string {
	switch name {
	case "火山云集群k8s-hs-bj-1-test":
		return "k8s-hs-bj-1-test"
	case "华为生产集群(tt)":
		return "k8s-hw-bj-tt-prod-00"
	default:
		return name
	}
}

func getEnvEnum(k8sName string) int8 {
	switch k8sName {
	case "火山云集群k8s-hs-bj-1-test":
		return int8(2)
	case "k8s-hw-bj-1-stage":
		return int8(3)
	default:
		return 4
	}
}

func getTemplateId(env int64, targetProjectName string) int64 {
	if targetProjectName == "TT-业务平台" {
		switch env {
		case 2:
			return 0
		case 3:
			return 0
		case 4:
			return 0
		default:
			return 0
		}
	} else if targetProjectName == "TT-营收" {
		switch env {
		case 2:
			return 0
		case 3:
			return 0
		case 4:
			return 0
		default:
			return 0
		}
	} else {
		return 0
	}
}

func isContainUnit(unit string, keys []string) bool {
	var myMap map[string]bool
	myMap = make(map[string]bool)
	for _, key := range keys {
		myMap[key] = true
	}
	if _, ok := myMap[unit]; ok {
		return true
	}
	return false
}
