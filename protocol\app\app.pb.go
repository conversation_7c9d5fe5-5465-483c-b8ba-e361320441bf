// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.4
// source: protocol/app/app.proto

package app

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type APP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Owners      []int64 `protobuf:"varint,3,rep,packed,name=owners,proto3" json:"owners,omitempty"`
	BuildPath   string  `protobuf:"bytes,4,opt,name=buildPath,proto3" json:"buildPath,omitempty"`
	RepoAddr    string  `protobuf:"bytes,5,opt,name=repoAddr,proto3" json:"repoAddr,omitempty"`
	LangName    string  `protobuf:"bytes,6,opt,name=langName,proto3" json:"langName,omitempty"`
	LangVersion string  `protobuf:"bytes,7,opt,name=langVersion,proto3" json:"langVersion,omitempty"`
	ProjectID   int64   `protobuf:"varint,8,opt,name=projectID,proto3" json:"projectID,omitempty"`
	Description string  `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`
	CmdbId      string  `protobuf:"bytes,10,opt,name=cmdbId,proto3" json:"cmdbId,omitempty"`
	ProjectName string  `protobuf:"bytes,11,opt,name=ProjectName,proto3" json:"ProjectName,omitempty"`
	SenvStatus  string  `protobuf:"bytes,12,opt,name=senvStatus,proto3" json:"senvStatus,omitempty"`
}

func (x *APP) Reset() {
	*x = APP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *APP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APP) ProtoMessage() {}

func (x *APP) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APP.ProtoReflect.Descriptor instead.
func (*APP) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{0}
}

func (x *APP) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *APP) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *APP) GetOwners() []int64 {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *APP) GetBuildPath() string {
	if x != nil {
		return x.BuildPath
	}
	return ""
}

func (x *APP) GetRepoAddr() string {
	if x != nil {
		return x.RepoAddr
	}
	return ""
}

func (x *APP) GetLangName() string {
	if x != nil {
		return x.LangName
	}
	return ""
}

func (x *APP) GetLangVersion() string {
	if x != nil {
		return x.LangVersion
	}
	return ""
}

func (x *APP) GetProjectID() int64 {
	if x != nil {
		return x.ProjectID
	}
	return 0
}

func (x *APP) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *APP) GetCmdbId() string {
	if x != nil {
		return x.CmdbId
	}
	return ""
}

func (x *APP) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

func (x *APP) GetSenvStatus() string {
	if x != nil {
		return x.SenvStatus
	}
	return ""
}

type AppParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BranchSearch string `protobuf:"bytes,2,opt,name=branchSearch,proto3" json:"branchSearch,omitempty"`
	Regex        string `protobuf:"bytes,3,opt,name=regex,proto3" json:"regex,omitempty"`
}

func (x *AppParam) Reset() {
	*x = AppParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppParam) ProtoMessage() {}

func (x *AppParam) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppParam.ProtoReflect.Descriptor instead.
func (*AppParam) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{1}
}

func (x *AppParam) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppParam) GetBranchSearch() string {
	if x != nil {
		return x.BranchSearch
	}
	return ""
}

func (x *AppParam) GetRegex() string {
	if x != nil {
		return x.Regex
	}
	return ""
}

type SearchAppParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId int64  `protobuf:"varint,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SearchAppParam) Reset() {
	*x = SearchAppParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAppParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAppParam) ProtoMessage() {}

func (x *SearchAppParam) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAppParam.ProtoReflect.Descriptor instead.
func (*SearchAppParam) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{2}
}

func (x *SearchAppParam) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *SearchAppParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type AppBranchList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BranchList []string `protobuf:"bytes,1,rep,name=branchList,proto3" json:"branchList,omitempty"`
}

func (x *AppBranchList) Reset() {
	*x = AppBranchList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppBranchList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppBranchList) ProtoMessage() {}

func (x *AppBranchList) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppBranchList.ProtoReflect.Descriptor instead.
func (*AppBranchList) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{3}
}

func (x *AppBranchList) GetBranchList() []string {
	if x != nil {
		return x.BranchList
	}
	return nil
}

type AppsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        []int64 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"`
	ProjectId int64   `protobuf:"varint,2,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
}

func (x *AppsReq) Reset() {
	*x = AppsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppsReq) ProtoMessage() {}

func (x *AppsReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppsReq.ProtoReflect.Descriptor instead.
func (*AppsReq) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{4}
}

func (x *AppsReq) GetId() []int64 {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *AppsReq) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type AppList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Apps []*APP `protobuf:"bytes,1,rep,name=apps,proto3" json:"apps,omitempty"`
}

func (x *AppList) Reset() {
	*x = AppList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppList) ProtoMessage() {}

func (x *AppList) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppList.ProtoReflect.Descriptor instead.
func (*AppList) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{5}
}

func (x *AppList) GetApps() []*APP {
	if x != nil {
		return x.Apps
	}
	return nil
}

type GetDeployMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	EnvType string `protobuf:"bytes,2,opt,name=env_type,json=envType,proto3" json:"env_type,omitempty"`
}

func (x *GetDeployMsgReq) Reset() {
	*x = GetDeployMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeployMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeployMsgReq) ProtoMessage() {}

func (x *GetDeployMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeployMsgReq.ProtoReflect.Descriptor instead.
func (*GetDeployMsgReq) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{6}
}

func (x *GetDeployMsgReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetDeployMsgReq) GetEnvType() string {
	if x != nil {
		return x.EnvType
	}
	return ""
}

type GetDeployMsgResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level         string            `protobuf:"bytes,1,opt,name=level,proto3" json:"level,omitempty"`
	MatchLabels   map[string]string `protobuf:"bytes,2,rep,name=matchLabels,proto3" json:"matchLabels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ServiceLabels map[string]string `protobuf:"bytes,3,rep,name=serviceLabels,proto3" json:"serviceLabels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// @gotags: json:"projectId"
	ProjectId int64 `protobuf:"varint,4,opt,name=project_id,json=projectId,proto3" json:"projectId"`
	// cmdb id
	CmdbId string `protobuf:"bytes,5,opt,name=cmdb_id,json=cmdbId,proto3" json:"cmdb_id,omitempty"`
	// 开发语言
	LangName string `protobuf:"bytes,6,opt,name=lang_name,json=langName,proto3" json:"lang_name,omitempty"`
	// 应用名称
	AppName string `protobuf:"bytes,7,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	// 标准化Labels 环境配置
	StandLabelEnvs string `protobuf:"bytes,8,opt,name=stand_label_envs,json=standLabelEnvs,proto3" json:"stand_label_envs,omitempty"`
	// 关联的动态配置
	DynamicConfigs []*DynamicConfig `protobuf:"bytes,9,rep,name=dynamic_configs,json=dynamicConfigs,proto3" json:"dynamic_configs,omitempty"`
}

func (x *GetDeployMsgResp) Reset() {
	*x = GetDeployMsgResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeployMsgResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeployMsgResp) ProtoMessage() {}

func (x *GetDeployMsgResp) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeployMsgResp.ProtoReflect.Descriptor instead.
func (*GetDeployMsgResp) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{7}
}

func (x *GetDeployMsgResp) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *GetDeployMsgResp) GetMatchLabels() map[string]string {
	if x != nil {
		return x.MatchLabels
	}
	return nil
}

func (x *GetDeployMsgResp) GetServiceLabels() map[string]string {
	if x != nil {
		return x.ServiceLabels
	}
	return nil
}

func (x *GetDeployMsgResp) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *GetDeployMsgResp) GetCmdbId() string {
	if x != nil {
		return x.CmdbId
	}
	return ""
}

func (x *GetDeployMsgResp) GetLangName() string {
	if x != nil {
		return x.LangName
	}
	return ""
}

func (x *GetDeployMsgResp) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *GetDeployMsgResp) GetStandLabelEnvs() string {
	if x != nil {
		return x.StandLabelEnvs
	}
	return ""
}

func (x *GetDeployMsgResp) GetDynamicConfigs() []*DynamicConfig {
	if x != nil {
		return x.DynamicConfigs
	}
	return nil
}

type DynamicConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FileName string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	ApolloNs string `protobuf:"bytes,3,opt,name=apollo_ns,json=apolloNs,proto3" json:"apollo_ns,omitempty"`
	EnvType  string `protobuf:"bytes,4,opt,name=env_type,json=envType,proto3" json:"env_type,omitempty"`
	IsGlobal bool   `protobuf:"varint,5,opt,name=is_global,json=isGlobal,proto3" json:"is_global,omitempty"`
}

func (x *DynamicConfig) Reset() {
	*x = DynamicConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicConfig) ProtoMessage() {}

func (x *DynamicConfig) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicConfig.ProtoReflect.Descriptor instead.
func (*DynamicConfig) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{8}
}

func (x *DynamicConfig) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DynamicConfig) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *DynamicConfig) GetApolloNs() string {
	if x != nil {
		return x.ApolloNs
	}
	return ""
}

func (x *DynamicConfig) GetEnvType() string {
	if x != nil {
		return x.EnvType
	}
	return ""
}

func (x *DynamicConfig) GetIsGlobal() bool {
	if x != nil {
		return x.IsGlobal
	}
	return false
}

type GetAppInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId int64 `protobuf:"varint,1,opt,name=app_id,json=appID,proto3" json:"app_id,omitempty"`
}

func (x *GetAppInfoReq) Reset() {
	*x = GetAppInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppInfoReq) ProtoMessage() {}

func (x *GetAppInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppInfoReq.ProtoReflect.Descriptor instead.
func (*GetAppInfoReq) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{9}
}

func (x *GetAppInfoReq) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

type GetAppInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	App     *APP                        `protobuf:"bytes,1,opt,name=app,proto3" json:"app,omitempty"`
	Project *GetAppInfoResp_ProjectInfo `protobuf:"bytes,2,opt,name=Project,proto3" json:"Project,omitempty"`
}

func (x *GetAppInfoResp) Reset() {
	*x = GetAppInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppInfoResp) ProtoMessage() {}

func (x *GetAppInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppInfoResp.ProtoReflect.Descriptor instead.
func (*GetAppInfoResp) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{10}
}

func (x *GetAppInfoResp) GetApp() *APP {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *GetAppInfoResp) GetProject() *GetAppInfoResp_ProjectInfo {
	if x != nil {
		return x.Project
	}
	return nil
}

type GetUserAppsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     int64   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ProjectIds []int64 `protobuf:"varint,2,rep,packed,name=project_ids,json=projectIds,proto3" json:"project_ids,omitempty"`
	AppName    string  `protobuf:"bytes,3,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
}

func (x *GetUserAppsReq) Reset() {
	*x = GetUserAppsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAppsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAppsReq) ProtoMessage() {}

func (x *GetUserAppsReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAppsReq.ProtoReflect.Descriptor instead.
func (*GetUserAppsReq) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{11}
}

func (x *GetUserAppsReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetUserAppsReq) GetProjectIds() []int64 {
	if x != nil {
		return x.ProjectIds
	}
	return nil
}

func (x *GetUserAppsReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

type CreateOrUpdateAppEventlinkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId        int64  `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppName      string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	ProjectId    int64  `protobuf:"varint,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Env          string `protobuf:"bytes,4,opt,name=env,proto3" json:"env,omitempty"`
	ConsumerType string `protobuf:"bytes,5,opt,name=consumer_type,json=consumerType,proto3" json:"consumer_type,omitempty"`
	ProducerType string `protobuf:"bytes,6,opt,name=producer_type,json=producerType,proto3" json:"producer_type,omitempty"`
}

func (x *CreateOrUpdateAppEventlinkReq) Reset() {
	*x = CreateOrUpdateAppEventlinkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateAppEventlinkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateAppEventlinkReq) ProtoMessage() {}

func (x *CreateOrUpdateAppEventlinkReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateAppEventlinkReq.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateAppEventlinkReq) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{12}
}

func (x *CreateOrUpdateAppEventlinkReq) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *CreateOrUpdateAppEventlinkReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *CreateOrUpdateAppEventlinkReq) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *CreateOrUpdateAppEventlinkReq) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *CreateOrUpdateAppEventlinkReq) GetConsumerType() string {
	if x != nil {
		return x.ConsumerType
	}
	return ""
}

func (x *CreateOrUpdateAppEventlinkReq) GetProducerType() string {
	if x != nil {
		return x.ProducerType
	}
	return ""
}

type GetAppEventlinkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId int64  `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Env   string `protobuf:"bytes,2,opt,name=env,proto3" json:"env,omitempty"`
}

func (x *GetAppEventlinkReq) Reset() {
	*x = GetAppEventlinkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppEventlinkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppEventlinkReq) ProtoMessage() {}

func (x *GetAppEventlinkReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppEventlinkReq.ProtoReflect.Descriptor instead.
func (*GetAppEventlinkReq) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{13}
}

func (x *GetAppEventlinkReq) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *GetAppEventlinkReq) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

type GetAppEventlinkResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId        int64  `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppName      string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	ProjectId    int64  `protobuf:"varint,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Env          string `protobuf:"bytes,4,opt,name=env,proto3" json:"env,omitempty"`
	ConsumerType string `protobuf:"bytes,5,opt,name=consumer_type,json=consumerType,proto3" json:"consumer_type,omitempty"`
	ProducerType string `protobuf:"bytes,6,opt,name=producer_type,json=producerType,proto3" json:"producer_type,omitempty"`
}

func (x *GetAppEventlinkResp) Reset() {
	*x = GetAppEventlinkResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppEventlinkResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppEventlinkResp) ProtoMessage() {}

func (x *GetAppEventlinkResp) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppEventlinkResp.ProtoReflect.Descriptor instead.
func (*GetAppEventlinkResp) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{14}
}

func (x *GetAppEventlinkResp) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *GetAppEventlinkResp) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *GetAppEventlinkResp) GetProjectId() int64 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

func (x *GetAppEventlinkResp) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *GetAppEventlinkResp) GetConsumerType() string {
	if x != nil {
		return x.ConsumerType
	}
	return ""
}

func (x *GetAppEventlinkResp) GetProducerType() string {
	if x != nil {
		return x.ProducerType
	}
	return ""
}

type AppByNameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ProjectName string `protobuf:"bytes,2,opt,name=projectName,proto3" json:"projectName,omitempty"`
}

func (x *AppByNameReq) Reset() {
	*x = AppByNameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppByNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppByNameReq) ProtoMessage() {}

func (x *AppByNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppByNameReq.ProtoReflect.Descriptor instead.
func (*AppByNameReq) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{15}
}

func (x *AppByNameReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AppByNameReq) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

type GetAppInfoResp_ProjectInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type                     string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Description              string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Identity                 string `protobuf:"bytes,5,opt,name=identity,proto3" json:"identity,omitempty"`
	CanaryDeployNotifyStatus string `protobuf:"bytes,6,opt,name=canaryDeployNotifyStatus,proto3" json:"canaryDeployNotifyStatus,omitempty"`
}

func (x *GetAppInfoResp_ProjectInfo) Reset() {
	*x = GetAppInfoResp_ProjectInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_app_app_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppInfoResp_ProjectInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppInfoResp_ProjectInfo) ProtoMessage() {}

func (x *GetAppInfoResp_ProjectInfo) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_app_app_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppInfoResp_ProjectInfo.ProtoReflect.Descriptor instead.
func (*GetAppInfoResp_ProjectInfo) Descriptor() ([]byte, []int) {
	return file_protocol_app_app_proto_rawDescGZIP(), []int{10, 0}
}

func (x *GetAppInfoResp_ProjectInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAppInfoResp_ProjectInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetAppInfoResp_ProjectInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetAppInfoResp_ProjectInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GetAppInfoResp_ProjectInfo) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *GetAppInfoResp_ProjectInfo) GetCanaryDeployNotifyStatus() string {
	if x != nil {
		return x.CanaryDeployNotifyStatus
	}
	return ""
}

var File_protocol_app_app_proto protoreflect.FileDescriptor

var file_protocol_app_app_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x61,
	0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x61, 0x70, 0x70, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd3, 0x02, 0x0a, 0x03, 0x41,
	0x50, 0x50, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x1c,
	0x0a, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08,
	0x72, 0x65, 0x70, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x70, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x61, 0x6e, 0x67, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6d, 0x64, 0x62, 0x49, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6d, 0x64, 0x62, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x6e, 0x76, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x76, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x54, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c,
	0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x67, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x72, 0x65, 0x67, 0x65, 0x78, 0x22, 0x43, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x41, 0x70, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2f, 0x0a, 0x0d, 0x41,
	0x70, 0x70, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x38, 0x0a, 0x07,
	0x41, 0x70, 0x70, 0x73, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x27, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1c, 0x0a, 0x04, 0x61, 0x70, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x08, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x50, 0x50, 0x52, 0x04, 0x61, 0x70, 0x70, 0x73, 0x22,
	0x3c, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73, 0x67, 0x52,
	0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x76, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x22, 0x9b, 0x04,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x48, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x4e, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x70, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x6d, 0x64, 0x62, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61,
	0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x5f, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x5f, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74,
	0x61, 0x6e, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x6e, 0x76, 0x73, 0x12, 0x3b, 0x0a, 0x0f,
	0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x44, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x91, 0x01, 0x0a, 0x0d,
	0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x70,
	0x6f, 0x6c, 0x6c, 0x6f, 0x5f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61,
	0x70, 0x6f, 0x6c, 0x6c, 0x6f, 0x4e, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x76, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x76, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x22,
	0x26, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x22, 0xa9, 0x02, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41,
	0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x03, 0x61, 0x70,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x50,
	0x50, 0x52, 0x03, 0x61, 0x70, 0x70, 0x12, 0x39, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x1a, 0xbf, 0x01, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x3a, 0x0a, 0x18, 0x63, 0x61, 0x6e, 0x61, 0x72,
	0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x63, 0x61, 0x6e, 0x61, 0x72,
	0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x65, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x70,
	0x70, 0x73, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xcc, 0x01, 0x0a, 0x1d, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x65, 0x6e, 0x76, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x3d, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12,
	0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x22, 0xc2, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x65, 0x6e, 0x76, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x44, 0x0a,
	0x0c, 0x41, 0x70, 0x70, 0x42, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x32, 0x85, 0x05, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x12, 0x0d, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x08, 0x2e, 0x61, 0x70,
	0x70, 0x2e, 0x41, 0x50, 0x50, 0x12, 0x35, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x0d, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a,
	0x12, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12, 0x0c, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x70, 0x70,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x0c, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x3e, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x4d, 0x73, 0x67, 0x12, 0x14, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x61, 0x70,
	0x70, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x30, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x70, 0x70,
	0x73, 0x12, 0x13, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x70, 0x70, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x0c, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x70, 0x70,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x70, 0x70, 0x73, 0x12, 0x13, 0x2e,
	0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x70, 0x70, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x0c, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x3c, 0x0a, 0x1b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x70, 0x70, 0x42, 0x79, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x13, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x70, 0x70, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x1a, 0x08, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x50, 0x50, 0x12, 0x58,
	0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x70, 0x70, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x22, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x44, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41,
	0x70, 0x70, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x17, 0x2e, 0x61, 0x70,
	0x70, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x6c, 0x69, 0x6e,
	0x6b, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2b,
	0x0a, 0x0c, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x42, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x11,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x42, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x08, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x41, 0x50, 0x50, 0x42, 0x0e, 0x5a, 0x0c, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x61, 0x70, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_protocol_app_app_proto_rawDescOnce sync.Once
	file_protocol_app_app_proto_rawDescData = file_protocol_app_app_proto_rawDesc
)

func file_protocol_app_app_proto_rawDescGZIP() []byte {
	file_protocol_app_app_proto_rawDescOnce.Do(func() {
		file_protocol_app_app_proto_rawDescData = protoimpl.X.CompressGZIP(file_protocol_app_app_proto_rawDescData)
	})
	return file_protocol_app_app_proto_rawDescData
}

var file_protocol_app_app_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_protocol_app_app_proto_goTypes = []any{
	(*APP)(nil),                           // 0: app.APP
	(*AppParam)(nil),                      // 1: app.AppParam
	(*SearchAppParam)(nil),                // 2: app.SearchAppParam
	(*AppBranchList)(nil),                 // 3: app.AppBranchList
	(*AppsReq)(nil),                       // 4: app.AppsReq
	(*AppList)(nil),                       // 5: app.AppList
	(*GetDeployMsgReq)(nil),               // 6: app.GetDeployMsgReq
	(*GetDeployMsgResp)(nil),              // 7: app.GetDeployMsgResp
	(*DynamicConfig)(nil),                 // 8: app.DynamicConfig
	(*GetAppInfoReq)(nil),                 // 9: app.GetAppInfoReq
	(*GetAppInfoResp)(nil),                // 10: app.GetAppInfoResp
	(*GetUserAppsReq)(nil),                // 11: app.GetUserAppsReq
	(*CreateOrUpdateAppEventlinkReq)(nil), // 12: app.CreateOrUpdateAppEventlinkReq
	(*GetAppEventlinkReq)(nil),            // 13: app.GetAppEventlinkReq
	(*GetAppEventlinkResp)(nil),           // 14: app.GetAppEventlinkResp
	(*AppByNameReq)(nil),                  // 15: app.AppByNameReq
	nil,                                   // 16: app.GetDeployMsgResp.MatchLabelsEntry
	nil,                                   // 17: app.GetDeployMsgResp.ServiceLabelsEntry
	(*GetAppInfoResp_ProjectInfo)(nil),    // 18: app.GetAppInfoResp.ProjectInfo
	(*emptypb.Empty)(nil),                 // 19: google.protobuf.Empty
}
var file_protocol_app_app_proto_depIdxs = []int32{
	0,  // 0: app.AppList.apps:type_name -> app.APP
	16, // 1: app.GetDeployMsgResp.matchLabels:type_name -> app.GetDeployMsgResp.MatchLabelsEntry
	17, // 2: app.GetDeployMsgResp.serviceLabels:type_name -> app.GetDeployMsgResp.ServiceLabelsEntry
	8,  // 3: app.GetDeployMsgResp.dynamic_configs:type_name -> app.DynamicConfig
	0,  // 4: app.GetAppInfoResp.app:type_name -> app.APP
	18, // 5: app.GetAppInfoResp.Project:type_name -> app.GetAppInfoResp.ProjectInfo
	1,  // 6: app.AppService.GetApp:input_type -> app.AppParam
	9,  // 7: app.AppService.GetAppInfo:input_type -> app.GetAppInfoReq
	1,  // 8: app.AppService.GetAppBranchList:input_type -> app.AppParam
	4,  // 9: app.AppService.GetAppListByIds:input_type -> app.AppsReq
	6,  // 10: app.AppService.GetAppDeployMsg:input_type -> app.GetDeployMsgReq
	11, // 11: app.AppService.GetUserApps:input_type -> app.GetUserAppsReq
	11, // 12: app.AppService.GetUserPreferenceApps:input_type -> app.GetUserAppsReq
	2,  // 13: app.AppService.SearchAppByProjectIdAndName:input_type -> app.SearchAppParam
	12, // 14: app.AppService.CreateOrUpdateAppEventlink:input_type -> app.CreateOrUpdateAppEventlinkReq
	13, // 15: app.AppService.GetAppEventlink:input_type -> app.GetAppEventlinkReq
	15, // 16: app.AppService.GetAppByName:input_type -> app.AppByNameReq
	0,  // 17: app.AppService.GetApp:output_type -> app.APP
	10, // 18: app.AppService.GetAppInfo:output_type -> app.GetAppInfoResp
	3,  // 19: app.AppService.GetAppBranchList:output_type -> app.AppBranchList
	5,  // 20: app.AppService.GetAppListByIds:output_type -> app.AppList
	7,  // 21: app.AppService.GetAppDeployMsg:output_type -> app.GetDeployMsgResp
	5,  // 22: app.AppService.GetUserApps:output_type -> app.AppList
	5,  // 23: app.AppService.GetUserPreferenceApps:output_type -> app.AppList
	0,  // 24: app.AppService.SearchAppByProjectIdAndName:output_type -> app.APP
	19, // 25: app.AppService.CreateOrUpdateAppEventlink:output_type -> google.protobuf.Empty
	14, // 26: app.AppService.GetAppEventlink:output_type -> app.GetAppEventlinkResp
	0,  // 27: app.AppService.GetAppByName:output_type -> app.APP
	17, // [17:28] is the sub-list for method output_type
	6,  // [6:17] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_protocol_app_app_proto_init() }
func file_protocol_app_app_proto_init() {
	if File_protocol_app_app_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protocol_app_app_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*APP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*AppParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*SearchAppParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*AppBranchList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*AppsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*AppList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*GetDeployMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*GetDeployMsgResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*DynamicConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GetAppInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*GetAppInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*GetUserAppsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrUpdateAppEventlinkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*GetAppEventlinkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*GetAppEventlinkResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*AppByNameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_app_app_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*GetAppInfoResp_ProjectInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protocol_app_app_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protocol_app_app_proto_goTypes,
		DependencyIndexes: file_protocol_app_app_proto_depIdxs,
		MessageInfos:      file_protocol_app_app_proto_msgTypes,
	}.Build()
	File_protocol_app_app_proto = out.File
	file_protocol_app_app_proto_rawDesc = nil
	file_protocol_app_app_proto_goTypes = nil
	file_protocol_app_app_proto_depIdxs = nil
}
