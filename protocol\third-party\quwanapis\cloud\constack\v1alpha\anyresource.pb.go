// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.4
// source: quwan/cloud/constack/v1alpha/anyresource.proto

package constack

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GroupVersionResource (详情查看https://q9jvw0u5f5.feishu.cn/docx/doxcnIbvmyP4adDuQ6Ya2HV3o9i#doxcn2sWSaYgICi8QA0L0hbj7Ta)
type GroupVersionResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group, e.g.: apps
	Group string `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
	// version, e.g.: v1
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// resource, e.g.: deployments
	Resource string `protobuf:"bytes,3,opt,name=resource,proto3" json:"resource,omitempty"`
}

func (x *GroupVersionResource) Reset() {
	*x = GroupVersionResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupVersionResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupVersionResource) ProtoMessage() {}

func (x *GroupVersionResource) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupVersionResource.ProtoReflect.Descriptor instead.
func (*GroupVersionResource) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{0}
}

func (x *GroupVersionResource) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *GroupVersionResource) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *GroupVersionResource) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

// ListRequest
type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名, e.g.: k8s-tc-bj-1-yunwei
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间(非namespaced资源无需填写), e.g.: infra
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// e.g.: {"group":"apps","version":"v1","resource":"deployments"}
	GroupVersionResource *GroupVersionResource `protobuf:"bytes,3,opt,name=group_version_resource,json=groupVersionResource,proto3" json:"group_version_resource,omitempty"`
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{1}
}

func (x *ListRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListRequest) GetGroupVersionResource() *GroupVersionResource {
	if x != nil {
		return x.GroupVersionResource
	}
	return nil
}

// ListResponse
type ListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//	资源列表 , e.g.: [{
	//	   "apiVersion": "v1",
	//	   "kind": "Namespace",
	//	   "metadata": {
	//	       "creationTimestamp": "2022-11-22T06:41:21Z",
	//	       "name": "infra"
	//	   },
	//	   "spec": {
	//	       "finalizers": [
	//	           "kubernetes"
	//	       ]
	//	   },
	//	   "status": {
	//	       "phase": "Active"
	//	   }
	//	},{}]
	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListResponse) Reset() {
	*x = ListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResponse) ProtoMessage() {}

func (x *ListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResponse.ProtoReflect.Descriptor instead.
func (*ListResponse) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{2}
}

func (x *ListResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// GetRequest
type GetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名, e.g.: k8s-tc-bj-1-yunwei
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间(非namespaced资源无需填写), e.g.: infra
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 资源名, e.g.: cloud-enterprise
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// e.g.: {"group":"apps","version":"v1","resource":"deployments"}
	GroupVersionResource *GroupVersionResource `protobuf:"bytes,4,opt,name=group_version_resource,json=groupVersionResource,proto3" json:"group_version_resource,omitempty"`
}

func (x *GetRequest) Reset() {
	*x = GetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequest) ProtoMessage() {}

func (x *GetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequest.ProtoReflect.Descriptor instead.
func (*GetRequest) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{3}
}

func (x *GetRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *GetRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *GetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetRequest) GetGroupVersionResource() *GroupVersionResource {
	if x != nil {
		return x.GroupVersionResource
	}
	return nil
}

// GetResponse
type GetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//	资源详情 , e.g.: {
	//	   "apiVersion": "v1",
	//	   "kind": "Namespace",
	//	   "metadata": {
	//	       "creationTimestamp": "2022-11-22T06:41:21Z",
	//	       "name": "infra"
	//	   },
	//	   "spec": {
	//	       "finalizers": [
	//	           "kubernetes"
	//	       ]
	//	   },
	//	   "status": {
	//	       "phase": "Active"
	//	   }
	//	}
	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetResponse) Reset() {
	*x = GetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResponse) ProtoMessage() {}

func (x *GetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResponse.ProtoReflect.Descriptor instead.
func (*GetResponse) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{4}
}

func (x *GetResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// CreateRequest
type CreateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名, e.g.: k8s-tc-bj-1-yunwei
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间(非namespaced资源无需填写), e.g.: infra
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	//	资源详情 , e.g.: {
	//	   "apiVersion": "v1",
	//	   "kind": "Namespace",
	//	   "metadata": {
	//	       "name": "infra"
	//	   },
	//	   "spec": {
	//	       "finalizers": [
	//	           "kubernetes"
	//	       ]
	//	   }
	//	}
	Data string `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CreateRequest) Reset() {
	*x = CreateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRequest) ProtoMessage() {}

func (x *CreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRequest.ProtoReflect.Descriptor instead.
func (*CreateRequest) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{5}
}

func (x *CreateRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *CreateRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *CreateRequest) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// CreateResponse
type CreateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateResponse) Reset() {
	*x = CreateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResponse) ProtoMessage() {}

func (x *CreateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResponse.ProtoReflect.Descriptor instead.
func (*CreateResponse) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{6}
}

// UpdateRequest
type UpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名, e.g.: k8s-tc-bj-1-yunwei
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间(非namespaced资源无需填写), e.g.: infra
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	//	资源详情 , e.g.: {
	//	   "apiVersion": "v1",
	//	   "kind": "Namespace",
	//	   "metadata": {
	//	       "name": "infra"
	//	   },
	//	   "spec": {
	//	       "finalizers": [
	//	           "kubernetes"
	//	       ]
	//	   }
	//	}
	Data string `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *UpdateRequest) Reset() {
	*x = UpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRequest) ProtoMessage() {}

func (x *UpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRequest.ProtoReflect.Descriptor instead.
func (*UpdateRequest) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *UpdateRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *UpdateRequest) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// UpdateResponse
type UpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateResponse) Reset() {
	*x = UpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResponse) ProtoMessage() {}

func (x *UpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResponse.ProtoReflect.Descriptor instead.
func (*UpdateResponse) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{8}
}

// DeleteRequest
type DeleteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名, e.g.: k8s-tc-bj-1-yunwei
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间(非namespaced资源无需填写), e.g.: infra
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 资源名, e.g.: cloud-enterprise
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// e.g.: {"group":"apps","version":"v1","resource":"deployments"}
	GroupVersionResource *GroupVersionResource `protobuf:"bytes,4,opt,name=group_version_resource,json=groupVersionResource,proto3" json:"group_version_resource,omitempty"`
}

func (x *DeleteRequest) Reset() {
	*x = DeleteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRequest) ProtoMessage() {}

func (x *DeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRequest.ProtoReflect.Descriptor instead.
func (*DeleteRequest) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DeleteRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeleteRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeleteRequest) GetGroupVersionResource() *GroupVersionResource {
	if x != nil {
		return x.GroupVersionResource
	}
	return nil
}

// DeleteResponse
type DeleteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteResponse) Reset() {
	*x = DeleteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteResponse) ProtoMessage() {}

func (x *DeleteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteResponse.ProtoReflect.Descriptor instead.
func (*DeleteResponse) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{10}
}

// ApplyRequest
type ApplyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名, e.g.: k8s-tc-bj-1-yunwei
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间(非namespaced资源无需填写), e.g.: infra
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	//	资源详情 , e.g.: {
	//	   "apiVersion": "v1",
	//	   "kind": "Namespace",
	//	   "metadata": {
	//	       "name": "infra"
	//	   },
	//	   "spec": {
	//	       "finalizers": [
	//	           "kubernetes"
	//	       ]
	//	   }
	//	}
	Data string `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ApplyRequest) Reset() {
	*x = ApplyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyRequest) ProtoMessage() {}

func (x *ApplyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyRequest.ProtoReflect.Descriptor instead.
func (*ApplyRequest) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{11}
}

func (x *ApplyRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ApplyRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ApplyRequest) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// ApplyResponse
type ApplyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ApplyResponse) Reset() {
	*x = ApplyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyResponse) ProtoMessage() {}

func (x *ApplyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyResponse.ProtoReflect.Descriptor instead.
func (*ApplyResponse) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{12}
}

// DeleteCollectionRequest
type DeleteCollectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名, e.g.: k8s-tc-bj-1-yunwei
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间(非namespaced资源无需填写), e.g.: infra
	Namespace string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// 资源名数组, e.g.: ["cloud-enterprise","web-cloud-enterprise"]
	Names []string `protobuf:"bytes,3,rep,name=names,proto3" json:"names,omitempty"`
}

func (x *DeleteCollectionRequest) Reset() {
	*x = DeleteCollectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCollectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCollectionRequest) ProtoMessage() {}

func (x *DeleteCollectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCollectionRequest.ProtoReflect.Descriptor instead.
func (*DeleteCollectionRequest) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteCollectionRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DeleteCollectionRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeleteCollectionRequest) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

// DeleteCollectionResponse
type DeleteCollectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteCollectionResponse) Reset() {
	*x = DeleteCollectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCollectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCollectionResponse) ProtoMessage() {}

func (x *DeleteCollectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCollectionResponse.ProtoReflect.Descriptor instead.
func (*DeleteCollectionResponse) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{14}
}

type MultiResourceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 集群名, e.g.: k8s-tc-bj-1-yunwei
	Cluster []string `protobuf:"bytes,1,rep,name=cluster,proto3" json:"cluster,omitempty"`
	// 命名空间(非namespaced资源无需填写), e.g.: infra
	Namespace []string `protobuf:"bytes,2,rep,name=namespace,proto3" json:"namespace,omitempty"`
	// e.g.: {"group":"apps","version":"v1","resource":"deployments"}
	GroupVersionResource []*GroupVersionResource `protobuf:"bytes,3,rep,name=group_version_resource,json=groupVersionResource,proto3" json:"group_version_resource,omitempty"`
	// 资源名 模糊匹配, e.g.: cloud-enterprise
	Search string `protobuf:"bytes,4,opt,name=search,proto3" json:"search,omitempty"`
	Extra string `protobuf:"bytes,5,opt,name=extra,proto3" json:"extra,omitempty"`
	// 起始页码 从 1 开始
	Page int32 `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	// 每页大小 0 表示不分页
	Size int32 `protobuf:"varint,7,opt,name=size,proto3" json:"size,omitempty"`
}

func (x *MultiResourceReq) Reset() {
	*x = MultiResourceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiResourceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiResourceReq) ProtoMessage() {}

func (x *MultiResourceReq) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiResourceReq.ProtoReflect.Descriptor instead.
func (*MultiResourceReq) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{15}
}

func (x *MultiResourceReq) GetCluster() []string {
	if x != nil {
		return x.Cluster
	}
	return nil
}

func (x *MultiResourceReq) GetNamespace() []string {
	if x != nil {
		return x.Namespace
	}
	return nil
}

func (x *MultiResourceReq) GetGroupVersionResource() []*GroupVersionResource {
	if x != nil {
		return x.GroupVersionResource
	}
	return nil
}

func (x *MultiResourceReq) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

func (x *MultiResourceReq) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *MultiResourceReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *MultiResourceReq) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type MultiResourceResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64               `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Objs  []*MultiResourceObj `protobuf:"bytes,2,rep,name=objs,proto3" json:"objs,omitempty"`
}

func (x *MultiResourceResp) Reset() {
	*x = MultiResourceResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiResourceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiResourceResp) ProtoMessage() {}

func (x *MultiResourceResp) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiResourceResp.ProtoReflect.Descriptor instead.
func (*MultiResourceResp) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{16}
}

func (x *MultiResourceResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *MultiResourceResp) GetObjs() []*MultiResourceObj {
	if x != nil {
		return x.Objs
	}
	return nil
}

type MultiResourceObj struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                 string                `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Resource             string                `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`
	ClusterName          string                `protobuf:"bytes,3,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	Namespace            string                `protobuf:"bytes,4,opt,name=namespace,proto3" json:"namespace,omitempty"`
	ClusterId            int64                 `protobuf:"varint,5,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
	CreatedAt            int64                 `protobuf:"varint,6,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	Uuid                 string                `protobuf:"bytes,7,opt,name=uuid,proto3" json:"uuid,omitempty"`
	GroupVersionResource *GroupVersionResource `protobuf:"bytes,8,opt,name=group_version_resource,json=groupVersionResource,proto3" json:"group_version_resource,omitempty"`
	IsAuthorized         bool                  `protobuf:"varint,9,opt,name=isAuthorized,proto3" json:"isAuthorized,omitempty"`
	Data                 []byte                `protobuf:"bytes,10,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *MultiResourceObj) Reset() {
	*x = MultiResourceObj{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MultiResourceObj) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiResourceObj) ProtoMessage() {}

func (x *MultiResourceObj) ProtoReflect() protoreflect.Message {
	mi := &file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiResourceObj.ProtoReflect.Descriptor instead.
func (*MultiResourceObj) Descriptor() ([]byte, []int) {
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP(), []int{17}
}

func (x *MultiResourceObj) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MultiResourceObj) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *MultiResourceObj) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *MultiResourceObj) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *MultiResourceObj) GetClusterId() int64 {
	if x != nil {
		return x.ClusterId
	}
	return 0
}

func (x *MultiResourceObj) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *MultiResourceObj) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *MultiResourceObj) GetGroupVersionResource() *GroupVersionResource {
	if x != nil {
		return x.GroupVersionResource
	}
	return nil
}

func (x *MultiResourceObj) GetIsAuthorized() bool {
	if x != nil {
		return x.IsAuthorized
	}
	return false
}

func (x *MultiResourceObj) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_quwan_cloud_constack_v1alpha_anyresource_proto protoreflect.FileDescriptor

var file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2f, 0x61,
	0x6e, 0x79, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1c, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x22, 0x62,
	0x0a, 0x14, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x22, 0xaf, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x68, 0x0a, 0x16, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x71, 0x75, 0x77,
	0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x14,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x22, 0x22, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xc2, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x68, 0x0a, 0x16, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x21, 0x0a,
	0x0b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x5b, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x10, 0x0a,
	0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x5b, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x10, 0x0a, 0x0e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xc5,
	0x01, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x68, 0x0a, 0x16,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x71,
	0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x10, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x5a, 0x0a, 0x0c, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x0f, 0x0a, 0x0d, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x67, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x1a,
	0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8a, 0x02, 0x0a, 0x10, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x68, 0x0a, 0x16, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x14, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x6d, 0x0a, 0x11, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x42, 0x0a, 0x04, 0x6f, 0x62, 0x6a, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x62, 0x6a,
	0x52, 0x04, 0x6f, 0x62, 0x6a, 0x73, 0x22, 0xf4, 0x02, 0x0a, 0x10, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x62, 0x6a, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x68, 0x0a, 0x16, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x71, 0x75,
	0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x41, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x7a, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0xe6, 0x06,
	0x0a, 0x12, 0x41, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x5f, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x29, 0x2e, 0x71,
	0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5c, 0x0a, 0x03, 0x47, 0x65, 0x74, 0x12, 0x28, 0x2e, 0x71,
	0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74,
	0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x06, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x2e,
	0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x71, 0x75, 0x77,
	0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x06, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2c, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x65, 0x0a, 0x06, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x2b, 0x2e, 0x71, 0x75,
	0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61,
	0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x12, 0x2a, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e,
	0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x83, 0x01, 0x0a,
	0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x35, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x72, 0x0a, 0x0d, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x2e, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x47, 0x5a, 0x45, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67,
	0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x65, 0x6e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x71, 0x75, 0x77, 0x61, 0x6e, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x2f, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x3b, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescOnce sync.Once
	file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescData = file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDesc
)

func file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescGZIP() []byte {
	file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescOnce.Do(func() {
		file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescData = protoimpl.X.CompressGZIP(file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescData)
	})
	return file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDescData
}

var file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_quwan_cloud_constack_v1alpha_anyresource_proto_goTypes = []any{
	(*GroupVersionResource)(nil),     // 0: quwan.cloud.constack.v1alpha.GroupVersionResource
	(*ListRequest)(nil),              // 1: quwan.cloud.constack.v1alpha.ListRequest
	(*ListResponse)(nil),             // 2: quwan.cloud.constack.v1alpha.ListResponse
	(*GetRequest)(nil),               // 3: quwan.cloud.constack.v1alpha.GetRequest
	(*GetResponse)(nil),              // 4: quwan.cloud.constack.v1alpha.GetResponse
	(*CreateRequest)(nil),            // 5: quwan.cloud.constack.v1alpha.CreateRequest
	(*CreateResponse)(nil),           // 6: quwan.cloud.constack.v1alpha.CreateResponse
	(*UpdateRequest)(nil),            // 7: quwan.cloud.constack.v1alpha.UpdateRequest
	(*UpdateResponse)(nil),           // 8: quwan.cloud.constack.v1alpha.UpdateResponse
	(*DeleteRequest)(nil),            // 9: quwan.cloud.constack.v1alpha.DeleteRequest
	(*DeleteResponse)(nil),           // 10: quwan.cloud.constack.v1alpha.DeleteResponse
	(*ApplyRequest)(nil),             // 11: quwan.cloud.constack.v1alpha.ApplyRequest
	(*ApplyResponse)(nil),            // 12: quwan.cloud.constack.v1alpha.ApplyResponse
	(*DeleteCollectionRequest)(nil),  // 13: quwan.cloud.constack.v1alpha.DeleteCollectionRequest
	(*DeleteCollectionResponse)(nil), // 14: quwan.cloud.constack.v1alpha.DeleteCollectionResponse
	(*MultiResourceReq)(nil),         // 15: quwan.cloud.constack.v1alpha.MultiResourceReq
	(*MultiResourceResp)(nil),        // 16: quwan.cloud.constack.v1alpha.MultiResourceResp
	(*MultiResourceObj)(nil),         // 17: quwan.cloud.constack.v1alpha.MultiResourceObj
}
var file_quwan_cloud_constack_v1alpha_anyresource_proto_depIdxs = []int32{
	0,  // 0: quwan.cloud.constack.v1alpha.ListRequest.group_version_resource:type_name -> quwan.cloud.constack.v1alpha.GroupVersionResource
	0,  // 1: quwan.cloud.constack.v1alpha.GetRequest.group_version_resource:type_name -> quwan.cloud.constack.v1alpha.GroupVersionResource
	0,  // 2: quwan.cloud.constack.v1alpha.DeleteRequest.group_version_resource:type_name -> quwan.cloud.constack.v1alpha.GroupVersionResource
	0,  // 3: quwan.cloud.constack.v1alpha.MultiResourceReq.group_version_resource:type_name -> quwan.cloud.constack.v1alpha.GroupVersionResource
	17, // 4: quwan.cloud.constack.v1alpha.MultiResourceResp.objs:type_name -> quwan.cloud.constack.v1alpha.MultiResourceObj
	0,  // 5: quwan.cloud.constack.v1alpha.MultiResourceObj.group_version_resource:type_name -> quwan.cloud.constack.v1alpha.GroupVersionResource
	1,  // 6: quwan.cloud.constack.v1alpha.AnyResourceService.List:input_type -> quwan.cloud.constack.v1alpha.ListRequest
	3,  // 7: quwan.cloud.constack.v1alpha.AnyResourceService.Get:input_type -> quwan.cloud.constack.v1alpha.GetRequest
	5,  // 8: quwan.cloud.constack.v1alpha.AnyResourceService.Create:input_type -> quwan.cloud.constack.v1alpha.CreateRequest
	7,  // 9: quwan.cloud.constack.v1alpha.AnyResourceService.Update:input_type -> quwan.cloud.constack.v1alpha.UpdateRequest
	9,  // 10: quwan.cloud.constack.v1alpha.AnyResourceService.Delete:input_type -> quwan.cloud.constack.v1alpha.DeleteRequest
	11, // 11: quwan.cloud.constack.v1alpha.AnyResourceService.Apply:input_type -> quwan.cloud.constack.v1alpha.ApplyRequest
	13, // 12: quwan.cloud.constack.v1alpha.AnyResourceService.DeleteCollection:input_type -> quwan.cloud.constack.v1alpha.DeleteCollectionRequest
	15, // 13: quwan.cloud.constack.v1alpha.AnyResourceService.MultiResource:input_type -> quwan.cloud.constack.v1alpha.MultiResourceReq
	2,  // 14: quwan.cloud.constack.v1alpha.AnyResourceService.List:output_type -> quwan.cloud.constack.v1alpha.ListResponse
	4,  // 15: quwan.cloud.constack.v1alpha.AnyResourceService.Get:output_type -> quwan.cloud.constack.v1alpha.GetResponse
	6,  // 16: quwan.cloud.constack.v1alpha.AnyResourceService.Create:output_type -> quwan.cloud.constack.v1alpha.CreateResponse
	8,  // 17: quwan.cloud.constack.v1alpha.AnyResourceService.Update:output_type -> quwan.cloud.constack.v1alpha.UpdateResponse
	10, // 18: quwan.cloud.constack.v1alpha.AnyResourceService.Delete:output_type -> quwan.cloud.constack.v1alpha.DeleteResponse
	12, // 19: quwan.cloud.constack.v1alpha.AnyResourceService.Apply:output_type -> quwan.cloud.constack.v1alpha.ApplyResponse
	14, // 20: quwan.cloud.constack.v1alpha.AnyResourceService.DeleteCollection:output_type -> quwan.cloud.constack.v1alpha.DeleteCollectionResponse
	16, // 21: quwan.cloud.constack.v1alpha.AnyResourceService.MultiResource:output_type -> quwan.cloud.constack.v1alpha.MultiResourceResp
	14, // [14:22] is the sub-list for method output_type
	6,  // [6:14] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_quwan_cloud_constack_v1alpha_anyresource_proto_init() }
func file_quwan_cloud_constack_v1alpha_anyresource_proto_init() {
	if File_quwan_cloud_constack_v1alpha_anyresource_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GroupVersionResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*GetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*CreateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*CreateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*ApplyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*ApplyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteCollectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*DeleteCollectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*MultiResourceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*MultiResourceResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*MultiResourceObj); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_quwan_cloud_constack_v1alpha_anyresource_proto_goTypes,
		DependencyIndexes: file_quwan_cloud_constack_v1alpha_anyresource_proto_depIdxs,
		MessageInfos:      file_quwan_cloud_constack_v1alpha_anyresource_proto_msgTypes,
	}.Build()
	File_quwan_cloud_constack_v1alpha_anyresource_proto = out.File
	file_quwan_cloud_constack_v1alpha_anyresource_proto_rawDesc = nil
	file_quwan_cloud_constack_v1alpha_anyresource_proto_goTypes = nil
	file_quwan_cloud_constack_v1alpha_anyresource_proto_depIdxs = nil
}
