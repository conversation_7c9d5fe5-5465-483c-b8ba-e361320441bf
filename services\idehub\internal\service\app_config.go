//go:generate mockgen -destination=app_config_mock.go -package=service -source=app_config.go
package service

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strings"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/log"
	pbapp "52tt.com/cicd/protocol/app"
	pbdeploy "52tt.com/cicd/protocol/deploy"
	"52tt.com/cicd/services/idehub/internal/model"
	error2 "52tt.com/cicd/services/idehub/pkg/error"
	"github.com/pkg/errors"
	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
	mesh "golang.ttyuyin.com/genproto/quwanapis/cloud/mesh/v1alpha"
	v1 "k8s.io/api/apps/v1"
	v12 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"sigs.k8s.io/yaml"
)

var _ AppConfigService = (*AppConfigSvc)(nil)

type AppConfigSvc struct {
	deployGrpcClient   pbdeploy.DeployServiceClient
	appGrpcClient      pbapp.AppServiceClient
	anyResourceClient  constack.AnyResourceServiceClient
	userConfigSvc      UserConfigService
	serviceRouteClient mesh.ServiceRouteServiceClient
}

func NewAppConfigService(
	deployGrpcClient pbdeploy.DeployServiceClient, appGrpcClient pbapp.AppServiceClient, userConfigSvc UserConfigService, anyResourceClient constack.AnyResourceServiceClient, serviceRouteClient mesh.ServiceRouteServiceClient,
) AppConfigService {
	return &AppConfigSvc{deployGrpcClient: deployGrpcClient, appGrpcClient: appGrpcClient, userConfigSvc: userConfigSvc, anyResourceClient: anyResourceClient, serviceRouteClient: serviceRouteClient}
}

func NewAppConfigServiceWithNull() AppConfigService {
	return &AppConfigSvc{}
}

type AppConfigService interface {
	GetAppClusterInfo(ctx context.Context, appId int64, appCluster *string) (cluster, namespace, name string, err error)
	GenerateNhctlConfig(ctx context.Context, userID int64, appID int64, cluster, namespace, appName string) (*model.ServiceConfigV2, error)
	GetAppClusterInfoWithAppIds(ctx context.Context, appCluster *string, appIds ...int64) (apps map[int64]*model.AppsItem, err error)
	GetAppClusterInfoWithProjectIds(ctx context.Context, appCluster *string, prjIds ...int64) (apps map[int64]*model.AppsItem, err error)
	BuildSubEnvRoute(ctx context.Context, in *BuildSubEnvRouteInput) (err error)
	ClearBuildSubEnvRoute(ctx context.Context, in *ClearSubEnvRouteInput) (err error)
	GetAppInfo(ctx context.Context, appId int64) (res *pbapp.APP, err error)
}

type DevImage string

var (
	GolangDevImage DevImage = "cr.ttyuyin.com/tt_oss/tt-golang-dev:latest"

	DefaultWorkDir      = "/home/<USER>"
	DefaultSidecarImage = "cr.ttyuyin.com/tt_oss/nocalhost-sidecar:syncthing"
	DefaultConfigType   = "deployment"

	// sync
	DefaultSyncType = "send"
	DefaultSyncMode = "pattern"

	DefaultIgnoreFilePattern = []string{".git", ".github", ".vscode", "node_modules"}
)

// GetAppClusterInfo 用于查找 app 对应部署的集群信息。
// 参数：
// - appId: app id
// - appCluster: 可选参数。指定会按照 appCluster 查找。
func (ac *AppConfigSvc) GetAppClusterInfo(ctx context.Context, appId int64, appCluster *string) (cluster, namespace, name string, err error) {
	resp, err := ac.deployGrpcClient.ListAppsChangeLog(ctx, &pbdeploy.ListAppsChangeLogReq{
		AppIds:    []int64{appId},
		Env:       string(constants.TESTING), // 本地开发只能在测试集群进行。
		IsCurrent: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAppClusterInfo via appId %d failed: %v", appId, err)
		return "", "", "", err
	}
	if len(resp.ChangeLogs) == 0 {
		log.ErrorWithCtx(ctx, "GetAppClusterInfo via appId %d not found", appId)
		return "", "", "", fmt.Errorf("app %d not found", appId)
	}
	var (
		primaryApp      *pbdeploy.AppsChangeLog
		latestSubenvApp *pbdeploy.AppsChangeLog
	)
	for _, app := range resp.ChangeLogs {
		if app.Status != constants.DeployStatusSuccessful.String() {
			continue
		}
		if appCluster != nil && *appCluster != app.Cluster {
			continue
		}

		if len(app.GetSenv()) == 0 {
			primaryApp = app
			break
		}
		// 获取最新的子环境服务
		if latestSubenvApp == nil || app.Id > latestSubenvApp.Id {
			latestSubenvApp = app
		}

	}

	// 优先返回基准环境的服务，没有的话再返回子环境的服务信息。
	if primaryApp != nil {
		return primaryApp.Cluster, primaryApp.Namespace, primaryApp.AppName, nil
	}

	if latestSubenvApp != nil {
		return latestSubenvApp.Cluster, latestSubenvApp.Namespace, latestSubenvApp.AppName, nil
	}

	// rest 层需要判断这个错误，返回一个具体的 api error。nhctl 根据 code 值做错误处理。
	return "", "", "", error2.ErrJetdevClusterNotFound
}

func (ac *AppConfigSvc) GetAppClusterInfoWithAppIds(ctx context.Context, appCluster *string, appIds ...int64) (apps map[int64]*model.AppsItem, err error) {
	if len(appIds) == 0 {
		return nil, nil
	}
	resp, err := ac.deployGrpcClient.ListAppsChangeLog(ctx, &pbdeploy.ListAppsChangeLogReq{
		AppIds:    appIds,
		Env:       string(constants.TESTING), // 本地开发只能在测试集群进行。
		IsCurrent: true,
	})

	apps = make(map[int64]*model.AppsItem, 0)

	var transferMap map[int64]*pbdeploy.AppsChangeLog // key = appId

	transferMap = make(map[int64]*pbdeploy.AppsChangeLog, 0)

	for _, changeLogData := range resp.GetChangeLogs() {
		// 跳过 success
		if changeLogData.Status != constants.DeployStatusSuccessful.String() {
			continue
		}

		if appCluster != nil && *appCluster != changeLogData.Cluster {
			continue
		}

		// 过滤子环境
		if len(changeLogData.Senv) != 0 {
			continue
		}
		if changeLogData.EnvTarget != constants.EnumEnvTargetOrigin.String() {
			continue
		}
		//transferMap[changeLogData.AppId] = changeLogData
		// 同一个app, 用最新id的数据覆盖
		if data := transferMap[changeLogData.AppId]; data != nil {
			if changeLogData.Id > data.Id {
				// 覆盖
				transferMap[changeLogData.AppId] = changeLogData
			}
		} else {
			transferMap[changeLogData.AppId] = changeLogData
		}

	}

	for key, changeLogData := range transferMap {
		apps[key] = &model.AppsItem{
			Cluster:   changeLogData.Cluster,
			Namespace: changeLogData.Namespace,
			ID:        changeLogData.AppId,
			Name:      changeLogData.AppName,
			Status:    constants.DebugStatusPending.String(),
		}
	}

	return apps, nil
}

func (ac *AppConfigSvc) GetAppClusterInfoWithProjectIds(ctx context.Context, appCluster *string, prjIds ...int64) (apps map[int64]*model.AppsItem, err error) {
	if len(prjIds) == 0 {
		return nil, nil
	}
	resp, err := ac.deployGrpcClient.ListAppsChangeLog(ctx, &pbdeploy.ListAppsChangeLogReq{
		PrjIds:    prjIds,
		Env:       string(constants.TESTING), // 本地开发只能在测试集群进行。
		IsCurrent: true,
	})

	if err != nil {
		return nil, err
	}

	apps = make(map[int64]*model.AppsItem, 0)

	transferMap := make(map[int64]*pbdeploy.AppsChangeLog, 0) // key = appId

	for _, changeLogData := range resp.GetChangeLogs() {
		// 跳过 success
		if changeLogData.Status != constants.DeployStatusSuccessful.String() {
			continue
		}

		if appCluster != nil && *appCluster != changeLogData.Cluster {
			continue
		}

		// 过滤子环境
		if len(changeLogData.Senv) != 0 {
			continue
		}
		if changeLogData.EnvTarget != constants.EnumEnvTargetOrigin.String() {
			continue
		}
		//transferMap[changeLogData.AppId] = changeLogData
		// 同一个app, 用最新id的数据覆盖
		if data := transferMap[changeLogData.AppId]; data != nil {
			if changeLogData.Id > data.Id {
				// 覆盖
				transferMap[changeLogData.AppId] = changeLogData
			}
		} else {
			transferMap[changeLogData.AppId] = changeLogData
		}

	}

	for key, changeLogData := range transferMap {
		apps[key] = &model.AppsItem{
			Cluster:   changeLogData.Cluster,
			Namespace: changeLogData.Namespace,
			ID:        changeLogData.AppId,
			Name:      changeLogData.AppName,
			Status:    constants.DebugStatusPending.String(),
		}
	}

	return apps, nil
}

// GenerateNhctlConfig 生成nhctl所需的配置文件, 具体对应: https://q9jvw0u5f5.feishu.cn/docx/KxjDdgQyKoU8CBxdr6Ic1lQqnhd#DgXRdxXjYoal3hx8a4Fc5AY0nUd
func (ac *AppConfigSvc) GenerateNhctlConfig(ctx context.Context, userID int64, appID int64, cluster, namespace, appName string) (*model.ServiceConfigV2, error) {
	// call to star-constack
	req := &constack.GetRequest{
		Cluster:   cluster,
		Namespace: namespace,
		Name:      appName,
		GroupVersionResource: &constack.GroupVersionResource{
			Group:    "apps",
			Version:  "v1",
			Resource: "deployments",
		},
	}
	appDeployment, err := ac.anyResourceClient.Get(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenerateNhctlConfig get app yaml failed error:%s, req:%+v", err.Error(), req)
		return nil, err
	}

	originDeploy := &v1.Deployment{}
	err = yaml.Unmarshal([]byte(appDeployment.GetData()), originDeploy)
	if err != nil {
		return nil, err
	}

	config, err := ac.userConfigSvc.GetUserAppConfig(ctx, model.UserAppConfigReq{
		UserId:    userID,
		AppId:     appID,
		Cluster:   cluster,
		Namespace: namespace,
		AppName:   appName,
	})
	if err != nil {
		return nil, err
	}

	nhctlConfig := ac.generateNhctlConfigByDeploy(ctx, &model.UserAppConfigDto{
		Id:     config.Id,
		UserId: config.UserId,
		AppId:  config.AppId,
		Config: config.Config,
	}, originDeploy)
	if nhctlConfig == nil {
		return nil, errors.New("generate nhctl config failed")
	}
	return nhctlConfig, nil
}

func (ac *AppConfigSvc) generateNhctlConfigByDeploy(ctx context.Context, userConfig *model.UserAppConfigDto, deploy *v1.Deployment) *model.ServiceConfigV2 {
	if deploy == nil {
		return nil
	}

	if len(deploy.Spec.Template.Spec.Containers) == 0 {
		return nil
	}

	config := &model.ServiceConfigV2{
		// 默认第一个
		Name:             deploy.Name,
		Type:             DefaultConfigType,
		ContainerConfigs: make([]*model.ContainerConfig, 0),
	}

	// env
	envs := make([]*model.Env, 0)

	for _, container := range deploy.Spec.Template.Spec.Containers {
		if container.Name != "service" && container.Name != deploy.Name {
			// container 应该只有一个, 要么与 Deployment.name 一样, 要么是 "service".
			log.InfoWithCtx(ctx, "GenerateNhctlConfig container name %s is not match nhctl dev container", container.Name)
			continue
		}
		limitCpu := resource.NewQuantity(int64(userConfig.Config.Resources.GetLimitCpu()), resource.DecimalSI)
		requsetCpu := resource.NewQuantity(int64(userConfig.Config.Resources.GetRequestCpu()), resource.DecimalSI)
		containerConfig := &model.ContainerConfig{
			Name: container.Name,
			Dev: &model.ContainerDevConfig{
				DevContainerName: container.Name,
				WorkDir:          DefaultWorkDir,
				// 单位是核 和 Mi
				DevContainerResources: &model.ResourceQuota{
					Limits: &model.QuotaList{
						Memory: fmt.Sprintf("%dMi", int64(userConfig.Config.Resources.GetLimitMemory())),
						Cpu:    limitCpu.String(),
					},
					Requests: &model.QuotaList{
						Memory: fmt.Sprintf("%dMi", int64(userConfig.Config.Resources.GetRequestMemory())),
						Cpu:    requsetCpu.String(),
					},
				},
				Sync: &model.SyncConfig{
					Type:              DefaultSyncType,
					Mode:              DefaultSyncMode,
					FilePattern:       []string{"."},
					IgnoreFilePattern: DefaultIgnoreFilePattern,
				},
				SidecarImage: DefaultSidecarImage,
				// StorageClass:         "cfs-cicd-test-new",
				// PersistentVolumeDirs: []*model.PersistentVolumeDir{{Path: "/go/pkg/mod", Capacity: "10Gi"}},
			},
		} // image cmd
		if value := deploy.Spec.Template.Labels["lang"]; value != "" {
			switch value {
			case constants.TransLan(constants.GO.String()).String():
				containerConfig.Dev.Image = string(GolangDevImage)
				debugPort := rand.Intn(100) + 9000
				containerConfig.Dev.DebugConfig = &model.DebugConfig{
					// random port
					RemoteDebugPort: debugPort,
					Language:        constants.GO.String(),
				}

				containerConfig.Dev.Command = &model.DevCommands{}
				// 根据run 命令生成  debug
				debugcmds := generateDebugCmd(debugPort, userConfig.Config.Command, constants.GO)
				containerConfig.Dev.Command.Run = []string{userConfig.Config.Command}
				containerConfig.Dev.Command.Debug = debugcmds
			}
		}
		for _, envVar := range container.Env {

			env := env2env(ctx, envVar, deploy)
			if env == nil {
				continue
			}

			envs = append(envs, &model.Env{
				Name:  env.Name,
				Value: env.Value,
			})
		}
		for _, env := range userConfig.Config.Envs {
			envs = append(envs, &model.Env{
				Name:  env.GetKey(),
				Value: env.GetValue(),
			})
		}
		containerConfig.Dev.Env = envs
		config.ContainerConfigs = append(config.ContainerConfigs, containerConfig)
	}

	if len(config.ContainerConfigs) == 0 {
		log.ErrorWithCtx(ctx, "GenerateNhctlConfigByDeploy failed, containerConfigs is empty, please check Deployment %s container name in cluster", deploy.Name)
		return nil
	}

	return config
}

func parseFlag(input string, lang constants.Language) (startCmd []string, execFile string, args []string) {
	inputs := strings.Split(input, " ")
	for index, cmd := range inputs {
		if strings.Contains(cmd, fmt.Sprintf(".%s", lang.String())) {
			execFile = cmd
			startCmd = inputs[:index+1]
			args = inputs[index+1:]
		}
	}
	return
}

func generateDebugCmd(port int, input string, lang constants.Language) []string {
	_, execFile, args := parseFlag(input, lang)
	switch lang {
	case constants.GO:
		debugCmds := []string{
			"dlv",
			"--headless",
			"--log",
			fmt.Sprintf("--listen :%d", port),
			"--api-version 2",
			"--accept-multiclient",
			"debug",
			execFile,
		}
		if len(args) > 0 {
			debugCmds = append(debugCmds, "--")
			debugCmds = append(debugCmds, args...)
		} else {
			debugCmds = append(debugCmds, args...)
		}
		return debugCmds
	}

	return nil
}

func env2env(ctx context.Context, envVar v12.EnvVar, deployment *v1.Deployment) *model.Env {
	e := &model.Env{
		Name: envVar.Name,
	}
	if envVar.Value != "" {
		e.Value = envVar.Value
	}

	marshal, _ := json.Marshal(deployment)

	unstruct := &unstructured.Unstructured{}

	err := json.Unmarshal(marshal, unstruct)
	if err != nil {
		log.ErrorWithCtx(ctx, "json unmarshal error")
		return nil
	}

	if envVar.ValueFrom != nil {
		if envVar.ValueFrom.FieldRef != nil {
			// split := strings.Split(envVar.ValueFrom.FieldRef.FieldPath, ".")

			//targetData := map[string]interface{}{}
			//for _, value := range split {
			//	if reflect.ValueOf(value).Kind() == reflect.Map{
			//		targetData = unstruct.Object[value].(map[string]interface{})
			//	}
			//
			//}
		}
	}

	if e.Value == "" {
		return nil
	}

	return e
}

type BuildSubEnvRouteInput struct {
	Cluster     string
	Namespace   string
	Name        string
	Senv        string
	TrafficMark string
}

func (ac *AppConfigSvc) BuildSubEnvRoute(ctx context.Context, in *BuildSubEnvRouteInput) (err error) {
	_, err = ac.serviceRouteClient.BuildSubenvRoute(ctx, &mesh.BuildSubenvRouteRequest{
		Service: &mesh.Service{
			Cluster:   in.Cluster,
			Namespace: in.Namespace,
			Name:      in.Name,
		},
		SubenvName: in.Senv,
		NewTrafficMark: &mesh.TrafficMark{
			Identifier:  fmt.Sprintf("cicd-idehub-%s", in.TrafficMark),
			StringMatch: &mesh.StringMatch{MatchType: &mesh.StringMatch_Exact{Exact: in.TrafficMark}},
		},
	})
	if err != nil {
		return errors.Wrapf(err, "[AppConfigSvc.BuildSubEnvRoute] call to constack BuildSubenvRoute error:%s , req:%+v", err.Error(), in)
	}

	return nil
}

type ClearSubEnvRouteInput struct {
	Cluster   string
	Namespace string
	Name      string
	Senv      string
}

func (ac *AppConfigSvc) ClearBuildSubEnvRoute(ctx context.Context, in *ClearSubEnvRouteInput) (err error) {
	_, err = ac.serviceRouteClient.ClearSubenvRoute(ctx, &mesh.ClearSubenvRouteRequest{
		Service: &mesh.Service{
			Cluster:   in.Cluster,
			Namespace: in.Namespace,
			Name:      in.Name,
		},
		SubenvName: in.Senv,
	})
	if err != nil {
		return errors.Wrapf(err, "[AppConfigSvc.ClearBuildSubEnvRoute] call to constack ClearSubenvRoute error:%s , req:%+v", err.Error(), in)
	}

	return nil
}

func (ac *AppConfigSvc) GetAppInfo(ctx context.Context, appId int64) (res *pbapp.APP, err error) {
	app, err := ac.appGrpcClient.GetApp(ctx, &pbapp.AppParam{Id: appId})
	if err != nil {
		return nil, errors.Wrapf(err, "[AppConfigSvc.GetAppInfo] call to appGrpcClient GetApp error:%s , req:%+v", err.Error(), appId)
	}

	return app, nil
}
