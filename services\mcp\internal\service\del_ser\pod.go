package del_ser

import (
	"context"
	"fmt"

	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
)

type KillPodArgs struct {
	ClusterName string `json:"clusterName" jsonschema:"required,description=集群名称"`
	Namespace   string `json:"namespace" jsonschema:"required,description=命名空间(NS)名称"`
	Name        string `json:"name" jsonschema:"required,description=要删除的pod名"`
}

func (agg *deleteAgg) KillPod(req KillPodArgs) (err error) {
	if req.ClusterName == "" || req.Namespace == "" || req.Name == "" {
		err = fmt.Errorf("clusterName, namespace, name must be set")
		return
	}
	// 查询 k8s 资源
	_, err = agg.cloudagg.Delete(context.Background(), &constack.DeleteRequest{
		Cluster:   req.ClusterName,
		Namespace: req.Namespace,
		Name:      req.Name,
		GroupVersionResource: &constack.GroupVersionResource{
			Group:    "",
			Version:  "v1",
			Resource: "pods",
		},
	})
	if err != nil {
		return
	}

	return
}
