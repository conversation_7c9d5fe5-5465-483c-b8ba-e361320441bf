// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.4
// source: quwan/cloud/constack/v1alpha/anyresource.proto

package constack

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AnyResourceService_List_FullMethodName             = "/quwan.cloud.constack.v1alpha.AnyResourceService/List"
	AnyResourceService_Get_FullMethodName              = "/quwan.cloud.constack.v1alpha.AnyResourceService/Get"
	AnyResourceService_Create_FullMethodName           = "/quwan.cloud.constack.v1alpha.AnyResourceService/Create"
	AnyResourceService_Update_FullMethodName           = "/quwan.cloud.constack.v1alpha.AnyResourceService/Update"
	AnyResourceService_Delete_FullMethodName           = "/quwan.cloud.constack.v1alpha.AnyResourceService/Delete"
	AnyResourceService_Apply_FullMethodName            = "/quwan.cloud.constack.v1alpha.AnyResourceService/Apply"
	AnyResourceService_DeleteCollection_FullMethodName = "/quwan.cloud.constack.v1alpha.AnyResourceService/DeleteCollection"
	AnyResourceService_MultiResource_FullMethodName    = "/quwan.cloud.constack.v1alpha.AnyResourceService/MultiResource"
)

// AnyResourceServiceClient is the client API for AnyResourceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AnyResourceServiceClient interface {
	// list
	List(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListResponse, error)
	// get
	Get(ctx context.Context, in *GetRequest, opts ...grpc.CallOption) (*GetResponse, error)
	// create
	Create(ctx context.Context, in *CreateRequest, opts ...grpc.CallOption) (*CreateResponse, error)
	// update
	Update(ctx context.Context, in *UpdateRequest, opts ...grpc.CallOption) (*UpdateResponse, error)
	// delete
	Delete(ctx context.Context, in *DeleteRequest, opts ...grpc.CallOption) (*DeleteResponse, error)
	// apply
	Apply(ctx context.Context, in *ApplyRequest, opts ...grpc.CallOption) (*ApplyResponse, error)
	// deleteCollection
	DeleteCollection(ctx context.Context, in *DeleteCollectionRequest, opts ...grpc.CallOption) (*DeleteCollectionResponse, error)
	// multiResource
	MultiResource(ctx context.Context, in *MultiResourceReq, opts ...grpc.CallOption) (*MultiResourceResp, error)
}

type anyResourceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAnyResourceServiceClient(cc grpc.ClientConnInterface) AnyResourceServiceClient {
	return &anyResourceServiceClient{cc}
}

func (c *anyResourceServiceClient) List(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListResponse, error) {
	out := new(ListResponse)
	err := c.cc.Invoke(ctx, AnyResourceService_List_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anyResourceServiceClient) Get(ctx context.Context, in *GetRequest, opts ...grpc.CallOption) (*GetResponse, error) {
	out := new(GetResponse)
	err := c.cc.Invoke(ctx, AnyResourceService_Get_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anyResourceServiceClient) Create(ctx context.Context, in *CreateRequest, opts ...grpc.CallOption) (*CreateResponse, error) {
	out := new(CreateResponse)
	err := c.cc.Invoke(ctx, AnyResourceService_Create_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anyResourceServiceClient) Update(ctx context.Context, in *UpdateRequest, opts ...grpc.CallOption) (*UpdateResponse, error) {
	out := new(UpdateResponse)
	err := c.cc.Invoke(ctx, AnyResourceService_Update_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anyResourceServiceClient) Delete(ctx context.Context, in *DeleteRequest, opts ...grpc.CallOption) (*DeleteResponse, error) {
	out := new(DeleteResponse)
	err := c.cc.Invoke(ctx, AnyResourceService_Delete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anyResourceServiceClient) Apply(ctx context.Context, in *ApplyRequest, opts ...grpc.CallOption) (*ApplyResponse, error) {
	out := new(ApplyResponse)
	err := c.cc.Invoke(ctx, AnyResourceService_Apply_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anyResourceServiceClient) DeleteCollection(ctx context.Context, in *DeleteCollectionRequest, opts ...grpc.CallOption) (*DeleteCollectionResponse, error) {
	out := new(DeleteCollectionResponse)
	err := c.cc.Invoke(ctx, AnyResourceService_DeleteCollection_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anyResourceServiceClient) MultiResource(ctx context.Context, in *MultiResourceReq, opts ...grpc.CallOption) (*MultiResourceResp, error) {
	out := new(MultiResourceResp)
	err := c.cc.Invoke(ctx, AnyResourceService_MultiResource_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnyResourceServiceServer is the server API for AnyResourceService service.
// All implementations must embed UnimplementedAnyResourceServiceServer
// for forward compatibility
type AnyResourceServiceServer interface {
	// list
	List(context.Context, *ListRequest) (*ListResponse, error)
	// get
	Get(context.Context, *GetRequest) (*GetResponse, error)
	// create
	Create(context.Context, *CreateRequest) (*CreateResponse, error)
	// update
	Update(context.Context, *UpdateRequest) (*UpdateResponse, error)
	// delete
	Delete(context.Context, *DeleteRequest) (*DeleteResponse, error)
	// apply
	Apply(context.Context, *ApplyRequest) (*ApplyResponse, error)
	// deleteCollection
	DeleteCollection(context.Context, *DeleteCollectionRequest) (*DeleteCollectionResponse, error)
	// multiResource
	MultiResource(context.Context, *MultiResourceReq) (*MultiResourceResp, error)
	mustEmbedUnimplementedAnyResourceServiceServer()
}

// UnimplementedAnyResourceServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAnyResourceServiceServer struct {
}

func (UnimplementedAnyResourceServiceServer) List(context.Context, *ListRequest) (*ListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedAnyResourceServiceServer) Get(context.Context, *GetRequest) (*GetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedAnyResourceServiceServer) Create(context.Context, *CreateRequest) (*CreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedAnyResourceServiceServer) Update(context.Context, *UpdateRequest) (*UpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedAnyResourceServiceServer) Delete(context.Context, *DeleteRequest) (*DeleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedAnyResourceServiceServer) Apply(context.Context, *ApplyRequest) (*ApplyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Apply not implemented")
}
func (UnimplementedAnyResourceServiceServer) DeleteCollection(context.Context, *DeleteCollectionRequest) (*DeleteCollectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCollection not implemented")
}
func (UnimplementedAnyResourceServiceServer) MultiResource(context.Context, *MultiResourceReq) (*MultiResourceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MultiResource not implemented")
}
func (UnimplementedAnyResourceServiceServer) mustEmbedUnimplementedAnyResourceServiceServer() {}

// UnsafeAnyResourceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AnyResourceServiceServer will
// result in compilation errors.
type UnsafeAnyResourceServiceServer interface {
	mustEmbedUnimplementedAnyResourceServiceServer()
}

func RegisterAnyResourceServiceServer(s grpc.ServiceRegistrar, srv AnyResourceServiceServer) {
	s.RegisterService(&AnyResourceService_ServiceDesc, srv)
}

func _AnyResourceService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnyResourceServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnyResourceService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnyResourceServiceServer).List(ctx, req.(*ListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnyResourceService_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnyResourceServiceServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnyResourceService_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnyResourceServiceServer).Get(ctx, req.(*GetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnyResourceService_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnyResourceServiceServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnyResourceService_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnyResourceServiceServer).Create(ctx, req.(*CreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnyResourceService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnyResourceServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnyResourceService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnyResourceServiceServer).Update(ctx, req.(*UpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnyResourceService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnyResourceServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnyResourceService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnyResourceServiceServer).Delete(ctx, req.(*DeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnyResourceService_Apply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnyResourceServiceServer).Apply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnyResourceService_Apply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnyResourceServiceServer).Apply(ctx, req.(*ApplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnyResourceService_DeleteCollection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCollectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnyResourceServiceServer).DeleteCollection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnyResourceService_DeleteCollection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnyResourceServiceServer).DeleteCollection(ctx, req.(*DeleteCollectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnyResourceService_MultiResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MultiResourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnyResourceServiceServer).MultiResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnyResourceService_MultiResource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnyResourceServiceServer).MultiResource(ctx, req.(*MultiResourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AnyResourceService_ServiceDesc is the grpc.ServiceDesc for AnyResourceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AnyResourceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "quwan.cloud.constack.v1alpha.AnyResourceService",
	HandlerType: (*AnyResourceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "List",
			Handler:    _AnyResourceService_List_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _AnyResourceService_Get_Handler,
		},
		{
			MethodName: "Create",
			Handler:    _AnyResourceService_Create_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _AnyResourceService_Update_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _AnyResourceService_Delete_Handler,
		},
		{
			MethodName: "Apply",
			Handler:    _AnyResourceService_Apply_Handler,
		},
		{
			MethodName: "DeleteCollection",
			Handler:    _AnyResourceService_DeleteCollection_Handler,
		},
		{
			MethodName: "MultiResource",
			Handler:    _AnyResourceService_MultiResource_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "quwan/cloud/constack/v1alpha/anyresource.proto",
}
