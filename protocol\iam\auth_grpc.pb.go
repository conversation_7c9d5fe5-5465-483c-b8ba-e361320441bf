// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.4
// source: protocol/iam/auth.proto

package iam

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AuthService_GetOwnUserinfo_FullMethodName    = "/iam.AuthService/GetOwnUserinfo"
	AuthService_Login_FullMethodName             = "/iam.AuthService/Login"
	AuthService_Logout_FullMethodName            = "/iam.AuthService/Logout"
	AuthService_CheckTokenIsBan_FullMethodName   = "/iam.AuthService/CheckTokenIsBan"
	AuthService_CheckAuthenticate_FullMethodName = "/iam.AuthService/CheckAuthenticate"
	AuthService_GenUserToken_FullMethodName      = "/iam.AuthService/GenUserToken"
)

// AuthServiceClient is the client API for AuthService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AuthServiceClient interface {
	GetOwnUserinfo(ctx context.Context, in *GetOwnUserinfoReq, opts ...grpc.CallOption) (*GetOwnUserinfoResp, error)
	Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error)
	Logout(ctx context.Context, in *LogoutReq, opts ...grpc.CallOption) (*LogoutResp, error)
	CheckTokenIsBan(ctx context.Context, in *CheckTokenIsBanReq, opts ...grpc.CallOption) (*CheckTokenIsBanResp, error)
	CheckAuthenticate(ctx context.Context, in *CheckAuthenticateReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GenUserToken(ctx context.Context, in *GenUserTokenReq, opts ...grpc.CallOption) (*LoginResp, error)
}

type authServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAuthServiceClient(cc grpc.ClientConnInterface) AuthServiceClient {
	return &authServiceClient{cc}
}

func (c *authServiceClient) GetOwnUserinfo(ctx context.Context, in *GetOwnUserinfoReq, opts ...grpc.CallOption) (*GetOwnUserinfoResp, error) {
	out := new(GetOwnUserinfoResp)
	err := c.cc.Invoke(ctx, AuthService_GetOwnUserinfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authServiceClient) Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error) {
	out := new(LoginResp)
	err := c.cc.Invoke(ctx, AuthService_Login_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authServiceClient) Logout(ctx context.Context, in *LogoutReq, opts ...grpc.CallOption) (*LogoutResp, error) {
	out := new(LogoutResp)
	err := c.cc.Invoke(ctx, AuthService_Logout_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authServiceClient) CheckTokenIsBan(ctx context.Context, in *CheckTokenIsBanReq, opts ...grpc.CallOption) (*CheckTokenIsBanResp, error) {
	out := new(CheckTokenIsBanResp)
	err := c.cc.Invoke(ctx, AuthService_CheckTokenIsBan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authServiceClient) CheckAuthenticate(ctx context.Context, in *CheckAuthenticateReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AuthService_CheckAuthenticate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authServiceClient) GenUserToken(ctx context.Context, in *GenUserTokenReq, opts ...grpc.CallOption) (*LoginResp, error) {
	out := new(LoginResp)
	err := c.cc.Invoke(ctx, AuthService_GenUserToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthServiceServer is the server API for AuthService service.
// All implementations must embed UnimplementedAuthServiceServer
// for forward compatibility
type AuthServiceServer interface {
	GetOwnUserinfo(context.Context, *GetOwnUserinfoReq) (*GetOwnUserinfoResp, error)
	Login(context.Context, *LoginReq) (*LoginResp, error)
	Logout(context.Context, *LogoutReq) (*LogoutResp, error)
	CheckTokenIsBan(context.Context, *CheckTokenIsBanReq) (*CheckTokenIsBanResp, error)
	CheckAuthenticate(context.Context, *CheckAuthenticateReq) (*emptypb.Empty, error)
	GenUserToken(context.Context, *GenUserTokenReq) (*LoginResp, error)
	mustEmbedUnimplementedAuthServiceServer()
}

// UnimplementedAuthServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAuthServiceServer struct {
}

func (UnimplementedAuthServiceServer) GetOwnUserinfo(context.Context, *GetOwnUserinfoReq) (*GetOwnUserinfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOwnUserinfo not implemented")
}
func (UnimplementedAuthServiceServer) Login(context.Context, *LoginReq) (*LoginResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedAuthServiceServer) Logout(context.Context, *LogoutReq) (*LogoutResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logout not implemented")
}
func (UnimplementedAuthServiceServer) CheckTokenIsBan(context.Context, *CheckTokenIsBanReq) (*CheckTokenIsBanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTokenIsBan not implemented")
}
func (UnimplementedAuthServiceServer) CheckAuthenticate(context.Context, *CheckAuthenticateReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAuthenticate not implemented")
}
func (UnimplementedAuthServiceServer) GenUserToken(context.Context, *GenUserTokenReq) (*LoginResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenUserToken not implemented")
}
func (UnimplementedAuthServiceServer) mustEmbedUnimplementedAuthServiceServer() {}

// UnsafeAuthServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthServiceServer will
// result in compilation errors.
type UnsafeAuthServiceServer interface {
	mustEmbedUnimplementedAuthServiceServer()
}

func RegisterAuthServiceServer(s grpc.ServiceRegistrar, srv AuthServiceServer) {
	s.RegisterService(&AuthService_ServiceDesc, srv)
}

func _AuthService_GetOwnUserinfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOwnUserinfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServiceServer).GetOwnUserinfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthService_GetOwnUserinfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServiceServer).GetOwnUserinfo(ctx, req.(*GetOwnUserinfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthService_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServiceServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthService_Login_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServiceServer).Login(ctx, req.(*LoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthService_Logout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogoutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServiceServer).Logout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthService_Logout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServiceServer).Logout(ctx, req.(*LogoutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthService_CheckTokenIsBan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTokenIsBanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServiceServer).CheckTokenIsBan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthService_CheckTokenIsBan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServiceServer).CheckTokenIsBan(ctx, req.(*CheckTokenIsBanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthService_CheckAuthenticate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAuthenticateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServiceServer).CheckAuthenticate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthService_CheckAuthenticate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServiceServer).CheckAuthenticate(ctx, req.(*CheckAuthenticateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthService_GenUserToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenUserTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServiceServer).GenUserToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthService_GenUserToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServiceServer).GenUserToken(ctx, req.(*GenUserTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AuthService_ServiceDesc is the grpc.ServiceDesc for AuthService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AuthService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "iam.AuthService",
	HandlerType: (*AuthServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOwnUserinfo",
			Handler:    _AuthService_GetOwnUserinfo_Handler,
		},
		{
			MethodName: "Login",
			Handler:    _AuthService_Login_Handler,
		},
		{
			MethodName: "Logout",
			Handler:    _AuthService_Logout_Handler,
		},
		{
			MethodName: "CheckTokenIsBan",
			Handler:    _AuthService_CheckTokenIsBan_Handler,
		},
		{
			MethodName: "CheckAuthenticate",
			Handler:    _AuthService_CheckAuthenticate_Handler,
		},
		{
			MethodName: "GenUserToken",
			Handler:    _AuthService_GenUserToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protocol/iam/auth.proto",
}
