package tt

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"52tt.com/cicd/pkg/cloud"

	keda "github.com/kedacore/keda/v2/apis/keda/v1alpha1"
	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/tools/set"
	"52tt.com/cicd/protocol/deploy"
	"52tt.com/cicd/services/migrate/internal/deploy_config"
	"52tt.com/cicd/services/migrate/internal/deploy_config/common"
	db "52tt.com/cicd/services/migrate/pkg/database"
)

func ExportAllConfig(envs []string, services []string, isUpdate bool, targetProjectName string) error {
	for _, env := range envs {
		err := ExportConfig(env, services, isUpdate, targetProjectName)
		if err != nil {
			return err
		}
	}
	return nil
}

func ManualExportDeployConfig(parameter *deploy_config.MigrationParameter) error {
	apps := parameter.Names
	for _, env := range parameter.Envs {
		if err := ExportConfig(env, apps, false, ""); err != nil {
			return err
		}
	}
	return nil
}

func notEqual(name string, apps []string) bool {
	if len(apps) > 0 {
		uniqueApps := set.Of(apps)
		return !uniqueApps.Exists(name)
	}
	return false
}

func ExportConfig(env string, apps []string, isUpdate bool, targetProjectName string) error {
	var configs []deploy_config.UnitDeployConfig
	k8sMap := map[string][]int{
		db.Dev:  {2},
		db.Test: {16},
		db.Prod: {4, 43},
	}
	deployConfigs, err := deploy_config.GetProjectDeployConfig(env, k8sMap)
	if err != nil {
		return err
	}
	for _, dc := range deployConfigs {
		if dc.Unit.App.Team.Name != "quicksilver" || dc.Unit.Status != "online" {
			continue
		}
		invailCluster := map[string]struct{}{
			"tt-testing":            {},
			"k8s-hw-gz-tt-ci":       {},
			"ucloud生产集群prod-bj2-01": {},
			"ucloud staging环境(tt)":  {},
		}
		if _, ok := invailCluster[dc.K8sEnv.AssetsK8sCluster.Name]; ok {
			continue
		}
		if notEqual(dc.Unit.Name, apps) {
			continue
		}
		configs = append(configs, deploy_config.UnitDeployConfig{
			Resource:       dc.Resource,
			Values:         dc.Values,
			Default:        dc.Default,
			K8sName:        dc.K8sEnv.AssetsK8sCluster.Name,
			NameSpace:      dc.K8sEnv.NameSpace,
			K8sDescription: dc.K8sEnv.AssetsK8sCluster.Description,
			K8sPrivateId:   dc.K8sEnv.AssetsK8sCluster.PrivateId,
			UnitName:       dc.Unit.Name,
			ObjectId:       dc.Unit.ObjectId,
			TeamName:       dc.Unit.App.Team.Name,
			ChartName:      dc.Chart.Name,
			ChartUrl:       dc.Chart.Url,
			ChartVersion:   dc.Chart.Version,
			ChartType:      dc.Chart.CharType,
			UnitId:         dc.UnitId,
			K8sEnvId:       dc.K8sEnvId,
		})
	}
	//err = deploy_config.CreateConfig(configs)
	err = PullConfig(configs, isUpdate, targetProjectName)
	if err != nil {
		return err
	}
	return nil
}

func PullConfig(configs []deploy_config.UnitDeployConfig, isUpdate bool, targetProjectName string) error {
	dbEnv := db.Prod
	//configs, err := GetUnitDeployConfig()
	//if err != nil {
	//  return err
	//}
	hpaMap := make(map[string]HPA)
	for _, conf := range configs {
		var tempHpa HPA
		if conf.Resource == "HPA" {
			json.Unmarshal(conf.Values, &tempHpa)
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			hpaMap[unitKey] = tempHpa
		}
	}
	log.Infof("hpaMap: %v", hpaMap)

	count := 0
	var unitNames []string
	for _, conf := range configs {
		if conf.ChartName == "generalServer" {
			var err error
			var generalServer GeneralServer
			json.Unmarshal(conf.Default, &generalServer)
			json.Unmarshal(conf.Values, &generalServer)
			if generalServer.Wasm.Enabled {
				fmt.Println(conf.UnitName)
			}
			appBaseConfig, err := HandAppBaseConfig(generalServer)
			if err != nil {
				return err
			}
			appAdvancedConfig, err := HandAppAdvancedConfig(generalServer)
			if err != nil {
				return err
			}
			unitKey := fmt.Sprintf("%v-%v", conf.UnitId, conf.K8sEnvId)
			traitConfig, err := HandTraitConfig(generalServer, unitKey, hpaMap)
			if err != nil {
				return err
			}
			teamName := conf.TeamName
			if targetProjectName != "" {
				teamName = targetProjectName
			}
			appId, err := common.FindAppIdBy(dbEnv, teamName, conf.UnitName)
			if err != nil {
				return err
			}
			if appId == 0 {
				continue
			}
			metadata := deploy_config.DeployMetadata{
				Env:       GetEnvEnum(conf.K8sName),
				EnvTarget: 1, //默认基准环境
				Cluster:   getClusterName(conf.K8sName),
				Namespace: conf.NameSpace,
				AppID:     appId,
				//AppID:    1336,
				AppName:  conf.UnitName,
				ConfigID: 0,
			}
			err = deploy_config.GetDeployConfigLastVersion(dbEnv, &metadata)
			if err != nil {
				return err
			}
			// 只验证数据转换是否可行，不更新
			if !isUpdate {
				log.Infof("ignore update, unitId: %v, unitName: %v", conf.UnitId, conf.UnitName)
				continue
			}
			version := 1
			if metadata.ID != 0 {
				if metadata.Config != nil {
					version = metadata.Config.Version + 1
					//continue
				}
			} else {
				err = deploy_config.CreateDeployMetadata(dbEnv, &metadata)
				if err != nil {
					return err
				}
			}
			deployConfig := deploy_config.DeployConfig{
				AppBasicConfig:    appBaseConfig,
				AppAdvancedConfig: appAdvancedConfig,
				TraitConfig:       traitConfig,
				Version:           version,
				CreatedBy:         113, //创建用户id
				//CreatedBy: 156, //创建用户id
				CreatedByChineseName: "陈实",
				//CreatedByChineseName: "李斌",
				CreatedByEmployeeNo: "T1695",
				//CreatedByEmployeeNo: "T1684",
				TemplateID: 0,
				MetadataID: metadata.ID,
			}
			deployConfig.ConfigType = 1
			if generalServer.Deploy.Stateful {
				deployConfig.ConfigType = 3
			}
			err = deploy_config.CreateDeployConfig(dbEnv, &deployConfig)
			if err != nil {
				return err
			}
			metadata.ConfigID = deployConfig.ID
			err = deploy_config.UpdateDeployMetadata(dbEnv, &metadata)
			if err != nil {
				return err
			}
			count++
			unitNames = append(unitNames, conf.UnitName)
		}
	}
	log.Infof("count: %d", count)
	fmt.Println(unitNames)
	return nil
}

func HandAppBaseConfig(generalServer GeneralServer) ([]byte, error) {
	res := deploy.AppBasicConfig{
		NetworkType:  "",
		NetworkPorts: []*deploy.AppBasicConfig_Port{},
		Annotations:  []*deploy.Pair{},
		Envs:         []*deploy.Pair{},
		Commands:     []string{},
		Configs:      []*deploy.AppBasicConfig_Config{},
	}

	//网络配置
	res.NetworkType = generalServer.Service.Type
	configName := "admin"
	if generalServer.Service.StartReportMetrics {
		configName = "http-admin"
	}
	res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
		Name: configName, InternalPort: 8078, ExternalPort: 8078})
	if generalServer.Service.Enabled {
		res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
			Name:         generalServer.Service.Name,
			InternalPort: generalServer.Service.TargetPort,
			ExternalPort: generalServer.Service.Port,
		})
	}
	if len(generalServer.Service.MultiPorts) > 0 {
		for _, port := range generalServer.Service.MultiPorts {
			res.NetworkPorts = append(res.NetworkPorts, &deploy.AppBasicConfig_Port{
				Name:         port.Name,
				InternalPort: port.TargetPort,
				ExternalPort: port.Port,
			})
		}
	}
	//命令参数
	for _, command := range generalServer.Deploy.Command {
		res.Commands = append(res.Commands, command)
	}
	//环境变量
	for _, env := range generalServer.Global.Deploy.Env {
		if env.Name != "" && env.Value != "" {
			res.Envs = append(res.Envs, &deploy.Pair{Key: env.Name, Value: env.Value})
		}
	}
	if generalServer.Service.StartReportMetrics {
		res.Envs = append(res.Envs, &deploy.Pair{Key: "RETCODE_METRICS_ENV", Value: "ENABLE"})
	}
	//配置文件
	for fileName, fileContent := range generalServer.ConfigFiles {
		res.Configs = append(res.Configs, &deploy.AppBasicConfig_Config{DirPath: "/config", FileName: fileName, Content: fileContent})
	}

	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func builderHandler() *deploy.HealthCheck_HealthCheckHandler {
	return &deploy.HealthCheck_HealthCheckHandler{
		Type: "",
		Exec: &deploy.ExecAction{
			Command: []string{},
		},
		TcpSocket: &deploy.TCPSocketAction{},
		HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
		Grpc:      &deploy.GRPCAction{},
		Probe: &deploy.HealthCheck_Probe{
			InitialDelaySeconds: 0,
			PeriodSeconds:       10,
			TimeoutSeconds:      1,
			SuccessThreshold:    1,
			FailureThreshold:    3,
		},
	}
}

func HandAppAdvancedConfig(generalServer GeneralServer) ([]byte, error) {
	res := deploy.AppAdvancedConfig{
		Labels:      []*deploy.Pair{},
		Annotations: []*deploy.Pair{},
		HostAliases: []*deploy.HostAlias{},
		HealthCheck: &deploy.HealthCheck{
			ReadinessProbe: builderHandler(),
			LivenessProbe:  builderHandler(),
			StartupProbe:   builderHandler(),
			Types:          []string{},
		},
		ServiceAccountName: "default",
		Lifecycle: &deploy.Lifecycle{
			PostStart: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			PreStop: &deploy.Lifecycle_LifecycleHandler{
				Type: "",
				Exec: &deploy.ExecAction{
					Command: []string{},
				},
				HttpGet:   &deploy.HTTPGetAction{Headers: []*deploy.Pair{}},
				TcpSocket: &deploy.TCPSocketAction{},
			},
			Types: []string{},
		},
	}
	//标签
	expectedLabel := map[string]bool{
		"uuid":       true,
		"cluster_id": true,
		"env":        true,
		"lang":       true,
		"type":       true,
	}
	for key, value := range generalServer.Labels {
		if _, ok := expectedLabel[key]; ok {
			continue
		}
		res.Labels = append(res.Labels, &deploy.Pair{Key: key, Value: value})
	}
	//注解
	for key, value := range generalServer.Deploy.Annotations {
		if key == "sidecar.istio.io/proxyCPU" || key == "sidecar.istio.io/proxyMemory" ||
			key == "sidecar.istio.io/proxyCPULimit" || key == "sidecar.istio.io/proxyMemoryLimit" {
			continue
		}
		res.Annotations = append(res.Annotations, &deploy.Pair{Key: key, Value: value})
	}
	if generalServer.Service.StartReportMetrics {
		res.Annotations = append(res.Annotations, &deploy.Pair{Key: "app.kubernetes.io/tt-monitor-of", Value: "metrics"})
	}
	//就绪探针
	if generalServer.Probe.Enabled && generalServer.TempNamespace.Probe.ReadinessProbe.Enabled {
		res.HealthCheck.Types = append(res.HealthCheck.Types, "readinessProbe")
		res.HealthCheck.ReadinessProbe.Type = "exec"
		if len(generalServer.Probe.ReadinessProbe.Exec.Command) > 0 {
			for _, command := range generalServer.Probe.ReadinessProbe.Exec.Command {
				res.HealthCheck.ReadinessProbe.Exec.Command = append(res.HealthCheck.ReadinessProbe.Exec.Command, command)
			}
		}
		res.HealthCheck.ReadinessProbe.Probe.PeriodSeconds = generalServer.Probe.ReadinessProbe.PeriodSeconds
		res.HealthCheck.ReadinessProbe.Probe.InitialDelaySeconds = generalServer.Probe.ReadinessProbe.InitialDelaySeconds
		res.HealthCheck.ReadinessProbe.Probe.SuccessThreshold = generalServer.Probe.ReadinessProbe.SuccessThreshold
		res.HealthCheck.ReadinessProbe.Probe.FailureThreshold = generalServer.Probe.ReadinessProbe.FailureThreshold
		res.HealthCheck.ReadinessProbe.Probe.TimeoutSeconds = generalServer.Probe.ReadinessProbe.TimeoutSeconds
	}
	//挂载配置
	//全局
	if len(generalServer.Global.Volumes) > 0 || len(generalServer.Volumes) > 0 {
		res.MountConfig = &deploy.MountConfig{}
		res.MountConfig.Volumes = []*deploy.Volume{}
	}
	for _, volume := range generalServer.Global.Volumes {
		res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
			Type: "hostPath", VolumeName: volume.Name, RefName: volume.HostPath.Path, ReadOnly: true,
		})
	}
	if len(generalServer.Global.VolumeMounts) > 0 {
		res.MountConfig.VolumeMounts = []*deploy.VolumeMount{}
		for _, volumeMount := range generalServer.Global.VolumeMounts {
			res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
				VolumeName: volumeMount.Name,
				MountPoint: volumeMount.MountPath,
			})
		}
	}
	//服务的挂载配置
	for _, volume := range generalServer.Volumes {
		res.MountConfig.Volumes = append(res.MountConfig.Volumes, &deploy.Volume{
			Type: "secret", VolumeName: volume.Name, RefName: volume.Secret.SecretName, ReadOnly: true,
		})
	}
	if len(generalServer.VolumeMounts) < 0 && res.MountConfig.VolumeMounts == nil {
		res.MountConfig.VolumeMounts = []*deploy.VolumeMount{}
	}
	for _, volumeMount := range generalServer.VolumeMounts {
		res.MountConfig.VolumeMounts = append(res.MountConfig.VolumeMounts, &deploy.VolumeMount{
			VolumeName: volumeMount.Name,
			MountPoint: volumeMount.MountPath,
		})
	}
	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func HandTraitConfig(generalServer GeneralServer, unitKey string, hpa map[string]HPA) ([]byte, error) {
	var res deploy.TraitConfig
	res.ScalingConfig = &deploy.ScalingConfig{}
	replicas, _ := strconv.Atoi(generalServer.Replicas)
	res.ScalingConfig.Replicas = int32(replicas)
	res.ScalingConfig.Type = "replicas"

	//resource
	res.ResourceConstraints = &deploy.ResourceConstraints{Resources: &deploy.Resources{}}
	cpuReq := generalServer.Resources.Requests.Cpu
	if strings.Contains(cpuReq, "m") || strings.Contains(cpuReq, "M") {
		//单位为m
		reqCpu, _ := strconv.ParseFloat(cpuReq[0:len(cpuReq)-1], 32)
		res.ResourceConstraints.Resources.RequestCpu = float32(reqCpu / 1000)
	} else {
		//单位为核
		reqCpu, _ := strconv.ParseFloat(cpuReq, 32)
		res.ResourceConstraints.Resources.RequestCpu = float32(reqCpu)
	}

	reqMem, err := strconv.ParseFloat(generalServer.Resources.Requests.Memory, 32)
	if strings.Contains(generalServer.Resources.Requests.Memory, "Gi") {
		res.ResourceConstraints.Resources.RequestMemory = float32(reqMem * 1024)
	} else {
		res.ResourceConstraints.Resources.RequestMemory = float32(reqMem)
	}

	cpuLim := generalServer.Resources.Limits.Cpu
	if strings.Contains(cpuLim, "m") || strings.Contains(cpuLim, "M") {
		//单位为m
		limCpu, _ := strconv.ParseFloat(cpuLim[0:len(cpuLim)-1], 32)
		res.ResourceConstraints.Resources.LimitCpu = float32(limCpu / 1000)
	} else {
		//单位为核
		limCpu, _ := strconv.ParseFloat(cpuLim, 32)
		res.ResourceConstraints.Resources.LimitCpu = float32(limCpu)
	}

	limMem, err := strconv.ParseFloat(generalServer.Resources.Limits.Memory, 32)
	if strings.Contains(generalServer.Resources.Limits.Memory, "Gi") {
		res.ResourceConstraints.Resources.LimitMemory = float32(limMem * 1024)
	} else {
		res.ResourceConstraints.Resources.LimitMemory = float32(limMem)
	}

	//sidecar注入
	var reqCpu, reqMem1, limCpu, limMem1 float64
	if generalServer.SidecarEnabled {
		res.ResourceConstraints.Sidecar = &deploy.ResourceConstraints_Sidecar{}
		res.ResourceConstraints.Sidecar.Enabled = true
		reqCpu, _ = strconv.ParseFloat(generalServer.CustomIstioSidecar.ProxyCPU, 32)
		res.ResourceConstraints.Sidecar.RequestCpu = float32(reqCpu)
		reqMem1, _ = strconv.ParseFloat(generalServer.CustomIstioSidecar.ProxyMemory, 32)
		res.ResourceConstraints.Sidecar.RequestMemory = float32(reqMem1)
		limCpu, _ = strconv.ParseFloat(generalServer.CustomIstioSidecar.ProxyCPULimit, 32)
		res.ResourceConstraints.Sidecar.LimitCpu = float32(limCpu)
		limMem1, _ = strconv.ParseFloat(generalServer.CustomIstioSidecar.ProxyMemoryLimit, 32)
		res.ResourceConstraints.Sidecar.LimitMemory = float32(limMem1)
	} else {
		var has bool
		for key, _ := range generalServer.Deploy.Annotations {
			if key == "sidecar.istio.io/proxyCPU" || key == "sidecar.istio.io/proxyMemory" ||
				key == "sidecar.istio.io/proxyCPULimit" || key == "sidecar.istio.io/proxyMemoryLimit" {
				has = true
			}
		}
		//cpu
		if value, ok := generalServer.Deploy.Annotations["sidecar.istio.io/proxyCPU"]; ok {
			if strings.Contains(value, "m") {
				reqCpu, _ = strconv.ParseFloat(value[0:len(value)-1], 32)
			} else {
				reqCpu, _ = strconv.ParseFloat(value, 32)
			}
		} else if has {
			reqCpu = 0.1
		}
		//memory
		if value, ok := generalServer.Deploy.Annotations["sidecar.istio.io/proxyMemory"]; ok {
			reqMem1, _ = strconv.ParseFloat(value[0:len(value)-2], 32)
			if strings.Contains(value, "Gi") {
				reqMem1 = reqMem1 * 1024
			}
		} else if has {
			reqMem1 = 400
		}
		//cpuLimit
		if value, ok := generalServer.Deploy.Annotations["sidecar.istio.io/proxyCPULimit"]; ok {
			if strings.Contains(value, "m") {
				limCpu, _ = strconv.ParseFloat(value[0:len(value)-1], 32)
			} else {
				limCpu, _ = strconv.ParseFloat(value, 32)
			}
		} else if has {
			limCpu = 1
		}
		//memoryLimit
		if value, ok := generalServer.Deploy.Annotations["sidecar.istio.io/proxyMemoryLimit"]; ok {
			limMem1, _ = strconv.ParseFloat(value[0:len(value)-2], 32)
			if strings.Contains(value, "Gi") {
				limMem1 = limMem1 * 1024
			}
		} else if has {
			limMem1 = 1500
		}
		if has {
			res.ResourceConstraints.Sidecar = &deploy.ResourceConstraints_Sidecar{}
			res.ResourceConstraints.Sidecar.Enabled = true
			res.ResourceConstraints.Sidecar.RequestCpu = float32(reqCpu)
			res.ResourceConstraints.Sidecar.RequestMemory = float32(reqMem1)
			res.ResourceConstraints.Sidecar.LimitCpu = float32(limCpu)
			res.ResourceConstraints.Sidecar.LimitMemory = float32(limMem1)
		}
	}
	res.AdvancedConfig = &deploy.TraitAdvancedConfig{}
	//熔断
	if generalServer.Wasm.Enabled {
		res.AdvancedConfig.CircuitBreaker = &deploy.CircuitBreaker{}
		res.AdvancedConfig.CircuitBreaker.Enabled = true
	}

	//限流
	if generalServer.EnvoyFilter.RateLimit.Global.Enabled {
		res.AdvancedConfig.RateLimiting = &deploy.RateLimiting{}
		res.AdvancedConfig.RateLimiting.Enabled = true
		res.AdvancedConfig.RateLimiting.Port = generalServer.EnvoyFilter.RateLimit.Global.PortNumber
		timeOut, _ := strconv.Atoi(generalServer.EnvoyFilter.RateLimit.Global.Timeout[0 : len(generalServer.EnvoyFilter.RateLimit.Global.Timeout)-2])
		res.AdvancedConfig.RateLimiting.Timeout = int32(timeOut)

	}
	//HPA
	if value, isExist := hpa[unitKey]; isExist && (value.Autoscale.CPU.Enabled || value.Autoscale.Memory.Enabled) {
		res.ScalingConfig.Type = "multiHPA"
		res.ScalingConfig.MultiHpa = &deploy.ScalingConfig_MultiHPA{Max: value.Autoscale.Max, Min: value.Autoscale.Min}
		if value.Autoscale.CPU.Enabled {
			res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "cpu")
			res.ScalingConfig.MultiHpa.CpuUtilization = value.Autoscale.CPU.TargetAverageUtilization
		}
		if value.Autoscale.Memory.Enabled {
			res.ScalingConfig.MultiHpa.Types = append(res.ScalingConfig.MultiHpa.Types, "memory")
			res.ScalingConfig.MultiHpa.MemoryUtilization = value.Autoscale.Memory.TargetAverageUtilization
		}
	}
	resByte, err := json.Marshal(res)
	if err != nil {
		return nil, err
	}
	return resByte, nil
}

func getClusterName(name string) string {
	if name == "k8s-hw-bj-1-stage" {
		return "k8s-hw-bj-1-stage"
	} else if name == "k8s-tc-bj-1-prod" {
		return "k8s-tc-bj-1-prod"
	} else if name == "华为生产集群(tt)" {
		return "k8s-hw-bj-tt-prod-00"
	} else if name == "k8s-tc-bj-1-test" {
		return "k8s-tc-bj-1-test"
	}
	return "k8s-hw-gz-tt-dev"
}

func GetEnvEnum(k8sName string) int8 {
	switch k8sName {
	case "k8s-hw-gz-tt-dev":
		return int8(1)
	case "k8s-tc-bj-1-test":
		return int8(2)
	case "k8s-hw-bj-1-stage":
		return int8(3)
	default:
		return 4
	}
}

func isContainUnit(unit, units string) bool {
	var myMap map[string]bool
	myMap = make(map[string]bool)
	keys := strings.Split(units, ",")
	for _, key := range keys {
		myMap[key] = true
	}
	if _, ok := myMap[unit]; ok {
		return true
	}
	return false
}

func RepairTTHPA(isUpdate, units string) error {
	dbEnv := "prod"
	unitArray := strings.Split(units, ",")
	deployConfigs, err := deploy_config.GetDeployConfig(dbEnv)
	if err != nil {
		return err
	}
	var configs []deploy_config.UnitDeployConfig
	for _, dc := range deployConfigs {
		if dc.Unit.App.Team.Name != "quicksilver" || dc.Unit.Status != "online" {
			continue
		}
		invailCluster := map[string]struct{}{
			"tt-testing":            {},
			"k8s-hw-gz-tt-ci":       {},
			"ucloud生产集群prod-bj2-01": {},
			"ucloud staging环境(tt)":  {},
		}
		if _, ok := invailCluster[dc.K8sEnv.AssetsK8sCluster.Name]; ok {
			continue
		}
		if !isContainUnit(dc.Unit.Name, units) {
			continue
		}
		configs = append(configs, deploy_config.UnitDeployConfig{
			Resource:       dc.Resource,
			Values:         dc.Values,
			Default:        dc.Default,
			K8sName:        dc.K8sEnv.AssetsK8sCluster.Name,
			NameSpace:      dc.K8sEnv.NameSpace,
			K8sDescription: dc.K8sEnv.AssetsK8sCluster.Description,
			K8sPrivateId:   dc.K8sEnv.AssetsK8sCluster.PrivateId,
			UnitName:       dc.Unit.Name,
			ObjectId:       dc.Unit.ObjectId,
			TeamName:       dc.Unit.App.Team.Name,
			ChartName:      dc.Chart.Name,
			ChartUrl:       dc.Chart.Url,
			ChartVersion:   dc.Chart.Version,
			ChartType:      dc.Chart.CharType,
			UnitId:         dc.UnitId,
			K8sEnvId:       dc.K8sEnvId,
		})
	}
	hpa := make(map[string]HPA)
	for _, conf := range configs {
		var tempHpa HPA
		if conf.Resource == "HPA" {
			json.Unmarshal(conf.Values, &tempHpa)
			k8sName := getClusterName(conf.K8sName)
			hpa[conf.UnitName+"_"+k8sName] = tempHpa
		}
	}
	var errInfo []string
	var updateInfo []string
	for _, unit := range unitArray {
		ttDeployConfigs, err := deploy_config.FindDeployMetadataWithConfigBy("prod", 22, unit)
		if err != nil {
			errInfo = append(errInfo, fmt.Sprintf("%v", err))
			continue
		}
		for metadata, deployConfigs := range ttDeployConfigs {
			for _, deployConfig := range deployConfigs {
				var traitConfig deploy.TraitConfig
				err := json.Unmarshal(deployConfig.TraitConfig, &traitConfig)
				if err != nil {
					return err
				}
				curHpa, ok := hpa[unit+"_"+metadata]
				if !ok {
					errInfo = append(errInfo, fmt.Sprintf("当前服务[%s]的集群[%s]找不到对应的hpa", unit, metadata))
					continue
				}
				source := fmt.Sprintf("当前服务[%s]的集群[%s]的HPA配置为 %v:", unit, metadata, traitConfig.ScalingConfig.Hpa)
				traitConfig.ScalingConfig.Type = "hpa"
				traitConfig.ScalingConfig.Replicas = 1
				traitConfig.ScalingConfig.Hpa = &deploy.ScalingConfig_HPA{Max: curHpa.Autoscale.Max, Min: curHpa.Autoscale.Min}
				if curHpa.Autoscale.CPU.Enabled {
					traitConfig.ScalingConfig.Hpa.Types = append(traitConfig.ScalingConfig.Hpa.Types, "cpu")
					traitConfig.ScalingConfig.Hpa.CpuUtilization = curHpa.Autoscale.CPU.TargetAverageUtilization
				}
				if curHpa.Autoscale.Memory.Enabled {
					traitConfig.ScalingConfig.Hpa.Types = append(traitConfig.ScalingConfig.Hpa.Types, "memory")
					traitConfig.ScalingConfig.Hpa.MemoryUtilization = curHpa.Autoscale.Memory.TargetAverageUtilization
				}
				dest := fmt.Sprintf("-->更改后的HPA配置为 %v:", traitConfig.ScalingConfig.Hpa)
				updateInfo = append(updateInfo, source+dest)
				configByte, err := json.Marshal(traitConfig)
				if err != nil {
					return err
				}
				if isUpdate == "1" {
					err = deploy_config.UpdateDeployConfigTraitConfig("prod", deployConfig.ID, configByte)
					if err != nil {
						return err
					}
				}
			}
		}
	}
	for _, errI := range errInfo {
		fmt.Println(errI)
	}
	for _, update := range updateInfo {
		fmt.Println(update)
	}
	return nil
}

//func RepairOnlineHPA(isUpdate, units string, projectId int64) error {
//	cloudRpcClient, err := cloud.NewGRPCClient("cloud.ttyuyin.com:8100", "Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663")
//	if err != nil {
//		return err
//	}
//	dbEnv := "prod"
//	unitArray := strings.Split(units, "@")
//	deployConfigs, err := deploy_config.GetDeployConfig(dbEnv)
//	if err != nil {
//		return err
//	}
//	var configs []deploy_config.UnitDeployConfig
//	for _, dc := range deployConfigs {
//		if dc.Unit.App.Team.Name != "quicksilver" {
//			continue
//		}
//		invailCluster := map[string]struct{}{
//			"tt-testing":            {},
//			"k8s-hw-gz-tt-ci":       {},
//			"ucloud生产集群prod-bj2-01": {},
//			"ucloud staging环境(tt)":  {},
//		}
//		if _, ok := invailCluster[dc.K8sEnv.AssetsK8sCluster.Name]; ok {
//			continue
//		}
//		if !isContainUnit(dc.Unit.Name, units) {
//			continue
//		}
//		configs = append(configs, deploy_config.UnitDeployConfig{
//			Resource:       dc.Resource,
//			Values:         dc.Values,
//			Default:        dc.Default,
//			K8sName:        dc.K8sEnv.AssetsK8sCluster.Name,
//			NameSpace:      dc.K8sEnv.NameSpace,
//			K8sDescription: dc.K8sEnv.AssetsK8sCluster.Description,
//			K8sPrivateId:   dc.K8sEnv.AssetsK8sCluster.PrivateId,
//			UnitName:       dc.Unit.Name,
//			ObjectId:       dc.Unit.ObjectId,
//			TeamName:       dc.Unit.App.Team.Name,
//			ChartName:      dc.Chart.Name,
//			ChartUrl:       dc.Chart.Url,
//			ChartVersion:   dc.Chart.Version,
//			ChartType:      dc.Chart.CharType,
//			UnitId:         dc.UnitId,
//			K8sEnvId:       dc.K8sEnvId,
//		})
//	}
//	hpa := make(map[string]HPA)
//	for _, conf := range configs {
//		var tempHpa HPA
//		if conf.Resource == "HPA" {
//			json.Unmarshal(conf.Values, &tempHpa)
//			k8sName := getClusterName(conf.K8sName)
//			hpa[conf.UnitName+"_"+k8sName] = tempHpa
//		}
//	}
//	var errInfo []string
//	var updateInfo []string
//	//var noUpdateInfo []string
//	var Updatereq []string
//	repair := make(map[string]struct{})
//	//fmt.Println(unitArray)
//	for _, unitInfo := range unitArray {
//		unit := strings.Split(unitInfo, ",")[0]
//		cluster := strings.Split(unitInfo, ",")[1]
//		//fmt.Println(unit, cluster)
//		ttDeployConfigs, err := deploy_config.FindDeployMetadataWithConfigBy("prod", projectId, unit)
//		if err != nil {
//			errInfo = append(errInfo, fmt.Sprintf("%v", err))
//			continue
//		}
//		fmt.Println(len(ttDeployConfigs))
//		for metadataInfo, deployConfigss := range ttDeployConfigs {
//			metadata := strings.Split(metadataInfo, ":")[0]
//			namespace := strings.Split(metadataInfo, ":")[1]
//			//if upInfo, isUp := checkHpaIsUpdated(unit, metadata, deployConfigs); isUp {
//			//	noUpdateInfo = append(noUpdateInfo, upInfo)
//			//	continue
//			//}
//			fmt.Println(len(deployConfigss))
//			for _, deployConfig := range deployConfigss {
//				var traitConfig deploy.TraitConfig
//				err := json.Unmarshal(deployConfig.TraitConfig, &traitConfig)
//				if err != nil {
//					return err
//				}
//				curHpa, ok := hpa[unit+"_"+metadata]
//				var min, max, cpu, mem int32
//				flag := "否"
//				min = traitConfig.ScalingConfig.Hpa.Min
//				max = traitConfig.ScalingConfig.Hpa.Max
//				cpu = traitConfig.ScalingConfig.Hpa.CpuUtilization
//				mem = traitConfig.ScalingConfig.Hpa.MemoryUtilization
//				traitConfig.ScalingConfig.Type = "hpa"
//				traitConfig.ScalingConfig.Replicas = 1
//				traitConfig.ScalingConfig.Hpa = &deploy.ScalingConfig_HPA{Max: curHpa.Autoscale.Max, Min: curHpa.Autoscale.Min}
//				if curHpa.Autoscale.CPU.Enabled {
//					traitConfig.ScalingConfig.Hpa.Types = append(traitConfig.ScalingConfig.Hpa.Types, "cpu")
//					traitConfig.ScalingConfig.Hpa.CpuUtilization = curHpa.Autoscale.CPU.TargetAverageUtilization
//				}
//				if curHpa.Autoscale.Memory.Enabled {
//					traitConfig.ScalingConfig.Hpa.Types = append(traitConfig.ScalingConfig.Hpa.Types, "memory")
//					traitConfig.ScalingConfig.Hpa.MemoryUtilization = curHpa.Autoscale.Memory.TargetAverageUtilization
//				}
//				if min != traitConfig.ScalingConfig.Hpa.Min ||
//					max != traitConfig.ScalingConfig.Hpa.Max ||
//					cpu != traitConfig.ScalingConfig.Hpa.CpuUtilization ||
//					mem != traitConfig.ScalingConfig.Hpa.MemoryUtilization {
//					flag = "是"
//				}
//				source := fmt.Sprintf("%s@%s@%s@%d@%d@%d@%d", unit, metadata, flag, min, max, cpu, mem)
//				dest := fmt.Sprintf("@%d@%d@%d@%d",
//					traitConfig.ScalingConfig.Hpa.Min,
//					traitConfig.ScalingConfig.Hpa.Max,
//					traitConfig.ScalingConfig.Hpa.CpuUtilization,
//					traitConfig.ScalingConfig.Hpa.MemoryUtilization)
//				updateInfo = append(updateInfo, source+dest)
//				configByte, err := json.Marshal(traitConfig)
//				if err != nil {
//					return err
//				}
//				if isUpdate == "100" && flag == "是" {
//					err = deploy_config.UpdateDeployConfigTraitConfig("prod", deployConfig.ID, configByte)
//					if err != nil {
//						return err
//					}
//				}
//				//var minHpa int32
//				//var maxHpa int32
//				//minHpa = 2
//				//maxHpa = 3
//				fmt.Println(cluster, metadata)
//				if _, isExist := repair[unit+":"+metadata]; !isExist && cluster == metadata {
//					if !ok {
//						errInfo = append(errInfo, fmt.Sprintf("当前服务[%s]的集群[%s]找不到对应的hpa", unit, metadata))
//						continue
//					}
//					fmt.Println(unit, metadata, "123")
//					scaledObjectSpec := keda.ScaledObjectSpec{
//						ScaleTargetRef: &keda.ScaleTarget{
//							Name: unit,
//							//Name: "deploy",
//							Kind: "Deployment",
//						},
//						MinReplicaCount: &traitConfig.ScalingConfig.Hpa.Min,
//						//MinReplicaCount: &minHpa,
//						MaxReplicaCount: &traitConfig.ScalingConfig.Hpa.Max,
//						//MaxReplicaCount: &maxHpa,
//					}
//					if traitConfig.ScalingConfig.Hpa.CpuUtilization != 0 || traitConfig.ScalingConfig.Hpa.MemoryUtilization != 0 {
//						scaledObjectSpec.Triggers = []keda.ScaleTriggers{}
//					}
//					if traitConfig.ScalingConfig.Hpa.CpuUtilization != 0 {
//						scaledObjectSpec.Triggers = append(scaledObjectSpec.Triggers, keda.ScaleTriggers{Type: "cpu", MetricType: "Utilization",
//							Metadata: map[string]string{"value": strconv.Itoa(int(traitConfig.ScalingConfig.Hpa.CpuUtilization))}})
//					}
//					if traitConfig.ScalingConfig.Hpa.MemoryUtilization != 0 {
//						scaledObjectSpec.Triggers = append(scaledObjectSpec.Triggers, keda.ScaleTriggers{Type: "memory", MetricType: "Utilization",
//							Metadata: map[string]string{"value": strconv.Itoa(int(traitConfig.ScalingConfig.Hpa.MemoryUtilization))}})
//					}
//					spec, err := json.Marshal(scaledObjectSpec)
//					if err != nil {
//						return err
//					}
//					req := &constack.SaveRequest{
//						Cluster:   metadata,
//						Namespace: namespace,
//						Name:      unit,
//						Spec:      string(spec),
//					}
//					Updatereq = append(Updatereq, fmt.Sprintf("%v", req))
//					if isUpdate == "1" {
//						resp, err := cloudRpcClient.Save(context.Background(), req)
//						if err != nil {
//							log.Errorf("%v", err)
//							return err
//						}
//						repair[unit+":"+metadata] = struct{}{}
//						fmt.Printf("更新服务[%s][%s]HPA成功，%v", unit, cluster, resp)
//					}
//				}
//			}
//		}
//	}
//	for _, errI := range errInfo {
//		fmt.Println(errI)
//	}
//	//for _, update := range Updatereq {
//	//	fmt.Println(update)
//	//}
//	noRepeat := make(map[string]struct{})
//	for _, update := range Updatereq {
//		noRepeat[update] = struct{}{}
//	}
//	for key, _ := range noRepeat {
//		fmt.Println(key)
//	}
//	return nil
//}

func checkHpaIsUpdated(unit, cluster string, deployConfigs []deploy_config.DeployConfig) (string, bool) {
	var lowV deploy_config.DeployConfig
	var upV deploy_config.DeployConfig
	curVmax := 0
	curVmin := 1000
	for _, deployConfig := range deployConfigs {
		if curVmin > deployConfig.Version {
			curVmin = deployConfig.Version
			lowV = deployConfig
		}
		if curVmax < deployConfig.Version {
			curVmax = deployConfig.Version
			upV = deployConfig
		}
	}
	//fmt.Println(fmt.Sprintf("%s-%s-%v-%v", unit, cluster, lowV, upV))
	var traitConfigLow deploy.TraitConfig
	err := json.Unmarshal(lowV.TraitConfig, &traitConfigLow)
	if err != nil {
		panic(err)
	}
	var traitConfigUp deploy.TraitConfig
	err = json.Unmarshal(upV.TraitConfig, &traitConfigUp)
	if err != nil {
		panic(err)
	}
	if traitConfigLow.ScalingConfig.Hpa.Min != traitConfigUp.ScalingConfig.Hpa.Min ||
		traitConfigLow.ScalingConfig.Hpa.Max != traitConfigUp.ScalingConfig.Hpa.Max ||
		traitConfigLow.ScalingConfig.Hpa.CpuUtilization != traitConfigUp.ScalingConfig.Hpa.CpuUtilization ||
		traitConfigLow.ScalingConfig.Hpa.MemoryUtilization != traitConfigUp.ScalingConfig.Hpa.MemoryUtilization {
		return fmt.Sprintf("%s@%s@%s@%d@%d@%d@%d@%d@%d@%d@%d", unit, cluster, "更改过",
			traitConfigLow.ScalingConfig.Hpa.Min,
			traitConfigLow.ScalingConfig.Hpa.Max,
			traitConfigLow.ScalingConfig.Hpa.CpuUtilization,
			traitConfigLow.ScalingConfig.Hpa.MemoryUtilization,
			traitConfigUp.ScalingConfig.Hpa.Min,
			traitConfigUp.ScalingConfig.Hpa.Max,
			traitConfigUp.ScalingConfig.Hpa.CpuUtilization,
			traitConfigUp.ScalingConfig.Hpa.MemoryUtilization), true
	}
	return "", false
}

func CheckSidecar() error {
	deployConfigs, err := deploy_config.FindDeployConfigByProjectIdWithAppId("prod", 7)
	if err != nil {
		return err
	}
	var info []string
	for appId, configs := range deployConfigs {
		for _, config := range configs {
			var traitConfig deploy.TraitConfig
			err := json.Unmarshal(config.TraitConfig, &traitConfig)
			if err != nil {
				panic(err)
			}
			if traitConfig.ResourceConstraints != nil && traitConfig.ResourceConstraints.Sidecar != nil {
				if traitConfig.ResourceConstraints.Sidecar.Enabled {
					sidecar := traitConfig.ResourceConstraints.Sidecar
					if sidecar.RequestCpu > 20 || sidecar.LimitCpu > 20 {
						info = append(info, fmt.Sprintf("%d-%d", appId, config.ID))
					}
				}
			}
		}
	}
	for _, i := range info {
		fmt.Println(i)
	}
	return nil
}

func CleanAppIds(appIds, isUpdate string) error {
	pgs, err := deploy_config.GetPipelineGroupBy("prod", 7)
	if err != nil {
		return err
	}
	if len(pgs) == 0 {
		return nil
	}
	idMap := make(map[string]struct{})
	ids := strings.Split(appIds, ",")
	for _, id := range ids {
		idMap[id] = struct{}{}
	}
	var updateInfo []string
	for _, pg := range pgs {
		update := ""
		curIds := strings.Split(pg.AppIDs, ",")
		for _, curId := range curIds {
			if _, exist := idMap[curId]; exist {
				continue
			}
			update = update + "," + curId
		}
		res := update[1:]
		if isUpdate == "1" {
			err = deploy_config.UpdatePipelineGroupAppIds("prod", pg.ID, res)
		}
		if err != nil {
			return err
		}
		if pg.AppIDs != res {
			updateInfo = append(updateInfo, fmt.Sprintf("流水线组[%s][%d]appIds:[%s]更改为[%s]", pg.Name, pg.ID, pg.AppIDs, res))
		}
	}
	for _, up := range updateInfo {
		fmt.Println(up)
	}
	return nil
}

func RepairOnlineHPA(isUpdate, units string, projectId int64) error {
	cloudRpcClient, err := cloud.NewGRPCClient("cloud.ttyuyin.com:8100", "Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663")
	if err != nil {
		return err
	}
	unitArray := strings.Split(units, "@")
	var Updatereq []string
	repair := make(map[string]struct{})
	//fmt.Println(unitArray)
	var errInfo []string
	var resInfo []string
	for _, unitInfo := range unitArray {
		unit := strings.Split(unitInfo, ",")[0]
		cluster := strings.Split(unitInfo, ",")[1]
		min, _ := strconv.Atoi(strings.Split(unitInfo, ",")[2])
		max, _ := strconv.Atoi(strings.Split(unitInfo, ",")[3])
		cpu := strings.Split(unitInfo, ",")[4]
		mem := strings.Split(unitInfo, ",")[5]
		min32 := int32(min)
		max32 := int32(max)
		if _, isExist := repair[unit+":"+cluster]; !isExist {
			scaledObjectSpec := keda.ScaledObjectSpec{
				ScaleTargetRef: &keda.ScaleTarget{
					Name: unit,
					//Name: "deploy",
					Kind: "Deployment",
				},
				MinReplicaCount: &min32,
				//MinReplicaCount: &minHpa,
				MaxReplicaCount: &max32,
				//MaxReplicaCount: &maxHpa,
			}
			if cpu != "0" || mem != "0" {
				scaledObjectSpec.Triggers = []keda.ScaleTriggers{}
			}
			if cpu != "0" {
				scaledObjectSpec.Triggers = append(scaledObjectSpec.Triggers, keda.ScaleTriggers{Type: "cpu", MetricType: "Utilization",
					Metadata: map[string]string{"value": cpu}})
			}
			if mem != "0" {
				scaledObjectSpec.Triggers = append(scaledObjectSpec.Triggers, keda.ScaleTriggers{Type: "memory", MetricType: "Utilization",
					Metadata: map[string]string{"value": mem}})
			}
			spec, err := json.Marshal(scaledObjectSpec)
			if err != nil {
				return err
			}
			req := &constack.SaveRequest{
				Cluster:   cluster,
				Namespace: "quicksilver",
				Name:      unit,
				Spec:      string(spec),
			}
			Updatereq = append(Updatereq, fmt.Sprintf("%v", req))
			if isUpdate == "1" {
				resp, err := cloudRpcClient.Save(context.Background(), req)
				if err != nil {
					log.Errorf("%v", err)
					errInfo = append(errInfo, fmt.Sprintf("服务[%s]调整HPA失败，原因：%v", unit, err))
				} else {
					repair[unit+":"+cluster] = struct{}{}
					resInfo = append(resInfo, fmt.Sprintf("更新服务[%s][%s]HPA成功，%v", unit, cluster, resp))
				}
			}
		}
	}
	for _, value := range errInfo {
		fmt.Println(value)
	}
	for _, value := range Updatereq {
		fmt.Println(value)
	}
	for _, value := range resInfo {
		fmt.Println(value)
	}
	return nil
}

func TTHpaMutle(units, isUpdate string) error {
	var updateUnits []string
	unitArray := strings.Split(units, " ")
	for _, unitInfo := range unitArray {
		unit := strings.Split(unitInfo, ",")[0]
		unitCluster := strings.Split(unitInfo, ",")[1]
		deployConfigs, err := deploy_config.FindDeployMetadataWithConfigBy("prod", 7, unit)
		if err != nil {
			return err
		}
		for metadata, configs := range deployConfigs {
			cluster := strings.Split(metadata, ":")[0]
			//namespace := strings.Split(metadata, ":")[1]
			if cluster != unitCluster {
				continue
			}
			for _, config := range configs {
				if config.Version == 0 {
					continue
				}
				var traitConfig deploy.TraitConfig
				err := json.Unmarshal(config.TraitConfig, &traitConfig)
				if err != nil {
					return err
				}
				if traitConfig.ScalingConfig != nil && traitConfig.ScalingConfig.Hpa != nil {
					if traitConfig.ScalingConfig.Type == "hpa" {
						traitConfig.ScalingConfig.Type = "multiHPA"
						traitConfig.ScalingConfig.MultiHpa = &deploy.ScalingConfig_MultiHPA{
							Min:               traitConfig.ScalingConfig.Hpa.Min,
							Max:               traitConfig.ScalingConfig.Hpa.Max,
							CpuUtilization:    traitConfig.ScalingConfig.Hpa.CpuUtilization,
							MemoryUtilization: traitConfig.ScalingConfig.Hpa.MemoryUtilization,
							Types:             traitConfig.ScalingConfig.Hpa.Types,
						}
						traitConfig.ScalingConfig.Hpa.Min = 0
						traitConfig.ScalingConfig.Hpa.Max = 0
						traitConfig.ScalingConfig.Hpa.CpuUtilization = 0
						traitConfig.ScalingConfig.Hpa.MemoryUtilization = 0
						traitConfig.ScalingConfig.Hpa.Types = []string{}
					}

				}
				configByte, err := json.Marshal(traitConfig)
				if isUpdate == "1" {
					err = deploy_config.UpdateDeployConfigTraitConfig("prod", config.ID, configByte)
					updateUnits = append(updateUnits, unit)
				}
			}
		}
	}
	for _, up := range updateUnits {
		fmt.Println(up)
	}
	return nil
}

func CreateHPA(ctx context.Context, cluster, namespace, appName string) error {
	var min, max int32
	var cpu, mem string
	cloudRpcClient, err := cloud.NewGRPCClient("cloud.ttyuyin.com:8100", "Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663")
	if err != nil {
		return err
	}
	getHpaReq := constack.GetWorkloadRelatedHpaRequest{
		Cluster:   cluster,
		Namespace: namespace,
		Name:      appName,
		Kind:      constack.WorkloadKind_DEPLOYMENT,
	}
	hpa, err := cloudRpcClient.GetWorkloadRelatedHpa(ctx, &getHpaReq)
	if err != nil {
		if status.Code(err).String() == codes.NotFound.String() {
			log.ErrorWithCtx(ctx, "请求容器云rpc接口获取HPA,Deployment不存在")
			//return deployErr.ErrDeploymentNotExisted
		} else {
			log.ErrorWithCtx(ctx, "请求容器云rpc接口获取HPA错误,opt:%v,错误信息:%v", getHpaReq, err)
			return err
		}
	}
	if hpa != nil {
		log.InfoWithCtx(ctx, "当前环境[%v]的HPA已经存在，无需创建", getHpaReq)
		return nil
	}
	scaledObjectSpec := keda.ScaledObjectSpec{
		ScaleTargetRef: &keda.ScaleTarget{
			Name: appName, //deployment名字
			Kind: "Deployment",
		},
		MinReplicaCount: &min,
		MaxReplicaCount: &max,
	}
	if cpu != "0" || mem != "0" {
		scaledObjectSpec.Triggers = []keda.ScaleTriggers{}
	}
	if cpu != "0" {
		scaledObjectSpec.Triggers = append(scaledObjectSpec.Triggers, keda.ScaleTriggers{Type: "cpu", MetricType: "Utilization",
			Metadata: map[string]string{"value": cpu}})
	}
	if mem != "0" {
		scaledObjectSpec.Triggers = append(scaledObjectSpec.Triggers, keda.ScaleTriggers{Type: "memory", MetricType: "Utilization",
			Metadata: map[string]string{"value": mem}})
	}
	spec, err := json.Marshal(scaledObjectSpec)
	createHpaReq := constack.SaveRequest{
		Cluster:   cluster,
		Namespace: namespace,
		Name:      appName,
		Spec:      string(spec),
	}
	createHpaResp, err := cloudRpcClient.Save(ctx, &createHpaReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求容器云rpc接口创建HPA错误,opt:%v,错误信息:%v", createHpaReq, err)
		return err
	}
	log.InfoWithCtx(ctx, "请求容器云rpc接口创建HPA成功,返回信息:%v", createHpaResp)
	return nil
}
