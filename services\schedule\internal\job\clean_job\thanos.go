package clean_job

import (
	"math/rand"
	"os"
	"path/filepath"

	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/schedule/internal/conf"
)

type Thanos struct {
	cfg *conf.Config
}

func NewThanos(cfg *conf.Config) *Thanos {
	return &Thanos{
		cfg: cfg,
	}
}

func (c *Thanos) Run() {
	log.Info("starting thanos cleanup")

	// Define the directory to clean
	cacheBuild := "/cicd/cache/cache/build"
	cachePkg := "/cicd/cache/cache/pkg"

	c.snapFingers(cacheBuild)
	c.snapFingers(cachePkg)

	log.Info("thanos cleanup complete")
}

func (c *Thanos) snapFingers(cacheDir string) {
	log.Info("snapping fingers to clean up cache")

	// Get all subdirectories
	subDirs, err := os.ReadDir(cacheDir)
	if err != nil {
		log.Errorf("failed to read cache directory: %v", err)
		return
	}
	// Shuffle and delete half of the subdirectories
	rand.Shuffle(len(subDirs), func(i, j int) {
		subDirs[i], subDirs[j] = subDirs[j], subDirs[i]
	})

	for i := 0; i < len(subDirs)/2; i++ {
		if subDirs[i].IsDir() {
			dirPath := filepath.Join(cacheDir, subDirs[i].Name())
			if err := os.RemoveAll(dirPath); err != nil {
				log.Errorf("failed to delete directory %s: %v", dirPath, err)
			} else {
				log.Infof("deleted directory: %s", dirPath)
			}

		}
	}
}
