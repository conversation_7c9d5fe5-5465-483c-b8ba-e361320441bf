package rest

import (
	"52tt.com/cicd/pkg/para"
	"github.com/gin-gonic/gin"
)

func (tc *HelloWorldController) Route(route *gin.RouterGroup) {
	r := route.Group("/hello-world")
	r.GET("", tc.helloWorld)
}

type HelloWorldController struct{}

func NewHelloWorldController() *HelloWorldController {
	return &HelloWorldController{}
}

func (tc *HelloWorldController) helloWorld(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "测试")
	result := map[string]interface{}{
		"result": "success",
	}
	ctxWrapper.Ok(result)
}
