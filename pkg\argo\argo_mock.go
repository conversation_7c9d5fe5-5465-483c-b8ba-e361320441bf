// Code generated by MockGen. DO NOT EDIT.
// Source: argo.go

// Package argo is a generated GoMock package.
package argo

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockArgoClient is a mock of ArgoClient interface.
type MockArgoClient struct {
	ctrl     *gomock.Controller
	recorder *MockArgoClientMockRecorder
}

// MockArgoClientMockRecorder is the mock recorder for MockArgoClient.
type MockArgoClientMockRecorder struct {
	mock *MockArgoClient
}

// NewMockArgoClient creates a new mock instance.
func NewMockArgoClient(ctrl *gomock.Controller) *MockArgoClient {
	mock := &MockArgoClient{ctrl: ctrl}
	mock.recorder = &MockArgoClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockArgoClient) EXPECT() *MockArgoClientMockRecorder {
	return m.recorder
}

// DeleteApplication mocks base method.
func (m *MockArgoClient) DeleteApplication(arg0 context.Context, arg1 *ApplicationDeleteRequest) (*ApplicationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteApplication", arg0, arg1)
	ret0, _ := ret[0].(*ApplicationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteApplication indicates an expected call of DeleteApplication.
func (mr *MockArgoClientMockRecorder) DeleteApplication(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteApplication", reflect.TypeOf((*MockArgoClient)(nil).DeleteApplication), arg0, arg1)
}
