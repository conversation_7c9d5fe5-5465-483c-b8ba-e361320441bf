package rest

import (
	"context"
	"reflect"

	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/mcp/internal/service/del_ser"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mitchellh/mapstructure"
)

func (ctl *ttCloudMCPController) ToolsKillPod() mcp.Tool {
	argsType := reflect.TypeOf(del_ser.KillPodArgs{})
	argsSchema := jsonSchemaReflector.ReflectFromType(argsType)
	raw, _ := argsSchema.MarshalJSON()

	return mcp.NewToolWithRawSchema("kill_pod_in_k8s", DescpOfKillPod, raw)
}

func (ctl *ttCloudMCPController) KillPod(ctx context.Context, request mcp.CallToolRequest) (res *mcp.CallToolResult, errOut error) {
	var req del_ser.KillPodArgs
	err := mapstructure.Decode(request.Params.Arguments, &req)
	if err != nil {
		res = mcp.NewToolResultError("find.FindK8SResourceArgs Decode Err")
		return
	}

	err = del_ser.DeleteAgg.KillPod(req)
	if err != nil {
		log.Errorf("ttCloudMCPController KillPod err: %v", err)
		res = mcp.NewToolResultError(err.Error())
		return
	}
	res = mcp.NewToolResultText("Success")

	return
}

const (
	DescpOfKillPod = `
功能：
    精准删除Kubernetes集群中指定的Pod（支持自动重建）
输入参数​（严格校验）：
    集群名称（必须）：目标集群标识
    命名空间（必须）：指定Namespace
    Pod名称（必须）：精确匹配Pod名称
核心特性：
    原子化操作：单次仅处理一个Pod
    重建兼容：删除pod后，pod将会自动重建；
典型场景：
    强制重启异常Pod（状态为CrashLoopBackOff）
    配置更新后触发Pod重建			
`
)
