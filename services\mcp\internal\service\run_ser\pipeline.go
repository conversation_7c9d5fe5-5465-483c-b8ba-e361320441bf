package run_ser

import (
	"context"

	"52tt.com/cicd/protocol/app"
	"52tt.com/cicd/protocol/iam"
	"52tt.com/cicd/protocol/pipeline"
)

type pipelineEn struct {
	psCli   pipeline.PipelineServiceClient
	userCli iam.UserServiceClient
	appCli  app.AppServiceClient
}

type RunPipelineReq struct {
	ProjectName  string `json:"projectName"`
	AppName      string `json:"appName"`
	PipelineName string `json:"pipelineName" `
	Branch       string `json:"branch" `
	UserEmail    string `json:"userEmail" `
}

func (en *pipelineEn) Run(req RunPipelineReq) (err error) {
	ctx := context.Background()
	app, err := en.appCli.GetAppByName(ctx, &app.AppByNameReq{Name: req.AppName, ProjectName: req.ProjectName})
	if err != nil {
		return
	}

	_, err = en.psCli.RunPipeline(ctx, &pipeline.RunPipelingReq{
		AppId:        app.Id,
		PipelineName: req.<PERSON><PERSON>ineN<PERSON>,
		Branch:       req.Branch,
		UserId:       999999,
		Chinese<PERSON><PERSON>:  "<PERSON>",
		EmployeeNo:   "T0000",
		Description:  "Run by Jarvis",
	})
	return
}
