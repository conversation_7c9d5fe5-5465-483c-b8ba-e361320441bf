package conf

import (
	"os"

	"52tt.com/cicd/pkg/kafka"

	"52tt.com/cicd/pkg/config"
)

var AppConfig *Config

type Config struct {
	config.Default
	PipelineRules PipelineRules `mapstructure:"pipeline_rules"`
	TaskCommands  TaskCommands  `mapstructure:"task_commands"`
	ScriptParams  ScriptParams  `mapstructure:"script_params"`
	Tekton        Tekton        `mapstructure:"tekton"`
	Gitlab        Gitlab        `mapstructure:"gitlab"`
	Cloud         Cloud         `mapstructure:"cloud"`
	RdLog         RdLog         `mapstructure:"rd_log"`
	PodLog        PodLog        `mapstructure:"pod_log"`
	LarkTemplate  LarkTemplate  `mapstructure:"lark_template"`
	Quality       Quality       `mapstructure:"quality"`
	Pipeline      Pipeline      `mapstructure:"pipeline"`
	Retry         Retry         `mapstructure:"retry"`
	SpecialImgs   SpecialImgs   `mapstructure:"special_imgs"`
	Open          *Open         `mapstructure:"open"`
	UpgradeCheck  UpgradeCheck  `mapstructure:"upgrade_check"`
	HarborCR      HarborCR      `mapstructure:"harbor_cr"`
}

type PipelineRules struct {
	Stages []Stage `mapstructure:"stages" json:"stages"`
}

type Pipeline struct {
	Host string
}

type Stage struct {
	Name            string   `mapstructure:"name" json:"name,omitempty"`
	Fixed           bool     `mapstructure:"fixed" json:"fixed,omitempty"`
	Ci              bool     `mapstructure:"ci" json:"ci,omitempty"`
	IsInChangeSet   bool     `mapstructure:"is_in_change_set" json:"isInChangeSet,omitempty"`
	AvailableStages []string `mapstructure:"available_stages" json:"availableStages,omitempty"`
	Tasks           []Task   `mapstructure:"tasks" json:"tasks,omitempty"`
}

type Task struct {
	Name  string `mapstructure:"name" json:"name,omitempty"`
	Fixed bool   `mapstructure:"fixed" json:"fixed,omitempty"`
	Max   int    `mapstructure:"max" json:"max,omitempty"`
}

type TaskCommands struct {
	Commands []Command `mapstructure:"commands" json:"commands"`
}

type Param struct {
	Key   string `mapstructure:"key" json:"key"`
	Value string `mapstructure:"value" json:"value"`
}

type Command struct {
	Language   string   `mapstructure:"language" json:"language"`
	UnitTest   string   `mapstructure:"unit_test" json:"unitTest"`
	Checkstyle string   `mapstructure:"checkstyle" json:"checkstyle"`
	BuildApp   string   `mapstructure:"build_app" json:"buildApp"`
	Versions   []string `mapstructure:"versions" json:"versions"`
}

type ScriptParams struct {
	Params []Param `mapstructure:"params" json:"params"`
}

type Tekton struct {
	Host                string       `mapstructure:"host" json:"host"`
	Namespace           string       `mapstructure:"namespace" json:"namespace"`
	Env                 string       `mapstructure:"env" json:"env"`
	Tasks               []TektonTask `mapstructure:"task" json:"task"`
	RegistryUrl         string       `mapstructure:"registry_url" json:"registry_url"`
	OverseasRegistryUrl string       `mapstructure:"overseas_registry_url" json:"overseas_registry_url"`
	ProjectQuota        int64        `mapstructure:"project_quota" json:"project_quota"`
	PipelineTimeouts    string       `mapstructure:"pipeline_timeouts" json:"pipeline_timeouts"` // ParseDuration format
	TasksTimeouts       string       `mapstructure:"tasks_timeouts" json:"tasks_timeouts"`       // ParseDuration format
	FinallyTimeouts     string       `mapstructure:"finally_timeouts" json:"finally_timeouts"`   // ParseDuration format
	NodeNums            int64        `mapstructure:"node_nums" json:"node_nums"`                 // 集群节点数量
	UseSpuerNode        int64        `mapstructure:"use_spuer_node" json:"use_spuer_node"`       // 是否使用超级节点
	SpuerNodePrjs       string       `mapstructure:"spuer_node_prjs" json:"spuer_node_prjs"`     // 超级节点项目列表
}

type TektonTask struct {
	Type           string   `mapstructure:"type" json:"type"`
	Name           string   `mapstructure:"name" json:"name"`
	Language       string   `mapstructure:"language" json:"language"`
	ContainerName  []string `mapstructure:"container_name" json:"container_name"`
	CustomTaskType string   `mapstructure:"custom_task_type" json:"custom_task_type"`
}

type Gitlab struct {
	Host     string `json:"host"`
	Username string `json:"username"`
	Token    string `json:"token"`
}

type Cloud struct {
	Host             string `json:"host"`
	Token            string `json:"token"`
	GrpcTarget       string `mapstructure:"grpc_target" json:"grpc_target"`
	DeployGrpcTarget string `mapstructure:"deploy_grpc_target" json:"deploy_grpc_target"`
}

type RdLog struct {
	Host       string `mapstructure:"host" json:"host"`
	Search     string `mapstructure:"search" json:"search"`
	Topic      string `mapstructure:"topic" json:"topic"`
	Auth       string `mapstructure:"auth" json:"auth"`
	QueryPod   string `mapstructure:"query_pod" json:"query_pod"`
	QueryLabel string `mapstructure:"query_label" json:"query_label"`
}

type PodLog struct {
	MaxLineNum int64 `mapstructure:"max_line_num" json:"max_line_num"`
}

type LarkTemplate struct {
	Build     string `mapstructure:"build" json:"build"`          //流水线运行状态飞书通知消息卡片-成功/终止，无阶段信息
	BuildMore string `mapstructure:"build_more" json:"buildMore"` //流水线运行状态飞书通知消息卡片-失败/待处理，有阶段信息
}

type Quality struct {
	Host        string `json:"host" mapstructure:"host"`
	GetProject  string `json:"get_project" mapstructure:"get_project"`
	GetPlan     string `json:"get_plan" mapstructure:"get_plan"`
	ApiAutoTest string `json:"api_auto_test" mapstructure:"api_auto_test"`
}

type Retry struct {
	TimeoutDays int `json:"timeout_days" mapstructure:"timeout_days"`
}

type SpecialImgs struct {
	ImgUrls []ImgUrl `mapstructure:"urls" json:"urls"`
}

type ImgUrl struct {
	ProjectId  int64  `mapstructure:"project_id" json:"projectId"`
	DefaultUrl string `mapstructure:"default_url" json:"defaultUrl"`
	ReleaseUrl string `mapstructure:"release_url" json:"releaseUrl"`
}

type Open struct {
	Kafka *kafka.Config `mapstructure:"kafka" json:"kafka"`
}

type UpgradeCheck struct {
	MaxPipelineRunID int64 `mapstructure:"max_pipeline_run_id" json:"max_pipeline_run_id"`
}

type HarborCR struct {
	Host     string `mapstructure:"host" json:"host"`
	AuthName string `mapstructure:"authName" json:"authName"`
	AuthPsw  string `mapstructure:"authPsw" json:"authPsw"`
}

func LoadConfig(path string) (*Config, error) {
	_, err := os.Stat(path)
	if err != nil {
		if os.IsNotExist(err) {
			path = "../conf/"
		}
	}
	var c Config
	if err := config.NewConfig(path, &c); err != nil {
		return nil, err
	}

	AppConfig = &c
	return &c, nil
}

func GetCiStagesConfig() []string {
	var ciStages []string
	for _, stage := range AppConfig.PipelineRules.Stages {
		if stage.Ci {
			ciStages = append(ciStages, stage.Name)
		}
	}
	return ciStages
}

func GetCdStageConfig() []string {
	var cdStages []string
	for _, stage := range AppConfig.PipelineRules.Stages {
		if !stage.Ci {
			cdStages = append(cdStages, stage.Name)
		}
	}
	return cdStages
}

func GetInChangeSetStageConfig() map[string]struct{} {
	changeSetStages := make(map[string]struct{})
	for _, stage := range AppConfig.PipelineRules.Stages {
		if stage.IsInChangeSet {
			changeSetStages[stage.Name] = struct{}{}
		}
	}
	return changeSetStages
}

func GetCustomTaskTypeConfig() map[string]string {
	customTaskType := make(map[string]string)
	for _, task := range AppConfig.Tekton.Tasks {
		customTaskType[task.Type] = task.CustomTaskType
	}
	return customTaskType
}
