package find

import (
	"context"
	"strconv"
	"time"

	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
)

func (agg *findAgg) FindClusterOrNamespaces(req FindClusterOrNSArgs) (rst any, err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*3)
	defer cancel()
	// 按查询 集群
	if req.ClusterName == "" && req.ClusterId == "" {
		clusters, errIn := agg.cloudagg.FindCluster(ctx, &constack.ClusterReq{
			Env: req.Env,
		})
		if errIn != nil {
			err = errIn
		}
		cluResp := ClusterResp{Total: int(clusters.Total),
			List: make([]ClusterInfo, 0, len(clusters.GetObjs()))}
		nums := 0

		for _, obj := range clusters.GetObjs() {
			if nums >= 33 {
				break
			}
			cluResp.List = append(cluResp.List, ClusterInfo{
				Id:        uint(obj.GetClusterId()),
				Name:      obj.GetName(),
				ApiServer: obj.GetApiServer(),
				Cloud:     obj.GetCloud(),
				CloudId:   obj.GetCloudId(),
				IsReady:   int(obj.GetIsReady()),
				RegionId:  obj.GetRegionId(),
				Desc:      obj.GetDesc(),
				Env:       obj.GetEnv(),
			})
			nums++
		}
		rst = cluResp

		return
	}

	// 按查询命名空间
	clusterId, _ := strconv.Atoi(req.ClusterId)
	reqParm := &constack.NamespaceReq{
		Cluster:   req.ClusterName,
		Business:  req.Business,
		ClusterId: int32(clusterId),
	}

	objs, err := agg.cloudagg.FindNS(ctx, reqParm)
	if err != nil {
		return
	}

	nsResp := NamespaceResp{Total: int(objs.Total),
		List: make([]NamespaceInfo, 0, len(objs.GetObjs()))}
	nsResp.ClusterInfo = ClusterInfo{
		Id:        uint(objs.GetClusterInfo().GetClusterId()),
		Name:      objs.GetClusterInfo().GetName(),
		ApiServer: objs.GetClusterInfo().GetApiServer(),
		Cloud:     objs.GetClusterInfo().GetCloud(),
		CloudId:   objs.GetClusterInfo().GetCloudId(),
		IsReady:   int(objs.GetClusterInfo().GetIsReady()),
		RegionId:  objs.GetClusterInfo().GetRegionId(),
		Desc:      objs.GetClusterInfo().GetDesc(),
		Env:       objs.GetClusterInfo().GetEnv(),
	}

	nums := 0
	for _, obj := range objs.GetObjs() {
		if nums >= 33 {
			break
		}
		nsResp.List = append(nsResp.List, NamespaceInfo{
			Cluster:   obj.GetCluster(),
			Name:      obj.GetName(),
			Business:  obj.GetBusiness(),
			Uuid:      obj.GetUuid(),
			DevGroups: obj.GetDevGroups(),
			Labels:    obj.GetLabels(),
		})
		nums++
	}

	rst = nsResp

	return
}

type FindClusterOrNSArgs struct {
	Env         string `json:"env" jsonschema:"description=环境枚举 非必需 查询集群信息时可以作为筛选条件，查询命名空间时不需要，分表代表production：生产环境，preview：灰度环境，testing:测试环境，dev：开发环境 enum=production,enum=preview,enum=testing,enum=dev"`
	ClusterName string `json:"clusterName" jsonschema:"description=集群名称,查询Namespaces时和clusterId必填其中之一"`
	ClusterId   string `json:"clusterId" jsonschema:"description=集群ID,查询Namespaces时和clusterName必填其中之一"`
	Business    string `json:"business" jsonschema:"description=业务属性,非必填"`
}

type ClusterResp struct {
	Total int           `json:"total"`
	List  []ClusterInfo `json:"list"`
}

type NamespaceResp struct {
	Total       int             `json:"total"`
	ClusterInfo ClusterInfo     `json:"clusterInfo"`
	List        []NamespaceInfo `json:"list"`
}

type ClusterInfo struct {
	Id        uint
	Name      string
	ApiServer string
	Cloud     string
	CloudId   string
	IsReady   int
	RegionId  string
	Desc      string
	Env       string
}

type NamespaceInfo struct {
	// 集群名称
	Cluster string
	// 命名空间名称
	Name string
	// 命名空间所属业务线
	Business string
	// UUID
	Uuid string
	// 开发团队
	DevGroups string
	// 集群标签
	Labels map[string]string
}
