// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.4
// source: protocol/pipeline/pipeline.proto

package pipeline

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Pipeline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AppId int64 `protobuf:"varint,2,opt,name=appId,proto3" json:"appId,omitempty"`
}

func (x *Pipeline) Reset() {
	*x = Pipeline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pipeline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pipeline) ProtoMessage() {}

func (x *Pipeline) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pipeline.ProtoReflect.Descriptor instead.
func (*Pipeline) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{0}
}

func (x *Pipeline) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Pipeline) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

type Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Config []byte `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *Task) Reset() {
	*x = Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{1}
}

func (x *Task) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Task) GetConfig() []byte {
	if x != nil {
		return x.Config
	}
	return nil
}

type PipelineArray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pipelines []*PipelineResult `protobuf:"bytes,1,rep,name=pipelines,proto3" json:"pipelines,omitempty"`
}

func (x *PipelineArray) Reset() {
	*x = PipelineArray{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineArray) ProtoMessage() {}

func (x *PipelineArray) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineArray.ProtoReflect.Descriptor instead.
func (*PipelineArray) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{2}
}

func (x *PipelineArray) GetPipelines() []*PipelineResult {
	if x != nil {
		return x.Pipelines
	}
	return nil
}

type PipelineResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TemplateId int64  `protobuf:"varint,2,opt,name=templateId,proto3" json:"templateId,omitempty"`
	Name       string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Type       string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *PipelineResult) Reset() {
	*x = PipelineResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineResult) ProtoMessage() {}

func (x *PipelineResult) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineResult.ProtoReflect.Descriptor instead.
func (*PipelineResult) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{3}
}

func (x *PipelineResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PipelineResult) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *PipelineResult) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PipelineResult) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type PipelineCountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppIds []int64 `protobuf:"varint,1,rep,packed,name=appIds,proto3" json:"appIds,omitempty"`
}

func (x *PipelineCountReq) Reset() {
	*x = PipelineCountReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineCountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineCountReq) ProtoMessage() {}

func (x *PipelineCountReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineCountReq.ProtoReflect.Descriptor instead.
func (*PipelineCountReq) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{4}
}

func (x *PipelineCountReq) GetAppIds() []int64 {
	if x != nil {
		return x.AppIds
	}
	return nil
}

type PipelineAppReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           int64  `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppName         string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	BuildPath       string `protobuf:"bytes,3,opt,name=build_path,json=buildPath,proto3" json:"build_path,omitempty"`
	RepoAddr        string `protobuf:"bytes,4,opt,name=repo_addr,json=repoAddr,proto3" json:"repo_addr,omitempty"`
	Language        string `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty"`
	LanguageVersion string `protobuf:"bytes,6,opt,name=language_version,json=languageVersion,proto3" json:"language_version,omitempty"`
}

func (x *PipelineAppReq) Reset() {
	*x = PipelineAppReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineAppReq) ProtoMessage() {}

func (x *PipelineAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineAppReq.ProtoReflect.Descriptor instead.
func (*PipelineAppReq) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{5}
}

func (x *PipelineAppReq) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *PipelineAppReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *PipelineAppReq) GetBuildPath() string {
	if x != nil {
		return x.BuildPath
	}
	return ""
}

func (x *PipelineAppReq) GetRepoAddr() string {
	if x != nil {
		return x.RepoAddr
	}
	return ""
}

func (x *PipelineAppReq) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *PipelineAppReq) GetLanguageVersion() string {
	if x != nil {
		return x.LanguageVersion
	}
	return ""
}

type PipelineCountResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountMap map[int64]int64 `protobuf:"bytes,1,rep,name=count_map,json=countMap,proto3" json:"count_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *PipelineCountResp) Reset() {
	*x = PipelineCountResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineCountResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineCountResp) ProtoMessage() {}

func (x *PipelineCountResp) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineCountResp.ProtoReflect.Descriptor instead.
func (*PipelineCountResp) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{6}
}

func (x *PipelineCountResp) GetCountMap() map[int64]int64 {
	if x != nil {
		return x.CountMap
	}
	return nil
}

type PipelineAppResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *PipelineAppResp) Reset() {
	*x = PipelineAppResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineAppResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineAppResp) ProtoMessage() {}

func (x *PipelineAppResp) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineAppResp.ProtoReflect.Descriptor instead.
func (*PipelineAppResp) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{7}
}

func (x *PipelineAppResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PipelineAppResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetPipelineConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: uri:"id"
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" uri:"id"`
}

func (x *GetPipelineConfigReq) Reset() {
	*x = GetPipelineConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineConfigReq) ProtoMessage() {}

func (x *GetPipelineConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineConfigReq.ProtoReflect.Descriptor instead.
func (*GetPipelineConfigReq) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{8}
}

func (x *GetPipelineConfigReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetPipelineConfigResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64                          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Stages []*GetPipelineConfigResp_Stage `protobuf:"bytes,2,rep,name=stages,proto3" json:"stages,omitempty"`
}

func (x *GetPipelineConfigResp) Reset() {
	*x = GetPipelineConfigResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineConfigResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineConfigResp) ProtoMessage() {}

func (x *GetPipelineConfigResp) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineConfigResp.ProtoReflect.Descriptor instead.
func (*GetPipelineConfigResp) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{9}
}

func (x *GetPipelineConfigResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPipelineConfigResp) GetStages() []*GetPipelineConfigResp_Stage {
	if x != nil {
		return x.Stages
	}
	return nil
}

type UpdatePipelineTaskConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"pipelineRunId"
	PipelineRunId int64 `protobuf:"varint,1,opt,name=pipelineRunId,proto3" json:"pipelineRunId"`
	// @gotags: json:"taskId"
	TaskId int64 `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"taskId"`
	// @gotags: json:"taskRunId"
	TaskRunId int64 `protobuf:"varint,3,opt,name=task_run_id,json=taskRunId,proto3" json:"taskRunId"`
	// @gotags: json:"updatedPipelineConfig"
	UpdatedPipelineConfig []byte `protobuf:"bytes,4,opt,name=updated_pipeline_config,json=updatedPipelineConfig,proto3" json:"updatedPipelineConfig"`
	// @gotags: json:"updatedTaskRunConfig"
	UpdatedTaskRunConfig []byte `protobuf:"bytes,5,opt,name=updated_task_run_config,json=updatedTaskRunConfig,proto3" json:"updatedTaskRunConfig"`
}

func (x *UpdatePipelineTaskConfigReq) Reset() {
	*x = UpdatePipelineTaskConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePipelineTaskConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePipelineTaskConfigReq) ProtoMessage() {}

func (x *UpdatePipelineTaskConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePipelineTaskConfigReq.ProtoReflect.Descriptor instead.
func (*UpdatePipelineTaskConfigReq) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{10}
}

func (x *UpdatePipelineTaskConfigReq) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *UpdatePipelineTaskConfigReq) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *UpdatePipelineTaskConfigReq) GetTaskRunId() int64 {
	if x != nil {
		return x.TaskRunId
	}
	return 0
}

func (x *UpdatePipelineTaskConfigReq) GetUpdatedPipelineConfig() []byte {
	if x != nil {
		return x.UpdatedPipelineConfig
	}
	return nil
}

func (x *UpdatePipelineTaskConfigReq) GetUpdatedTaskRunConfig() []byte {
	if x != nil {
		return x.UpdatedTaskRunConfig
	}
	return nil
}

type UpdatePipelineTaskMultiCloudConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @gotags: json:"pipelineRunId"
	PipelineRunId int64 `protobuf:"varint,1,opt,name=pipelineRunId,proto3" json:"pipelineRunId"`
	// @gotags: json:"stageRunId"
	StageRunId int64 `protobuf:"varint,3,opt,name=stage_run_id,json=stageRunId,proto3" json:"stageRunId"`
	// @gotags: json:"updatedTaskRunMultiCloudConfig"
	UpdatedTaskRunMultiCloudConfig []byte `protobuf:"bytes,5,opt,name=updated_task_run_multi_cloud_config,json=updatedTaskRunMultiCloudConfig,proto3" json:"updatedTaskRunMultiCloudConfig"`
}

func (x *UpdatePipelineTaskMultiCloudConfigReq) Reset() {
	*x = UpdatePipelineTaskMultiCloudConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePipelineTaskMultiCloudConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePipelineTaskMultiCloudConfigReq) ProtoMessage() {}

func (x *UpdatePipelineTaskMultiCloudConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePipelineTaskMultiCloudConfigReq.ProtoReflect.Descriptor instead.
func (*UpdatePipelineTaskMultiCloudConfigReq) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{11}
}

func (x *UpdatePipelineTaskMultiCloudConfigReq) GetPipelineRunId() int64 {
	if x != nil {
		return x.PipelineRunId
	}
	return 0
}

func (x *UpdatePipelineTaskMultiCloudConfigReq) GetStageRunId() int64 {
	if x != nil {
		return x.StageRunId
	}
	return 0
}

func (x *UpdatePipelineTaskMultiCloudConfigReq) GetUpdatedTaskRunMultiCloudConfig() []byte {
	if x != nil {
		return x.UpdatedTaskRunMultiCloudConfig
	}
	return nil
}

type PipelineAppsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppIds   []int64 `protobuf:"varint,1,rep,packed,name=app_ids,json=appIds,proto3" json:"app_ids,omitempty"`
	RepoAddr string  `protobuf:"bytes,4,opt,name=repo_addr,json=repoAddr,proto3" json:"repo_addr,omitempty"`
}

func (x *PipelineAppsReq) Reset() {
	*x = PipelineAppsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineAppsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineAppsReq) ProtoMessage() {}

func (x *PipelineAppsReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineAppsReq.ProtoReflect.Descriptor instead.
func (*PipelineAppsReq) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{12}
}

func (x *PipelineAppsReq) GetAppIds() []int64 {
	if x != nil {
		return x.AppIds
	}
	return nil
}

func (x *PipelineAppsReq) GetRepoAddr() string {
	if x != nil {
		return x.RepoAddr
	}
	return ""
}

type RunPipelingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId        int64  `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	PipelineName string `protobuf:"bytes,2,opt,name=pipeline_name,json=pipelineName,proto3" json:"pipeline_name,omitempty"`
	Branch       string `protobuf:"bytes,3,opt,name=branch,proto3" json:"branch,omitempty"`
	UserId       int64  `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ChineseName  string `protobuf:"bytes,5,opt,name=chineseName,proto3" json:"chineseName,omitempty"`
	EmployeeNo   string `protobuf:"bytes,6,opt,name=employeeNo,proto3" json:"employeeNo,omitempty"`
	Description  string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *RunPipelingReq) Reset() {
	*x = RunPipelingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunPipelingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunPipelingReq) ProtoMessage() {}

func (x *RunPipelingReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunPipelingReq.ProtoReflect.Descriptor instead.
func (*RunPipelingReq) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{13}
}

func (x *RunPipelingReq) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *RunPipelingReq) GetPipelineName() string {
	if x != nil {
		return x.PipelineName
	}
	return ""
}

func (x *RunPipelingReq) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *RunPipelingReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *RunPipelingReq) GetChineseName() string {
	if x != nil {
		return x.ChineseName
	}
	return ""
}

func (x *RunPipelingReq) GetEmployeeNo() string {
	if x != nil {
		return x.EmployeeNo
	}
	return ""
}

func (x *RunPipelingReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type PrepareingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PrepareingReq) Reset() {
	*x = PrepareingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrepareingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrepareingReq) ProtoMessage() {}

func (x *PrepareingReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrepareingReq.ProtoReflect.Descriptor instead.
func (*PrepareingReq) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{14}
}

type PrepareingResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PrepareingResp) Reset() {
	*x = PrepareingResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrepareingResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrepareingResp) ProtoMessage() {}

func (x *PrepareingResp) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrepareingResp.ProtoReflect.Descriptor instead.
func (*PrepareingResp) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{15}
}

type DelPipelineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId *int64 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3,oneof" json:"app_id,omitempty"`
	PrjId *int64 `protobuf:"varint,2,opt,name=prj_id,json=prjId,proto3,oneof" json:"prj_id,omitempty"`
}

func (x *DelPipelineReq) Reset() {
	*x = DelPipelineReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelPipelineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelPipelineReq) ProtoMessage() {}

func (x *DelPipelineReq) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelPipelineReq.ProtoReflect.Descriptor instead.
func (*DelPipelineReq) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{16}
}

func (x *DelPipelineReq) GetAppId() int64 {
	if x != nil && x.AppId != nil {
		return *x.AppId
	}
	return 0
}

func (x *DelPipelineReq) GetPrjId() int64 {
	if x != nil && x.PrjId != nil {
		return *x.PrjId
	}
	return 0
}

type GetPipelineConfigResp_Stage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64                         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name  string                        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Tasks []*GetPipelineConfigResp_Task `protobuf:"bytes,3,rep,name=tasks,proto3" json:"tasks,omitempty"`
	Type  string                        `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *GetPipelineConfigResp_Stage) Reset() {
	*x = GetPipelineConfigResp_Stage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineConfigResp_Stage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineConfigResp_Stage) ProtoMessage() {}

func (x *GetPipelineConfigResp_Stage) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineConfigResp_Stage.ProtoReflect.Descriptor instead.
func (*GetPipelineConfigResp_Stage) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{9, 0}
}

func (x *GetPipelineConfigResp_Stage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPipelineConfigResp_Stage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPipelineConfigResp_Stage) GetTasks() []*GetPipelineConfigResp_Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *GetPipelineConfigResp_Stage) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type GetPipelineConfigResp_Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name   string           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Config *structpb.Struct `protobuf:"bytes,3,opt,name=config,proto3" json:"config,omitempty"`
	Type   string           `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *GetPipelineConfigResp_Task) Reset() {
	*x = GetPipelineConfigResp_Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protocol_pipeline_pipeline_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineConfigResp_Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineConfigResp_Task) ProtoMessage() {}

func (x *GetPipelineConfigResp_Task) ProtoReflect() protoreflect.Message {
	mi := &file_protocol_pipeline_pipeline_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineConfigResp_Task.ProtoReflect.Descriptor instead.
func (*GetPipelineConfigResp_Task) Descriptor() ([]byte, []int) {
	return file_protocol_pipeline_pipeline_proto_rawDescGZIP(), []int{9, 1}
}

func (x *GetPipelineConfigResp_Task) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPipelineConfigResp_Task) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPipelineConfigResp_Task) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *GetPipelineConfigResp_Task) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

var File_protocol_pipeline_pipeline_proto protoreflect.FileDescriptor

var file_protocol_pipeline_pipeline_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x08, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x31, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x74, 0x68, 0x69, 0x72, 0x64, 0x2d, 0x70, 0x61, 0x72,
	0x74, 0x79, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x30, 0x0a, 0x08,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x22, 0x2e,
	0x0a, 0x04, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x47,
	0x0a, 0x0d, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x72, 0x72, 0x61, 0x79, 0x12,
	0x36, 0x0a, 0x09, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x09, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x22, 0x68, 0x0a, 0x0e, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x22, 0x2a, 0x0a, 0x10, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x49, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x61, 0x70, 0x70, 0x49, 0x64, 0x73, 0x22, 0xc5, 0x01,
	0x0a, 0x0e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1a,
	0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x98, 0x01, 0x0a, 0x11, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x09, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4d, 0x61, 0x70, 0x1a, 0x3b, 0x0a, 0x0d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x37, 0x0a, 0x0f, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70, 0x70, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x26, 0x0a, 0x14, 0x47, 0x65, 0x74,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x22, 0xd4, 0x02, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x67, 0x65, 0x73, 0x1a, 0x7b, 0x0a, 0x05, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x74, 0x61,
	0x73, 0x6b, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x1a, 0x6f, 0x0a, 0x04, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0xeb, 0x01, 0x0a, 0x1b, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61,
	0x73, 0x6b, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x17, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x15, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x35, 0x0a, 0x17, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x72, 0x75, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x14, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xbc, 0x01, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x75, 0x6c,
	0x74, 0x69, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71,
	0x12, 0x24, 0x0a, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x75, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f,
	0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x23, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x5f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x1e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x75, 0x6e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x47, 0x0a, 0x0f, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x41, 0x70, 0x70, 0x73, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x22, 0xe1,
	0x01, 0x0a, 0x0e, 0x52, 0x75, 0x6e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x4e, 0x6f,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x0f, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x22, 0x10, 0x0a, 0x0e, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x5e, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06, 0x70, 0x72, 0x6a, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x05, 0x70, 0x72, 0x6a, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70,
	0x72, 0x6a, 0x5f, 0x69, 0x64, 0x32, 0xa6, 0x07, 0x0a, 0x0f, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x4e, 0x65, 0x77,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x18, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x54, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x79, 0x41, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x12, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x17, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x72, 0x72, 0x61, 0x79, 0x12,
	0x2d, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x79, 0x49, 0x64, 0x12, 0x0e,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x1a, 0x0e,
	0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x4b,
	0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x41, 0x70, 0x70, 0x4d, 0x73, 0x67, 0x12, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71,
	0x1a, 0x19, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x59, 0x0a, 0x18, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x25, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6d, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2f, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43,
	0x6c, 0x6f, 0x75, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x53, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x41, 0x70, 0x70, 0x49, 0x64,
	0x73, 0x12, 0x1a, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e,
	0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x19, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x41, 0x70, 0x70, 0x4d, 0x73, 0x67, 0x12, 0x19, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70, 0x70, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4d, 0x0a,
	0x18, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x69, 0x6e,
	0x67, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x17, 0x2e, 0x70, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x72,
	0x65, 0x70, 0x61, 0x72, 0x65, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0b,
	0x52, 0x75, 0x6e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x18, 0x2e, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x52, 0x75, 0x6e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3f, 0x0a,
	0x0b, 0x44, 0x65, 0x6c, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x18, 0x2e, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x13,
	0x5a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protocol_pipeline_pipeline_proto_rawDescOnce sync.Once
	file_protocol_pipeline_pipeline_proto_rawDescData = file_protocol_pipeline_pipeline_proto_rawDesc
)

func file_protocol_pipeline_pipeline_proto_rawDescGZIP() []byte {
	file_protocol_pipeline_pipeline_proto_rawDescOnce.Do(func() {
		file_protocol_pipeline_pipeline_proto_rawDescData = protoimpl.X.CompressGZIP(file_protocol_pipeline_pipeline_proto_rawDescData)
	})
	return file_protocol_pipeline_pipeline_proto_rawDescData
}

var file_protocol_pipeline_pipeline_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_protocol_pipeline_pipeline_proto_goTypes = []any{
	(*Pipeline)(nil),                              // 0: pipeline.Pipeline
	(*Task)(nil),                                  // 1: pipeline.Task
	(*PipelineArray)(nil),                         // 2: pipeline.PipelineArray
	(*PipelineResult)(nil),                        // 3: pipeline.PipelineResult
	(*PipelineCountReq)(nil),                      // 4: pipeline.PipelineCountReq
	(*PipelineAppReq)(nil),                        // 5: pipeline.PipelineAppReq
	(*PipelineCountResp)(nil),                     // 6: pipeline.PipelineCountResp
	(*PipelineAppResp)(nil),                       // 7: pipeline.PipelineAppResp
	(*GetPipelineConfigReq)(nil),                  // 8: pipeline.GetPipelineConfigReq
	(*GetPipelineConfigResp)(nil),                 // 9: pipeline.GetPipelineConfigResp
	(*UpdatePipelineTaskConfigReq)(nil),           // 10: pipeline.UpdatePipelineTaskConfigReq
	(*UpdatePipelineTaskMultiCloudConfigReq)(nil), // 11: pipeline.UpdatePipelineTaskMultiCloudConfigReq
	(*PipelineAppsReq)(nil),                       // 12: pipeline.PipelineAppsReq
	(*RunPipelingReq)(nil),                        // 13: pipeline.RunPipelingReq
	(*PrepareingReq)(nil),                         // 14: pipeline.PrepareingReq
	(*PrepareingResp)(nil),                        // 15: pipeline.PrepareingResp
	(*DelPipelineReq)(nil),                        // 16: pipeline.DelPipelineReq
	nil,                                           // 17: pipeline.PipelineCountResp.CountMapEntry
	(*GetPipelineConfigResp_Stage)(nil),           // 18: pipeline.GetPipelineConfigResp.Stage
	(*GetPipelineConfigResp_Task)(nil),            // 19: pipeline.GetPipelineConfigResp.Task
	(*structpb.Struct)(nil),                       // 20: google.protobuf.Struct
	(*emptypb.Empty)(nil),                         // 21: google.protobuf.Empty
}
var file_protocol_pipeline_pipeline_proto_depIdxs = []int32{
	3,  // 0: pipeline.PipelineArray.pipelines:type_name -> pipeline.PipelineResult
	17, // 1: pipeline.PipelineCountResp.count_map:type_name -> pipeline.PipelineCountResp.CountMapEntry
	18, // 2: pipeline.GetPipelineConfigResp.stages:type_name -> pipeline.GetPipelineConfigResp.Stage
	19, // 3: pipeline.GetPipelineConfigResp.Stage.tasks:type_name -> pipeline.GetPipelineConfigResp.Task
	20, // 4: pipeline.GetPipelineConfigResp.Task.config:type_name -> google.protobuf.Struct
	0,  // 5: pipeline.PipelineService.NewPipeline:input_type -> pipeline.Pipeline
	8,  // 6: pipeline.PipelineService.GetPipelineConfig:input_type -> pipeline.GetPipelineConfigReq
	0,  // 7: pipeline.PipelineService.GetPipelineByAppId:input_type -> pipeline.Pipeline
	1,  // 8: pipeline.PipelineService.GetTaskById:input_type -> pipeline.Task
	5,  // 9: pipeline.PipelineService.UpdatePipelineAppMsg:input_type -> pipeline.PipelineAppReq
	10, // 10: pipeline.PipelineService.UpdatePipelineTaskConfig:input_type -> pipeline.UpdatePipelineTaskConfigReq
	11, // 11: pipeline.PipelineService.UpdatePipelineTaskMultiCloudConfig:input_type -> pipeline.UpdatePipelineTaskMultiCloudConfigReq
	4,  // 12: pipeline.PipelineService.GetPipelineCountByAppIds:input_type -> pipeline.PipelineCountReq
	12, // 13: pipeline.PipelineService.BatchUpdatePipelineAppMsg:input_type -> pipeline.PipelineAppsReq
	14, // 14: pipeline.PipelineService.HandlePrepareingPipeline:input_type -> pipeline.PrepareingReq
	13, // 15: pipeline.PipelineService.RunPipeline:input_type -> pipeline.RunPipelingReq
	16, // 16: pipeline.PipelineService.DelPipeline:input_type -> pipeline.DelPipelineReq
	3,  // 17: pipeline.PipelineService.NewPipeline:output_type -> pipeline.PipelineResult
	9,  // 18: pipeline.PipelineService.GetPipelineConfig:output_type -> pipeline.GetPipelineConfigResp
	2,  // 19: pipeline.PipelineService.GetPipelineByAppId:output_type -> pipeline.PipelineArray
	1,  // 20: pipeline.PipelineService.GetTaskById:output_type -> pipeline.Task
	7,  // 21: pipeline.PipelineService.UpdatePipelineAppMsg:output_type -> pipeline.PipelineAppResp
	21, // 22: pipeline.PipelineService.UpdatePipelineTaskConfig:output_type -> google.protobuf.Empty
	21, // 23: pipeline.PipelineService.UpdatePipelineTaskMultiCloudConfig:output_type -> google.protobuf.Empty
	6,  // 24: pipeline.PipelineService.GetPipelineCountByAppIds:output_type -> pipeline.PipelineCountResp
	7,  // 25: pipeline.PipelineService.BatchUpdatePipelineAppMsg:output_type -> pipeline.PipelineAppResp
	15, // 26: pipeline.PipelineService.HandlePrepareingPipeline:output_type -> pipeline.PrepareingResp
	21, // 27: pipeline.PipelineService.RunPipeline:output_type -> google.protobuf.Empty
	21, // 28: pipeline.PipelineService.DelPipeline:output_type -> google.protobuf.Empty
	17, // [17:29] is the sub-list for method output_type
	5,  // [5:17] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_protocol_pipeline_pipeline_proto_init() }
func file_protocol_pipeline_pipeline_proto_init() {
	if File_protocol_pipeline_pipeline_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protocol_pipeline_pipeline_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Pipeline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*PipelineArray); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*PipelineResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*PipelineCountReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*PipelineAppReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*PipelineCountResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*PipelineAppResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*GetPipelineConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GetPipelineConfigResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*UpdatePipelineTaskConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*UpdatePipelineTaskMultiCloudConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*PipelineAppsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*RunPipelingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*PrepareingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*PrepareingResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*DelPipelineReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*GetPipelineConfigResp_Stage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protocol_pipeline_pipeline_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*GetPipelineConfigResp_Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_protocol_pipeline_pipeline_proto_msgTypes[16].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protocol_pipeline_pipeline_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protocol_pipeline_pipeline_proto_goTypes,
		DependencyIndexes: file_protocol_pipeline_pipeline_proto_depIdxs,
		MessageInfos:      file_protocol_pipeline_pipeline_proto_msgTypes,
	}.Build()
	File_protocol_pipeline_pipeline_proto = out.File
	file_protocol_pipeline_pipeline_proto_rawDesc = nil
	file_protocol_pipeline_pipeline_proto_goTypes = nil
	file_protocol_pipeline_pipeline_proto_depIdxs = nil
}
