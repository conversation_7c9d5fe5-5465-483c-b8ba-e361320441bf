package middleware

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/http/httputil"
	"net/url"
	"regexp"
	"strconv"
	"strings"

	"52tt.com/cicd/pkg/admin"
	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/para"
	pbiam "52tt.com/cicd/protocol/iam"
	"52tt.com/cicd/services/bff/internal/conf"
	"52tt.com/cicd/services/bff/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/koding/websocketproxy"
	"github.com/tidwall/gjson"
)

func RoutesInterceptor(c *conf.Config, us service.UserService) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		method := ctx.Request.Method
		path := ctx.Request.URL.Path
		if !c.WithinWhitelists(method, path) {
			ctxWrapper := para.WrapperContext(ctx, true, "鉴权")
			openapiToken := cctx.OpenapiTokenFromGinHeader(ctx)
			// openapi token 校验，提供给外部接口的校验
			if openapiToken != "" {
				if err := us.CheckOpenapiAuth(context.Background(), openapiToken); err != nil {
					ctxWrapper.Failf(http.StatusUnauthorized, "token校验失败")
					return
				}
				roles := []string{"OPENAPI"}
				// openapi token校验通过，设置OPENAPI角色，进行权限校验
				//authorized := c.WithinAuthorized(method, path, roles)
				//if !authorized {
				//	ctxWrapper.Failf(http.StatusForbidden, "权限不足")
				//	return
				//}
				cctx.SetRequestInfo(ctx, &cctx.RequestInfo{
					UserInfo: cctx.UserInfo{
						UserID:      0,
						Username:    "third-party",
						ChineseName: "第三方接口调用",
						EmployeeNo:  "ThirdParty",
						Roles:       roles,
					},
					RequestID: cctx.RequestIDFromGinHeader(ctx),
				})
			} else {
				token := cctx.TokenFromGinHeader(ctx)
				if isSwitchingToWs(ctx.Request) {
					token = ctx.GetHeader("Sec-WebSocket-Protocol")
				}
				if token == "" {
					ctxWrapper.Failf(http.StatusUnauthorized, "token缺失")
					return
				}
				//md := metadata.Pairs(cctx.AuthorizationKey, token)
				newCtx := cctx.WithToken(context.Background(), token)
				prjId := parsePrjId(ctx, c)
				i, err := us.GetUserinfo(newCtx, prjId)
				if err != nil {
					log.Errorf("token校验失败: %v", err)
					ctxWrapper.Failf(http.StatusUnauthorized, "token校验失败")
					return
				}
				if !i.HasDataPerms {
					ctxWrapper.Failf(http.StatusForbidden, "数据权限不足")
					return
				}
				combinedRoles := getCombinedRoles(i)
				authorized := c.WithinAuthorized(method, path, combinedRoles)
				if !authorized {
					ctxWrapper.Failf(http.StatusForbidden, "权限不足")
					return
				}
				// 把相关信息放入gin.Context往下传递
				ri := &cctx.RequestInfo{
					UserInfo: cctx.UserInfo{
						UserID:      i.UserInfo.Id,
						Username:    i.UserInfo.Username,
						ChineseName: i.UserInfo.ChineseName,
						EmployeeNo:  i.UserInfo.EmployeeNo,
						Roles:       combinedRoles,
						ProjectID:   i.CurrentProject.Id,
					},
					RequestID: cctx.RequestIDFromGinHeader(ctx),
				}
				cctx.SetRequestInfo(ctx, ri)
			}
		}
		proxyContext := c.ProxyContext(path)
		if proxyContext.ProxyUrl != "" {
			proxyForward(proxyContext, ctx)
			return
		}
		ctx.Next()
	}
}

func getCombinedRoles(i *pbiam.GetOwnUserinfoResp) []string {
	combinedRoles := i.UserInfo.Role
	if i.CurrentProject != nil && len(strings.TrimSpace(i.CurrentProject.Role)) != 0 {
		combinedRoles = strings.Join([]string{combinedRoles, i.CurrentProject.Role}, " ")
	}
	return strings.Split(strings.TrimSpace(combinedRoles), " ")
}

type Query struct {
	ProjectId int64 `json:"projectId" form:"projectId"`
}

// URL  /projects/31/  里解析projectId
var rePrjId = regexp.MustCompile(`/projects/(\d+)`)

func parsePrjId(ctx *gin.Context, c *conf.Config) (prjId int64) {
	var pQuery Query
	ctxWrapper := para.WrapperContext(ctx, true, "查询应用列表(不分页)")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindQuery,
		ctx.ShouldBindHeader,
	)
	ctxWrapper.Bind(&pQuery)
	if pQuery.ProjectId > 0 {
		prjId = pQuery.ProjectId
		return
	}

	matches := rePrjId.FindStringSubmatch(ctx.Request.URL.Path)
	if len(matches) > 1 {
		projectId, _ := strconv.Atoi(matches[1])
		if projectId > 0 {
			prjId = int64(projectId)
			return
		}
	}
	// Body 体里解析,略重略重，而且可能此PrjID非彼PrjID 需要具体业务接口含义
	method := ctx.Request.Method
	path := ctx.Request.URL.Path
	jsonPath, in := c.InAuthBodyApis(method, path)
	if in && jsonPath != "" {
		var bodyBytes []byte
		if ctx.Request.Body != nil {
			bodyBytes, _ = io.ReadAll(ctx.Request.Body)
		}
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

		if len(bodyBytes) > 0 {
			prjId = gjson.ParseBytes(bodyBytes).Get(jsonPath).Int()
			if prjId > 0 {
				return
			}
		}
	}
	return
}

func proxyForward(pc *conf.ProxyContext, ctx *gin.Context) {
	r := ctx.Request
	w := ctx.Writer
	target, err := url.Parse(pc.ProxyUrl)
	if err != nil {
		panic(err)
	}

	r.URL.Path = pc.RewritePath + strings.TrimPrefix(r.URL.Path, pc.StripPrefix)
	r.RequestURI = pc.RewritePath + strings.TrimPrefix(r.RequestURI, pc.StripPrefix)
	r.URL.Scheme = target.Scheme
	r.URL.Host = target.Host
	r.Host = target.Host

	// forward headers
	cctx.ForwardHeaderRequestInfo(ctx)

	log.Infof("proxy forward request %s %s to %s", r.Method, r.RequestURI, target)
	if isSwitchingToWs(r) {
		target.Scheme = "ws"
		proxy := websocketproxy.NewProxy(target)
		proxy.Director = func(incoming *http.Request, out http.Header) {
			header := incoming.Header
			headerKeys := []string{cctx.UseridHeaderKey, cctx.UsernameHeaderKey, cctx.ChineseNameHeaderKey,
				cctx.EmployeeNoHeaderKey, cctx.RolesHeaderKey, cctx.ProjectIdHeaderKey, cctx.RequestIDHeaderKey}
			for _, v := range headerKeys {
				out.Set(v, header.Get(v))
			}
			log.Debugf("request headers: %v", out)
		}
		proxy.Upgrader = &websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
		}
		proxy.ServeHTTP(w, r)
		return
	}

	proxy := httputil.NewSingleHostReverseProxy(target)
	originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		originalDirector(req)
		log.Debugf("request headers: %v", req.Header)
	}
	proxy.ModifyResponse = func(resp *http.Response) error {
		log.Debugf("response headers: %v", resp.Header)
		// 代理请求头中有请求id，原始请求头中也有请求id；返回之前会复制代理请求头的信息到原始请求头进行合并(add)
		// 所以需要删除代理请求头中的X-Request-ID
		resp.Header.Del(cctx.RequestIDHeaderKey)
		return nil
	}
	proxy.ServeHTTP(w, r)
}

func isSwitchingToWs(r *http.Request) bool {
	return strings.EqualFold(r.Header.Get("Connection"), "Upgrade") &&
		strings.EqualFold(r.Header.Get("Upgrade"), "websocket")
}

func RoutesMetrics(c *conf.Config) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		metricsPre := "/admin_metrics"

		path := strings.TrimPrefix(ctx.Request.URL.Path, metricsPre)
		pc := c.ProxyContext(path)

		if pc.ProxyUrl != "" {
			r := ctx.Request
			w := ctx.Writer

			target, err := url.Parse(pc.ProxyUrl)
			if err != nil {
				panic(err)
			}
			hostArr := strings.Split(target.Host, ":")
			if len(hostArr) == 0 {
				panic("hostArr panic" + target.Host)
			}

			target.Host = hostArr[0] + admin.DefaultAdminAddr
			prefix := fmt.Sprintf("%s/%s", metricsPre, pc.SerName)

			r.URL.Path = strings.TrimPrefix(r.URL.Path, prefix)
			r.RequestURI = strings.TrimPrefix(r.RequestURI, prefix)
			r.URL.Scheme = target.Scheme
			r.URL.Host = target.Host
			r.Host = target.Host

			proxy := httputil.NewSingleHostReverseProxy(target)
			proxy.ServeHTTP(w, r)
			return
		}
		ctx.Next()
	}
}
