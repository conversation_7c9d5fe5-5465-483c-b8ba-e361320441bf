package chain

import (
	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/tools"
	"52tt.com/cicd/services/deploy/internal/conf"
	"52tt.com/cicd/services/deploy/internal/model"
	"context"
	"encoding/json"
	"fmt"
	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"istio.io/client-go/pkg/apis/networking/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"strings"
)

type SidecarHandleRequest struct {
	Cluster   string
	Namespace string
	Senv      string
	AppID     int64
	AppName   string
	Env       constants.EnumEnv
	EnvTarget constants.EnumEnvTarget
}

var StepNameSidecar = "sidecar"

// SidecarOnlineHandle 处理 sidecar 上线
func SidecarOnlineHandle(request *SidecarHandleRequest) HandlerFunc {
	return func(ctx context.Context, m *Manager) error {
		log.InfoWithCtx(ctx, "[SidecarOnlineHandle] start, params: %+v", request)
		if !checkIsTrigger(request.Namespace) {
			log.ErrorWithCtx(ctx, "[SidecarOnlineHandle] namespace[%v] not in sidecar trigger namespace", request.Namespace)
			return nil
		}

		// 1. 获取默认sidecar
		defaultHosts := make([]string, 0)
		if request.Env == constants.EnumEnvTesting {
			params := &model.RunningChangeLogParams{
				AppID:     request.AppID,
				Cluster:   request.Cluster,
				Senv:      request.Senv,
				Namespace: request.Namespace,
				EnvTarget: request.EnvTarget,
			}
			currentCl, err := m.clRepo.FindRunningChangeLog(ctx, params)
			if err != nil {
				log.ErrorWithCtx(ctx, "[SidecarOnlineHandle] Error fetching running change log: %v", err)
				return err
			}
			if currentCl != nil {
				log.ErrorWithCtx(ctx, "[SidecarOnlineHandle] has running change log found for params: %+v", params)
				return nil
			}
			// 测试环境是新服务，创建sidecar
			defaultHosts = []string{
				"istio-ecosystem/*",
				"external/*",
			}
		} else if request.Env == constants.EnumEnvProduction {
			testingEnvSidecar, err := getRelatedTestingSidecar(ctx, m, request)
			if err != nil {
				return err
			}
			// 如果没有测试环境sidecar，则不创建sidecar
			if testingEnvSidecar == nil {
				log.InfoWithCtx(ctx, "[SidecarOnlineHandle] No testing env sidecar found for params: %+v", request)
				return fmt.Errorf("关联的测试环境sidecar不存在")
			}
			for _, egress := range testingEnvSidecar.Spec.Egress {
				for _, host := range egress.Hosts {
					defaultHosts = append(defaultHosts, host)
				}
			}
		} else {
			// 非测试环境和生产环境，不创建sidecar
			return nil
		}

		// 2. 校验是否已存在sidecar
		isExisted := false
		existedHosts := make([]string, 0)
		getReq := &getSidecarReq{
			Cluster:   request.Cluster,
			Namespace: request.Namespace,
			AppName:   request.AppName,
		}
		realSidecar, err := getSidecarResult(ctx, m, getReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "[SidecarOnlineHandle] Error get existed sidecar: %v, params: %+v", err, getReq)
			return err
		}
		if realSidecar == nil {
			log.InfoWithCtx(ctx, "[SidecarOnlineHandle] sidecar not existed, params: %+v", getReq)
		} else {
			isExisted = true
			for _, egress := range realSidecar.Spec.Egress {
				for _, host := range egress.Hosts {
					existedHosts = append(existedHosts, host)
				}
			}
		}
		sidecarVal := generateSidecar(request, defaultHosts, existedHosts)
		data, err := json.Marshal(sidecarVal)
		if err != nil {
			return err
		}

		if !isExisted {
			req := &constack.CreateRequest{
				Cluster:   request.Cluster,
				Namespace: request.Namespace,
				Data:      string(data),
			}
			_, err = m.CloudAggClient.Create(ctx, req)
		} else {
			req := &constack.UpdateRequest{
				Cluster:   request.Cluster,
				Namespace: request.Namespace,
				Data:      string(data),
			}
			_, err = m.CloudAggClient.Update(ctx, req)
		}
		if err != nil {
			log.ErrorWithCtx(ctx, "[SidecarOnlineHandle] Error create/update sidecar: %v, params: %+v", err, request)
			return err
		}

		// 同步sidecar到统一集群
		_, err = m.CloudAggClient.SyncUnifiedClusterSidecar(ctx, &constack.SyncUnifiedClusterSidecarReq{
			Cluster:   request.Cluster,
			Namespace: request.Namespace,
			Name:      request.AppName,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "[SidecarOnlineHandle] Error sync sidecar to unified cluster: %v, params: %+v", err, request)
			return err
		}

		return nil
	}
}

func checkIsTrigger(namespace string) bool {
	nsList := strings.Split(conf.DeployConfig.SidecarTrigger.Namespace, ",")
	return tools.Contains(nsList, namespace)
}

type getSidecarReq struct {
	Cluster   string
	Namespace string
	AppName   string
}

// getRelatedTestingSidecar 查找同一 namespace 下测试环境的sidecar
func getRelatedTestingSidecar(ctx context.Context, m *Manager, request *SidecarHandleRequest) (*v1beta1.Sidecar, error) {
	isCurrent := 1
	params := &model.ChangeLogParams{
		AppID:     request.AppID,
		Namespace: request.Namespace,
		IsCurrent: &isCurrent,
		Env:       constants.EnumEnvTesting.Value(),
	}
	cls, err := m.clRepo.FindChangeLogBy(ctx, params)
	if err != nil {
		log.ErrorWithCtx(ctx, "[SidecarOnlineHandle] Error fetching change log: %v, params: %+v", err, params)
		return nil, err
	}
	if len(cls) == 0 {
		log.InfoWithCtx(ctx, "[SidecarOnlineHandle] No testing env change log found for params: %+v", params)
		return nil, nil
	}

	testingCl := cls[0]
	getReq := &getSidecarReq{
		Cluster:   testingCl.Cluster,
		Namespace: testingCl.Namespace,
		AppName:   testingCl.AppName,
	}
	return getSidecarResult(ctx, m, getReq)
}

func getSidecarResult(ctx context.Context, m *Manager, req *getSidecarReq) (*v1beta1.Sidecar, error) {
	getReq := &constack.GetRequest{
		Cluster:              req.Cluster,
		Namespace:            req.Namespace,
		Name:                 req.AppName,
		GroupVersionResource: &constack.GroupVersionResource{Group: "networking.istio.io", Version: "v1beta1", Resource: "sidecars"},
	}
	getSidecar, err := m.CloudAggClient.Get(ctx, getReq)
	if err != nil {
		if status.Code(err) == codes.NotFound {
			log.InfoWithCtx(ctx, "[SidecarOnlineHandle] sidecar not existed, params: %+v", getReq)
			return nil, nil
		} else {
			log.ErrorWithCtx(ctx, "[SidecarOnlineHandle]	Error get sidecar: %v, params: %+v", err, getReq)
			return nil, err
		}
	}
	realSidecar := &v1beta1.Sidecar{}
	err = json.Unmarshal([]byte(getSidecar.Data), realSidecar)
	if err != nil {
		log.ErrorWithCtx(ctx, "[SidecarOnlineHandle]	Error unmarshal sidecar: %v, sidecarData: %v", err, getSidecar)
		return nil, err
	}

	return realSidecar, nil
}

type SidecarDetail struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              SidecarSpec `json:"spec,omitempty"`
}

type SidecarSpec struct {
	Egress []*SidecarEgress `json:"egress,omitempty"`
	// +optional
	WorkloadSelector *SidecarWorkloadSelector `json:"workloadSelector,omitempty"`
}

type SidecarWorkloadSelector struct {
	Labels map[string]string `json:"labels,omitempty"`
}

type SidecarEgress struct {
	Hosts []string `json:"hosts,omitempty"`
}

func generateSidecar(request *SidecarHandleRequest, defaultHosts, existedHosts []string) SidecarDetail {
	hostsResult := make([]string, 0)
	hostsMap := make(map[string]struct{})
	for _, host := range existedHosts {
		if _, ok := hostsMap[host]; !ok {
			hostsMap[host] = struct{}{}
			hostsResult = append(hostsResult, host)
		}
	}
	for _, host := range defaultHosts {
		if _, ok := hostsMap[host]; !ok {
			hostsMap[host] = struct{}{}
			hostsResult = append(hostsResult, host)
		}
	}
	sidecar := SidecarDetail{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Sidecar",
			APIVersion: "networking.istio.io/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      request.AppName,
			Namespace: request.Namespace,
			Labels: map[string]string{
				string(constants.ResourceLogoLableKey): string(constants.ResourceLogoLableValue),
			},
		},
		Spec: SidecarSpec{
			Egress: []*SidecarEgress{
				{
					Hosts: hostsResult,
				},
			},
			WorkloadSelector: &SidecarWorkloadSelector{
				Labels: map[string]string{
					"app": request.AppName,
				},
			},
		},
	}

	return sidecar
}
