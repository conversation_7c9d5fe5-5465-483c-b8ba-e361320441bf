// Code generated by MockGen. DO NOT EDIT.
// Source: config_manager.go

// Package dao is a generated GoMock package.
package dao

import (
	context "context"
	reflect "reflect"

	model "52tt.com/cicd/services/app/internal/model"
	gomock "github.com/golang/mock/gomock"
)

// MockConfigManagerRepository is a mock of ConfigManagerRepository interface.
type MockConfigManagerRepository struct {
	ctrl     *gomock.Controller
	recorder *MockConfigManagerRepositoryMockRecorder
}

// MockConfigManagerRepositoryMockRecorder is the mock recorder for MockConfigManagerRepository.
type MockConfigManagerRepositoryMockRecorder struct {
	mock *MockConfigManagerRepository
}

// NewMockConfigManagerRepository creates a new mock instance.
func NewMockConfigManagerRepository(ctrl *gomock.Controller) *MockConfigManagerRepository {
	mock := &MockConfigManagerRepository{ctrl: ctrl}
	mock.recorder = &MockConfigManagerRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConfigManagerRepository) EXPECT() *MockConfigManagerRepositoryMockRecorder {
	return m.recorder
}

// CheckEnvConfigNameIsExisted mocks base method.
func (m *MockConfigManagerRepository) CheckEnvConfigNameIsExisted(ctx context.Context, req *model.CreateConfigParams) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckEnvConfigNameIsExisted", ctx, req)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckEnvConfigNameIsExisted indicates an expected call of CheckEnvConfigNameIsExisted.
func (mr *MockConfigManagerRepositoryMockRecorder) CheckEnvConfigNameIsExisted(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckEnvConfigNameIsExisted", reflect.TypeOf((*MockConfigManagerRepository)(nil).CheckEnvConfigNameIsExisted), ctx, req)
}

// CreateConfig mocks base method.
func (m *MockConfigManagerRepository) CreateConfig(ctx context.Context, config *Config) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateConfig", ctx, config)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateConfig indicates an expected call of CreateConfig.
func (mr *MockConfigManagerRepositoryMockRecorder) CreateConfig(ctx, config interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateConfig", reflect.TypeOf((*MockConfigManagerRepository)(nil).CreateConfig), ctx, config)
}

// CreateConfigApps mocks base method.
func (m *MockConfigManagerRepository) CreateConfigApps(ctx context.Context, apps []ConfigApp) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateConfigApps", ctx, apps)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateConfigApps indicates an expected call of CreateConfigApps.
func (mr *MockConfigManagerRepositoryMockRecorder) CreateConfigApps(ctx, apps interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateConfigApps", reflect.TypeOf((*MockConfigManagerRepository)(nil).CreateConfigApps), ctx, apps)
}

// CreateConfigVersion mocks base method.
func (m *MockConfigManagerRepository) CreateConfigVersion(ctx context.Context, version *ConfigVersion) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateConfigVersion", ctx, version)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateConfigVersion indicates an expected call of CreateConfigVersion.
func (mr *MockConfigManagerRepositoryMockRecorder) CreateConfigVersion(ctx, version interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateConfigVersion", reflect.TypeOf((*MockConfigManagerRepository)(nil).CreateConfigVersion), ctx, version)
}

// DeleteByApp mocks base method.
func (m *MockConfigManagerRepository) DeleteByApp(ctx context.Context, appId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByApp", ctx, appId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByApp indicates an expected call of DeleteByApp.
func (mr *MockConfigManagerRepositoryMockRecorder) DeleteByApp(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByApp", reflect.TypeOf((*MockConfigManagerRepository)(nil).DeleteByApp), ctx, appId)
}

// DeleteConfig mocks base method.
func (m *MockConfigManagerRepository) DeleteConfig(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteConfig", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteConfig indicates an expected call of DeleteConfig.
func (mr *MockConfigManagerRepositoryMockRecorder) DeleteConfig(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteConfig", reflect.TypeOf((*MockConfigManagerRepository)(nil).DeleteConfig), ctx, id)
}

// DeleteConfigApps mocks base method.
func (m *MockConfigManagerRepository) DeleteConfigApps(ctx context.Context, ids []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteConfigApps", ctx, ids)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteConfigApps indicates an expected call of DeleteConfigApps.
func (mr *MockConfigManagerRepositoryMockRecorder) DeleteConfigApps(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteConfigApps", reflect.TypeOf((*MockConfigManagerRepository)(nil).DeleteConfigApps), ctx, ids)
}

// GetConfig mocks base method.
func (m *MockConfigManagerRepository) GetConfig(ctx context.Context, id int64) (*Config, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfig", ctx, id)
	ret0, _ := ret[0].(*Config)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfig indicates an expected call of GetConfig.
func (mr *MockConfigManagerRepositoryMockRecorder) GetConfig(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfig", reflect.TypeOf((*MockConfigManagerRepository)(nil).GetConfig), ctx, id)
}

// GetConfigApp mocks base method.
func (m *MockConfigManagerRepository) GetConfigApp(ctx context.Context, query *model.ConfigAppsParams) (*ConfigApp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigApp", ctx, query)
	ret0, _ := ret[0].(*ConfigApp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigApp indicates an expected call of GetConfigApp.
func (mr *MockConfigManagerRepositoryMockRecorder) GetConfigApp(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigApp", reflect.TypeOf((*MockConfigManagerRepository)(nil).GetConfigApp), ctx, query)
}

// GetConfigBy mocks base method.
func (m *MockConfigManagerRepository) GetConfigBy(ctx context.Context, query *model.ConfigParams) (*Config, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigBy", ctx, query)
	ret0, _ := ret[0].(*Config)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigBy indicates an expected call of GetConfigBy.
func (mr *MockConfigManagerRepositoryMockRecorder) GetConfigBy(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigBy", reflect.TypeOf((*MockConfigManagerRepository)(nil).GetConfigBy), ctx, query)
}

// GetConfigVersion mocks base method.
func (m *MockConfigManagerRepository) GetConfigVersion(ctx context.Context, query *model.ConfigVersionParams) (*ConfigVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigVersion", ctx, query)
	ret0, _ := ret[0].(*ConfigVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigVersion indicates an expected call of GetConfigVersion.
func (mr *MockConfigManagerRepositoryMockRecorder) GetConfigVersion(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigVersion", reflect.TypeOf((*MockConfigManagerRepository)(nil).GetConfigVersion), ctx, query)
}

// GetConfigVersionPreload mocks base method.
func (m *MockConfigManagerRepository) GetConfigVersionPreload(ctx context.Context, query *model.ConfigVersionParams) (*ConfigVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigVersionPreload", ctx, query)
	ret0, _ := ret[0].(*ConfigVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigVersionPreload indicates an expected call of GetConfigVersionPreload.
func (mr *MockConfigManagerRepositoryMockRecorder) GetConfigVersionPreload(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigVersionPreload", reflect.TypeOf((*MockConfigManagerRepository)(nil).GetConfigVersionPreload), ctx, query)
}

// ListConfig mocks base method.
func (m *MockConfigManagerRepository) ListConfig(ctx context.Context, query *model.ConfigParams) ([]Config, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListConfig", ctx, query)
	ret0, _ := ret[0].([]Config)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListConfig indicates an expected call of ListConfig.
func (mr *MockConfigManagerRepositoryMockRecorder) ListConfig(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListConfig", reflect.TypeOf((*MockConfigManagerRepository)(nil).ListConfig), ctx, query)
}

// ListConfigApps mocks base method.
func (m *MockConfigManagerRepository) ListConfigApps(ctx context.Context, query *model.ConfigAppsParams) ([]ConfigApp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListConfigApps", ctx, query)
	ret0, _ := ret[0].([]ConfigApp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListConfigApps indicates an expected call of ListConfigApps.
func (mr *MockConfigManagerRepositoryMockRecorder) ListConfigApps(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListConfigApps", reflect.TypeOf((*MockConfigManagerRepository)(nil).ListConfigApps), ctx, query)
}

// ListConfigVersions mocks base method.
func (m *MockConfigManagerRepository) ListConfigVersions(ctx context.Context, configID int64) ([]ConfigVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListConfigVersions", ctx, configID)
	ret0, _ := ret[0].([]ConfigVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListConfigVersions indicates an expected call of ListConfigVersions.
func (mr *MockConfigManagerRepositoryMockRecorder) ListConfigVersions(ctx, configID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListConfigVersions", reflect.TypeOf((*MockConfigManagerRepository)(nil).ListConfigVersions), ctx, configID)
}

// PageConfig mocks base method.
func (m *MockConfigManagerRepository) PageConfig(ctx context.Context, query *model.QueryConfigParams, total *int64) ([]Config, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PageConfig", ctx, query, total)
	ret0, _ := ret[0].([]Config)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageConfig indicates an expected call of PageConfig.
func (mr *MockConfigManagerRepositoryMockRecorder) PageConfig(ctx, query, total interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageConfig", reflect.TypeOf((*MockConfigManagerRepository)(nil).PageConfig), ctx, query, total)
}

// PageConfigVersion mocks base method.
func (m *MockConfigManagerRepository) PageConfigVersion(ctx context.Context, query *model.QueryConfigVersionParams, total *int64) ([]ConfigVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PageConfigVersion", ctx, query, total)
	ret0, _ := ret[0].([]ConfigVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageConfigVersion indicates an expected call of PageConfigVersion.
func (mr *MockConfigManagerRepositoryMockRecorder) PageConfigVersion(ctx, query, total interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageConfigVersion", reflect.TypeOf((*MockConfigManagerRepository)(nil).PageConfigVersion), ctx, query, total)
}

// UpdateConfig mocks base method.
func (m *MockConfigManagerRepository) UpdateConfig(ctx context.Context, config *Config) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateConfig", ctx, config)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateConfig indicates an expected call of UpdateConfig.
func (mr *MockConfigManagerRepositoryMockRecorder) UpdateConfig(ctx, config interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfig", reflect.TypeOf((*MockConfigManagerRepository)(nil).UpdateConfig), ctx, config)
}

// UpdateConfigVersion mocks base method.
func (m *MockConfigManagerRepository) UpdateConfigVersion(ctx context.Context, version *ConfigVersion) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateConfigVersion", ctx, version)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateConfigVersion indicates an expected call of UpdateConfigVersion.
func (mr *MockConfigManagerRepositoryMockRecorder) UpdateConfigVersion(ctx, version interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfigVersion", reflect.TypeOf((*MockConfigManagerRepository)(nil).UpdateConfigVersion), ctx, version)
}
