// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.28.3
// source: resourcesync.proto

package constack

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ResourceSyncService_SyncUnifiedClusterSidecar_FullMethodName = "/quwan.cloud.constack.v1alpha.ResourceSyncService/SyncUnifiedClusterSidecar"
)

// ResourceSyncServiceClient is the client API for ResourceSyncService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ResourceSyncServiceClient interface {
	// sync unified cluster sidecar
	SyncUnifiedClusterSidecar(ctx context.Context, in *SyncUnifiedClusterSidecarReq, opts ...grpc.CallOption) (*SyncUnifiedClusterSidecarResp, error)
}

type resourceSyncServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewResourceSyncServiceClient(cc grpc.ClientConnInterface) ResourceSyncServiceClient {
	return &resourceSyncServiceClient{cc}
}

func (c *resourceSyncServiceClient) SyncUnifiedClusterSidecar(ctx context.Context, in *SyncUnifiedClusterSidecarReq, opts ...grpc.CallOption) (*SyncUnifiedClusterSidecarResp, error) {
	out := new(SyncUnifiedClusterSidecarResp)
	err := c.cc.Invoke(ctx, ResourceSyncService_SyncUnifiedClusterSidecar_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ResourceSyncServiceServer is the server API for ResourceSyncService service.
// All implementations must embed UnimplementedResourceSyncServiceServer
// for forward compatibility
type ResourceSyncServiceServer interface {
	// sync unified cluster sidecar
	SyncUnifiedClusterSidecar(context.Context, *SyncUnifiedClusterSidecarReq) (*SyncUnifiedClusterSidecarResp, error)
	mustEmbedUnimplementedResourceSyncServiceServer()
}

// UnimplementedResourceSyncServiceServer must be embedded to have forward compatible implementations.
type UnimplementedResourceSyncServiceServer struct {
}

func (UnimplementedResourceSyncServiceServer) SyncUnifiedClusterSidecar(context.Context, *SyncUnifiedClusterSidecarReq) (*SyncUnifiedClusterSidecarResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncUnifiedClusterSidecar not implemented")
}
func (UnimplementedResourceSyncServiceServer) mustEmbedUnimplementedResourceSyncServiceServer() {}

// UnsafeResourceSyncServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ResourceSyncServiceServer will
// result in compilation errors.
type UnsafeResourceSyncServiceServer interface {
	mustEmbedUnimplementedResourceSyncServiceServer()
}

func RegisterResourceSyncServiceServer(s grpc.ServiceRegistrar, srv ResourceSyncServiceServer) {
	s.RegisterService(&ResourceSyncService_ServiceDesc, srv)
}

func _ResourceSyncService_SyncUnifiedClusterSidecar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncUnifiedClusterSidecarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceSyncServiceServer).SyncUnifiedClusterSidecar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ResourceSyncService_SyncUnifiedClusterSidecar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceSyncServiceServer).SyncUnifiedClusterSidecar(ctx, req.(*SyncUnifiedClusterSidecarReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ResourceSyncService_ServiceDesc is the grpc.ServiceDesc for ResourceSyncService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ResourceSyncService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "quwan.cloud.constack.v1alpha.ResourceSyncService",
	HandlerType: (*ResourceSyncServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncUnifiedClusterSidecar",
			Handler:    _ResourceSyncService_SyncUnifiedClusterSidecar_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "resourcesync.proto",
}
