package upd_ser

import (
	"context"
	"fmt"

	"52tt.com/cicd/protocol/iam"
	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
)

type ScalePodReplicasArgs struct {
	ClusterName string `json:"clusterName" jsonschema:"required,description=集群名称"`
	Namespace   string `json:"namespace" jsonschema:"required,description=命名空间(NS)名称"`
	Name        string `json:"name" jsonschema:"required,description=要调整副本数量的工作负载名"`
	Replicas    *int32 `json:"replicas" jsonschema:"required,description=目标副本数，整数且≥0（StatefulSet建议≥1）"`
	Kind        string `json:"kind" jsonschema:"description=工作负载类型 默认为Deployment,enum=Deployment,enum=StatefulSet"`
	UserEmail   string `json:"userEmail" jsonschema:"description=用户邮箱,非必填"`
}

func (agg *updateAgg) ScalePod(ctx context.Context, req ScalePodReplicasArgs) (err error) {
	if req.ClusterName == "" || req.Namespace == "" || req.Name == "" || req.Replicas == nil {
		err = fmt.Errorf("clusterName, namespace, name, replicas must be set")
		return
	}

	switch req.Kind {
	case "Deployment", "deployments", "deployment":
		req.Kind = "Deployment"
	case "StatefulSet", "statefulsets", "statefulset":
		req.Kind = "StatefulSet"
	default:
		req.Kind = "Deployment"
	}

	userName := ""
	if req.UserEmail != "" {
		user, errIn := agg.userCli.GetUserByEmail(ctx, &iam.UserSearch{
			Email: req.UserEmail,
		})
		if errIn != nil {
			return errIn
		}
		if user == nil {
			err = fmt.Errorf("user not found")
			return
		}
		userName = user.Username
	}

	// 查询 k8s 资源
	_, err = agg.cloudagg.ScaleWorkloadReplicas(ctx, &constack.ScaleWorkloadReq{
		Cluster:   req.ClusterName,
		Namespace: req.Namespace,
		Name:      req.Name,
		Replicas:  *req.Replicas,
		Kind:      req.Kind,
		Username:  userName,
	})
	if err != nil {
		return
	}

	return
}
