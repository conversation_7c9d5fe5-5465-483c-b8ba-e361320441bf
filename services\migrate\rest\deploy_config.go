package rest

import (
	"52tt.com/cicd/services/migrate/internal/deploy_config/cpp"
	"52tt.com/cicd/services/migrate/internal/deploy_config/rcmd"
	"52tt.com/cicd/services/migrate/internal/deploy_config/sre"
	"52tt.com/cicd/services/migrate/internal/deploy_config/tciyuan"
	"52tt.com/cicd/services/migrate/internal/deploy_config/wefly"
	"52tt.com/cicd/services/migrate/internal/deploy_config/ywkf"
	"net/http"
	"strconv"
	"strings"

	"52tt.com/cicd/services/migrate/internal/deploy_config/common_server"
	"52tt.com/cicd/services/migrate/internal/deploy_config/qyxn"
	"52tt.com/cicd/services/migrate/internal/deploy_config/yyx"
	ztmingame "52tt.com/cicd/services/migrate/internal/deploy_config/ztminigame"

	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/para"
	migrate "52tt.com/cicd/services/migrate/internal/deploy_config"
	"52tt.com/cicd/services/migrate/internal/deploy_config/tt"
	"52tt.com/cicd/services/migrate/internal/deploy_config/ttchat"
	"52tt.com/cicd/services/migrate/internal/deploy_config/ttchatweb"
	"52tt.com/cicd/services/migrate/internal/deploy_config/zt"
	"github.com/gin-gonic/gin"
)

func DeployConfigRoute(route *gin.RouterGroup) {
	team := route.Group("/deploy_config")
	team.GET("/export", exportDeployConfig)
	team.GET("/pull", pullDeployConfig)
	team.POST("/ttchat/export", exportChatDeployConfig)
	team.POST("/tt/export", exportTTDeployConfig)
	team.GET("/zt/app/export", exportZTApp)
	team.POST("/tt/manual/export", manualExportTTDeployConfig)
	team.GET("/zt/export", exportZTDeployConfig)
	team.GET("/change-log/branch-refresh", branchRefresh)
	team.POST("/ttchat-web/export", exportChatWebDeployConfig)
	team.POST("/zt-minigame/export", exportZtMinigameDeployConfig)
	team.POST("/sync-app", syncApp)
	team.POST("/zt-minigame/del", deleteMiniGameConfig)
	team.GET("/yyx/export", exportYYXDeployConfig)
	team.GET("/yyx/service", updateYYXServiceName)
	team.POST("/common-server/export", exportCommonServerDeployConfig)
	team.POST("/qyxn/export", exportQYXNDeployConfig)
	team.POST("/repair/tt/hpa", repairTTHPA)
	team.GET("/check/tt/Sidecar", checkSidecar)
	team.GET("/repair/online/hpa", repairOnlineHPA)
	team.GET("/clean/pipeline-group/app", cleanAppIds)
	team.GET("/tt/hpa/mutile", ttHpaMutle)
	team.POST("/sre/export", exportSREDeployConfig)
	team.POST("/tciyuan/export", exportTCiYuanDeployConfig)
	team.POST("/wefly/export", exportWeflyDeployConfig)
	team.POST("/ywkf/export", exportYWKFDeployConfig)
	team.POST("/cpp/export", exportCppDeployConfig)
	team.POST("/zttw/export", exportZTTWDeployConfig)
	team.POST("/ztypsf/export", exportZTYPSFDeployConfig)
	team.POST("/zttj/export", exportZTTJDeployConfig)
	team.POST("/ztsf/export", exportZTSFDeployConfig)
}

func exportDeployConfig(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "导出部署配置信息")
	env := ctx.Query("env")
	units := ctx.Query("units")
	isUpdate := ctx.Query("isUpdate")
	envs := strings.Split(env, " ")
	err := migrate.ExportAllConfig(envs, units, isUpdate)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func pullDeployConfig(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "导入部署配置信息")
	//err := migrate.PullAllConfig()
	//if err != nil {
	//  ctxWrapper.FailError(http.StatusInternalServerError, err)
	//  return
	//}
	ctxWrapper.Ok()

}

func exportChatDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出海外线部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := ttchat.ExportAllConfig(params.Envs, params.Services)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

type exportParams struct {
	Services          []string `json:"services" validate:"required"`
	Envs              []string `json:"envs" validate:"required"`
	TargetProjectName string   `json:"targetProjectName"`
	IsUpdate          bool     `json:"isUpdate"`
	IsCover           bool     `json:"isCover"`
}

func exportTTDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出TT线部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)
	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := tt.ExportAllConfig(params.Envs, params.Services, params.IsUpdate, params.TargetProjectName)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportTCiYuanDeployConfig(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "导出T次元部署配置信息")
	env := ctx.Query("env")
	units := ctx.Query("units")
	envs := strings.Split(env, " ")
	isUpdate := ctx.Query("isUpdate")
	err := tciyuan.ExportAllConfig(envs, units, isUpdate)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportSREDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出sre部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := sre.ExportAllConfig(params.Envs, params.Services, params.IsUpdate, params.TargetProjectName)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportZTApp(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "导出中台app信息")
	unitInfo := ctx.Query("unitInfo")
	//envs := strings.Split(env, " ")
	err := zt.SyncApp(unitInfo)
	//err := zt.ExportAllConfig(envs)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func manualExportTTDeployConfig(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "手动指定导出TT线部署配置信息")
	var parameter migrate.MigrationParameter
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)
	if err := ctxWrapper.Bind(&parameter); err != nil {
		return
	}
	if err := tt.ManualExportDeployConfig(&parameter); err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportZTDeployConfig(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "导出中台部署配置信息")
	//env := ctx.Query("env")
	unitInfo := ctx.Query("unitInfo")
	//envs := strings.Split(env, " ")
	err := zt.SyncApp(unitInfo)
	//err := zt.ExportAllConfig(envs)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func branchRefresh(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "变更记录构建分支刷新")
	env := ctx.Query("env")
	err := migrate.BranchRefresh(env)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportChatWebDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出海外线前端组部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := ttchatweb.ExportAllConfig(params.Envs, params.Services)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportZtMinigameDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出中台小游戏组部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := ztmingame.ExportAllConfig(params.Envs, params.Services)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func syncApp(ctx *gin.Context) {
	var params migrate.SyncAppParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出应用下服务信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出服务信息，参数有误")
		return
	}
	err := migrate.SyncApp(params)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func deleteMiniGameConfig(ctx *gin.Context) {
	type deleteParams struct {
		Env        string   `json:"env" validate:"required,oneof=dev test prod"`
		Clusters   []string `json:"clusters" validate:"required,gte=1"`
		Namespaces []string `json:"namespaces" validate:"required,gte=1"`
	}
	var params deleteParams
	ctxWrapper := para.WrapperContext(ctx, true, "删除部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("删除部署配置失败，参数有误")
		return
	}
	err := migrate.DeleteMiniGameDeployConfig(params.Env, params.Clusters, params.Namespaces)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func updateYYXServiceName(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "导出应用线线部署配置信息")
	err := yyx.UpdateService()
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportYYXDeployConfig(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "导出应用线线部署配置信息")
	env := ctx.Query("env")
	envs := strings.Split(env, " ")
	isUpdate := ctx.Query("isUpdate")
	err := yyx.ExportAllConfig(envs, isUpdate)
	//err := deploy_config.DeleteDeployConfig("prod")
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportCommonServerDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出质量平台组部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := common_server.ExportAllConfig(params.Envs, params.Services, params.TargetProjectName, params.IsUpdate)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportQYXNDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出企业效能部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := qyxn.ExportAllConfig(params.Envs, params.Services, params.IsUpdate, params.TargetProjectName)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func repairTTHPA(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "修复TT线HPA问题")
	units := ctx.Query("units")
	isUpdate := ctx.Query("isUpdate")
	err := tt.RepairTTHPA(isUpdate, units)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func repairOnlineHPA(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "修复TT线线上HPA问题")
	units := ctx.Query("units")
	isUpdate := ctx.Query("isUpdate")
	projectId := ctx.Query("projectId")
	id, err := strconv.Atoi(projectId)
	err = tt.RepairOnlineHPA(isUpdate, units, int64(id))
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func checkSidecar(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "检查Sidecar的单位")
	err := tt.CheckSidecar()
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}
func cleanAppIds(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "清除批量流水线的appids")
	appIds := ctx.Query("appIds")
	isUpdate := ctx.Query("isUpdate")
	err := tt.CleanAppIds(appIds, isUpdate)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func ttHpaMutle(ctx *gin.Context) {
	ctxWrapper := para.WrapperContext(ctx, true, "将原生HPA改成多维度HPA")
	units := ctx.Query("units")
	isUpdate := ctx.Query("isUpdate")
	err := tt.TTHpaMutle(units, isUpdate)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportWeflyDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出应用线-起飞部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := wefly.ExportAllConfig(params.Envs, params.Services, params.IsUpdate)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportYWKFDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出运维开发部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := ywkf.ExportAllConfig(params.Envs, params.Services, params.TargetProjectName, params.IsUpdate)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportCppDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出CPP部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := cpp.ExportAllConfig(params.Envs, params.Services, params.IsUpdate, params.TargetProjectName)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportZTTWDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出中台T网部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := rcmd.ExportZTTWAllConfig(params.Envs, params.Services, params.TargetProjectName, params.IsUpdate, params.IsCover)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportZTYPSFDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出中台音频算法组部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := rcmd.ExportZTYPSFAllConfig(params.Envs, params.Services, params.TargetProjectName, params.IsUpdate, params.IsCover)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportZTTJDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出中台推荐部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := rcmd.ExportZTTJAllConfig(params.Envs, params.Services, params.TargetProjectName, params.IsUpdate, params.IsCover)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}

func exportZTSFDeployConfig(ctx *gin.Context) {
	var params exportParams
	ctxWrapper := para.WrapperContext(ctx, true, "导出中台算法部署配置信息")
	ctxWrapper.RegisterBind(
		ctx.ShouldBindJSON,
	)

	if err := ctxWrapper.Bind(&params); err != nil {
		log.Errorf("导出部署配置失败，参数有误")
		return
	}
	err := rcmd.ExportZTSFAllConfig(params.Envs, params.Services, params.TargetProjectName, params.IsUpdate, params.IsCover)
	if err != nil {
		ctxWrapper.FailError(http.StatusInternalServerError, err)
		return
	}
	ctxWrapper.Ok()
}
