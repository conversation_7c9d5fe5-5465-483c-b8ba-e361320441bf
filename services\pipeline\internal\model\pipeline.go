package model

import (
	"encoding/json"
	"time"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/page"
)

type TaskCommands struct {
	Commands []Command `json:"commands"`
}

type Command struct {
	Language   constants.Language `json:"language"`
	UnitTest   string             `json:"unitTest"`
	Checkstyle string             `json:"checkstyle"`
	BuildApp   string             `json:"buildApp"`
}

type Pipeline struct {
	ID                  int64                  `json:"id" uri:"id"`
	Name                string                 `json:"name" validate:"required"` // 流水线名
	Type                string                 `json:"type" validate:"required,oneof=feature release hotfix pre_merge integration"`
	Language            string                 `json:"language"`                  // 语言类型
	RepoAddr            string                 `json:"repoAddr"`                  // 仓库地址
	BuildPath           string                 `json:"buildPath"`                 // 构建路径
	AppId               int64                  `json:"appId" validate:"required"` // 应用id
	AppName             string                 `json:"appName"`
	TriggerType         string                 `json:"triggerType"`
	TriggerByChange     int                    `json:"triggerByChange"`
	TemplateId          int64                  `json:"templateId" validate:"required"`                         // 模版id
	TemplateName        string                 `json:"templateName"`                                           // 模板名称
	TargetBranchType    string                 `json:"targetBranchType" validate:"required,oneof=exact regex"` // 目标分支类型
	TargetBranchContent string                 `json:"targetBranchContent" validate:"required"`                // 目标分支内容
	Config              map[string]interface{} `json:"config" mapstructure:"-"`                                //流水线配置
	PipelineGroupID     *int64                 `json:"pipelineGroupId"`                                        // 流水线组ID
	IsAutoMerge         int                    `json:"isAutoMerge"`                                            // 是否自动合并分支
	IsDeleted           int                    `json:"isDelete"`                                               // 是否删除
	ProjectID           int64                  `json:"projectId"`                                              // 关联项目ID
}

type Config map[string]any

func (c *Config) MarshalConfig() ([]byte, error) {
	return json.Marshal(c)
}

// PipelineConfig 是 dao.Pipeline.config 的 Unmarshal
type PipelineConfig map[int64]Config

type UpdatePipelineCfgReq struct {
	ID     int64 `json:"id" uri:"id"`
	Config PipelineConfig
}

type PipelineStateConfig struct {
	ID     int64          `json:"id" uri:"id"` //流水线id
	Config PipelineConfig `json:"config"`      //流水线配置
}

type PipelineParam struct {
	ID int64 `uri:"id"`
}

type PipelineDelParam struct {
	AppId *int64 `uri:"appId"`
	PrjId *int64 `uri:"projectId"`
}

type PipelineQuery struct {
	page.PageModel
	ProjectId int64  `form:"projectId" json:"projectId" validate:"required"`
	AppId     int64  `form:"appId" json:"appId"`
	Name      string `form:"name" json:"name"`
	//status of pipeline
	Status constants.PipelineStatus `form:"status" json:"status"`
	//appName of pipeline
	AppName string `form:"appName" json:"appName"`
	//type of pipeline
	Type constants.PipelineType `form:"type" json:"type"`
	//triggered by user
	TriggerBy  int64 `form:"triggerBy" json:"triggerBy"`
	TemplateId int64 `form:"templateId" json:"templateId"`
}

type AppInPipeline struct {
	Name      string `json:"name,omitempty" validate:"required"`
	LangName  string `json:"langName"`
	BuildPath string `json:"buildPath"`
	RepoAddr  string `json:"repoAddr,omitempty" validate:"required"`
}

type AppBasicInfo struct {
	ID          int64
	Name        string
	BuildPath   string
	RepoAddr    string
	LangName    string
	LangVersion string
}

type StageRunInfo struct {
	Name   string                   `json:"name"`
	Status constants.PipelineStatus `json:"status"`
}

type LatestRunInfo struct {
	PipelineRunId      int64                    `json:"pipelineRunId"`
	Status             constants.PipelineStatus `json:"status"`
	SourceBranch       string                   `json:"sourceBranch"`
	Branch             string                   `json:"branch"`
	StartedTime        *time.Time               `json:"startedTime"`
	CompletedTime      *time.Time               `json:"completedTime"`
	Stages             []StageRunInfo           `json:"stages"`
	TriggerUserId      int64                    `json:"triggerUserId"`
	TriggerChineseName string                   `json:"triggerChineseName"`
	TriggerEmployeeNo  string                   `json:"triggerEmployeeNo"`
}

type PipelineListItem struct {
	Pipeline
	LatestRunInfo *LatestRunInfo `json:"latestRunInfo"`
}

type PipelineRunArgs struct {
	SourceBranch         string                 `json:"sourceBranch"`
	Branch               string                 `json:"branch"`
	TriggerBy            int64                  `json:"triggerBy"`
	TriggerByChineseName string                 `json:"triggerByChineseName"`
	TriggerByEmployeeNo  string                 `json:"triggerByEmployeeNo"`
	Args                 map[string]string      `json:"args"`
	IsRetry              bool                   `json:"isRetry"`
	RequestDate          time.Time              `json:"requestDate"`
	GitlabEventType      string                 `json:"gitlabEventType"`
	GitlabEvents         map[string]interface{} `json:"gitlabEvents"`
	Iid                  int64                  `json:"iid"`
	Description          string                 `json:"description"`
}

func (arg PipelineRunArgs) ManualRunValidate() bool {
	if arg.Branch != "" && arg.TriggerBy != 0 && arg.TriggerByChineseName != "" && arg.TriggerByEmployeeNo != "" {
		return true
	}
	return false
}

func (arg PipelineRunArgs) ValidateBeforeRun() bool {
	// TODO: check which arg is required before execute run pipeline
	return arg.ManualRunValidate()
}

type ManualRunPipelineReq struct {
	ID           int64  `json:"id" uri:"id"`                      //流水线id
	TargetBranch string `json:"targetBranch" validate:"required"` //目标分支
	Description  string `json:"description"`                      //描述
}

type UpdatePipelineConfigParams struct {
	PipelineRunId         int64  `json:"pipelineRunId"`
	TaskRunId             int64  `json:"taskRunId"`
	UpdatedTaskRunConfig  []byte `json:"updatedTaskRunConfig"`
	TaskId                int64  `json:"taskId"`
	UpdatedPipelineConfig []byte `json:"updatedPipelineConfig"`
}

type PlAppCountResult struct {
	AppId int64 `json:"appId"`
	Count int64 `json:"count"`
}

type DeletePipelineQuery struct {
	PipelineGroupID int64
	AppIds          []int64
	TemplateId      int64
}
